# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.address_list.param.AddressListParam import AddressListParam


class AddressListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AddressListParam()

	def getUrlPath(self, ):
		return "/address/list"

	def getParams(self, ):
		return self.params



