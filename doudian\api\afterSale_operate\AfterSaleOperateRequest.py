# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.afterSale_operate.param.AfterSaleOperateParam import AfterSaleOperateParam


class AfterSaleOperateRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AfterSaleOperateParam()

	def getUrlPath(self, ):
		return "/afterSale/operate"

	def getParams(self, ):
		return self.params



