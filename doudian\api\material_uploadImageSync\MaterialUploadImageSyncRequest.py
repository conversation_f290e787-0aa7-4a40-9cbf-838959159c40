# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_uploadImageSync.param.MaterialUploadImageSyncParam import MaterialUploadImageSyncParam


class MaterialUploadImageSyncRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialUploadImageSyncParam()

	def getUrlPath(self, ):
		return "/material/uploadImageSync"

	def getParams(self, ):
		return self.params



