from datetime import datetime, timedelta, timezone
import hashlib
import json
import re
import urllib.parse
import aiohttp

api_key = "35004015"  # 奇门 appkey
api_secret = "59f6b87d8e106aad93566e2705479e25"  # 奇门 appsecret
endpoint = "https://tp8z6548i2.api.taobao.com/router/qm"


class JsonEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime("%Y-%m-%d %H:%M:%S")
        return super().default(obj)


def generate_guanyi_sign(params: dict, secret: str) -> str:
    """
    生成管易签名
    """
    # 将参数转为标准 JSON 字符串(不包含 sign 字段)
    params_json = json.dumps(params, separators=(",", ":"), ensure_ascii=False)
    # 在字符串首尾加上 secret
    sign_str = f"{secret}{params_json}{secret}"
    # MD5 加密并转为大写
    return hashlib.md5(sign_str.encode()).hexdigest().upper()


# 计算 sign
def generate_sign(params: dict, secret: str) -> str:
    """
    生成签名
    """
    # 按参数名称升序排序
    sorted_params = dict(sorted(params.items()))
    # 构造签名字符串
    sign_str = secret
    for k, v in sorted_params.items():
        if v is not None and v != "":
            sign_str += f"{k}{v}"
    sign_str += secret
    # MD5加密
    return hashlib.md5(sign_str.encode()).hexdigest().upper()


number_pattern = re.compile(r"^-?\d+\.\d+$")
datetime_pattern = re.compile(r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$")
# 确保不超过 int 最大位数（8字节整数的最大值约为 2^63-1，即19位数）
int_pattern = re.compile(r"^-?(?:\d{1,18}|9(?:(?:22337203)(?:6854775807)?))$")
IGNORE_FORMAT_FIELDS = [
    "platform_code",
    "express_code",
    "mail_no",
    "shop_code",
    "express_no",
    "item_id",
    "barcode",
]


def _format_data(data: dict) -> dict:
    """
    格式化数据

    递归处理

    字符串 true/false 转换为 bool

    整数串转为 int

    小数串转为 float
    """
    for k, v in data.items():
        if k in IGNORE_FORMAT_FIELDS:
            continue
        if isinstance(v, str):
            if v == "true":
                data[k] = True
            elif v == "false":
                data[k] = False
            elif int_pattern.match(v):
                data[k] = int(v)
            elif number_pattern.match(v):
                data[k] = float(v)
            elif datetime_pattern.match(v):
                t = datetime.strptime(v, "%Y-%m-%d %H:%M:%S")
                # 东八区
                t = t.replace(tzinfo=timezone(timedelta(hours=8)))
                data[k] = t
        elif isinstance(v, dict):
            data[k] = _format_data(v)
        elif isinstance(v, list):
            data[k] = [_format_data(item) for item in v]
    return data


def _get_base_params(type: str = "qimen"):
    if type == "qimen":
        return {
            "app_key": api_key,
            "sign_method": "md5",
            "v": "2.0",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "format": "json",
            "target_app_key": "21151296",  # 管易 奇门 appkey
            "sessionkey": "c1bfd8a7d6214eeead1a50ccee7282d6",  # 管易 sessionkey
        }

    return {
        "appkey": "152235",
        "sessionkey": "c1bfd8a7d6214eeead1a50ccee7282d6",
    }


async def _do_request_without_qimen(params: dict) -> dict | None:
    """
    发送请求
    """
    params = {
        **_get_base_params(type="guanyi"),
        **params,
    }
    params["sign"] = generate_guanyi_sign(params, "b2d3719541454df48a1d7e6a07e983d4")
    endpoint = "http://api.guanyierp.com/rest/erp_open"
    body = json.dumps(params, ensure_ascii=False)
    body = urllib.parse.quote(body, safe="")
    async with aiohttp.ClientSession() as session:
        async with session.post(
            endpoint,
            data=body,
            headers={"Content-Type": "application/json"},
        ) as response:
            text = await response.text()
            result = json.loads(text)
            _format_data(result)
            return result


async def _do_request(params: dict) -> dict | None:
    """
    发送请求
    """
    params = {
        **_get_base_params(),
        **params,
    }
    params["sign"] = generate_sign(params, api_secret)
    async with aiohttp.ClientSession() as session:
        async with session.get(
            endpoint,
            params=params,
            headers={
                "Accept-Encoding": "gzip",
            },
        ) as response:
            text = await response.text()
            resp = json.loads(text)
            _format_data(resp)
            result = resp.get("response", {})
            success = result.get("success", False)
            if not success:
                print(f"奇门请求失败: {resp}")
                print(f"请求地址: {response.request_info.url}")
                return None
            return result


async def tradeDetailGet(code: str) -> dict | None:
    """
    通过奇门获取单个管易订单详情
    """
    params = {
        "method": "gy.erp.trade.detail.get",
        "code": code,
    }
    result = await _do_request(params)
    if not result:
        return None
    orderDetail: dict = result.get("orderDetail", {})
    extend_info: str = orderDetail.get("extend_info", "")
    if extend_info and extend_info != "":
        orderDetail["extend_info"] = json.loads(extend_info)
    details: list[dict] = orderDetail.get("details", [])
    platform_type: str = orderDetail.get("platform_code", "")
    is_xhs = platform_type.startswith("P")
    for detail in details:
        # 如果是小红书订单，则需要 SKU 从 platform_item_name 里提取
        if is_xhs:
            sku_name = detail.get("platform_item_name", "")
        else:
            sku_name = detail.get("platform_sku_name", "")
        parts = sku_name.split("||")
        if len(parts) > 1:
            sku = parts[0]
            detail["platform_sku"] = sku
    return orderDetail


async def tradeGet(query: dict | None = None) -> tuple[list[dict], int] | None:
    """
    通过奇门获取单个管易订单
    """
    params = {
        "method": "gy.erp.trade.get",
        **(query or {}),
    }
    result = await _do_request(params)
    if not result:
        return None
    total = result.get("total", 0)
    orders = result.get("orders", [])
    return orders, total


async def tradeHistoryGet(query: dict | None = None) -> tuple[list[dict], int] | None:
    """
    通过奇门获取管易订单历史
    """
    params = {
        "method": "gy.erp.trade.history.get",
        **(query or {}),
    }
    result = await _do_request(params)
    if not result:
        return None
    orders = result.get("orders", [])
    total = result.get("total", 0)
    return orders, total


# gy.erp.trade.history.detail.get


async def tradeHistoryDetailGet(code: str) -> dict | None:
    """
    通过奇门获取管易订单历史详情
    """
    params = {
        "method": "gy.erp.trade.history.detail.get",
        "code": code,
    }
    result = await _do_request(params)
    if not result:
        return None
    return result


async def tradeDeliverysGet(query: dict | None = None) -> tuple[list[dict], int] | None:
    """
    通过奇门获取管易订单发货记录
    """
    params = {
        "method": "gy.erp.trade.deliverys.get",
        **(query or {}),
    }
    result = await _do_request(params)
    if not result:
        return None
    return result


async def tradeDeliverysHistoryGet(
    query: dict | None = None,
) -> tuple[list[dict], int] | None:
    """
    通过奇门获取管易订单历史发货记录
    """
    params = {
        "method": "gy.erp.trade.deliverys.history.get",
        **(query or {}),
    }
    result = await _do_request(params)
    if not result:
        return None
    return result


async def tradeDeliverysDetailGet(
    query: dict | None = None,
) -> tuple[list[dict], int] | None:
    """
    通过奇门获取管易订单发货记录
    """
    params = {
        "method": "gy.erp.trade.deliverys.detail.get",
        **(query or {}),
    }
    result = await _do_request_without_qimen(params)
    if not result:
        return None
    return result


async def tradeDeliverysDetailHistoryGet(
    query: dict | None = None,
) -> tuple[list[dict], int] | None:
    """
    通过奇门获取管易订单发货记录历史
    """
    params = {
        "method": "gy.erp.trade.deliverys.detail.history.get",
        **(query or {}),
    }
    result = await _do_request_without_qimen(params)
    if not result:
        return None
    return result
