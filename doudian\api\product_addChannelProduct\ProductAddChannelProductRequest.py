# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_addChannelProduct.param.ProductAddChannelProductParam import ProductAddChannelProductParam


class ProductAddChannelProductRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductAddChannelProductParam()

	def getUrlPath(self, ):
		return "/product/addChannelProduct"

	def getParams(self, ):
		return self.params



