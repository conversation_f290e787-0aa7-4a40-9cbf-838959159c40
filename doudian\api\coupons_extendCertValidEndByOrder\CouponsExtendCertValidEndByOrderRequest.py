# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.coupons_extendCertValidEndByOrder.param.CouponsExtendCertValidEndByOrderParam import CouponsExtendCertValidEndByOrderParam


class CouponsExtendCertValidEndByOrderRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = CouponsExtendCertValidEndByOrderParam()

	def getUrlPath(self, ):
		return "/coupons/extendCertValidEndByOrder"

	def getParams(self, ):
		return self.params



