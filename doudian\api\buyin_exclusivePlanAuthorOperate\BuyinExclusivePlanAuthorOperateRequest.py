# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.buyin_exclusivePlanAuthorOperate.param.BuyinExclusivePlanAuthorOperateParam import BuyinExclusivePlanAuthorOperateParam


class BuyinExclusivePlanAuthorOperateRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = BuyinExclusivePlanAuthorOperateParam()

	def getUrlPath(self, ):
		return "/buyin/exclusivePlanAuthorOperate"

	def getParams(self, ):
		return self.params



