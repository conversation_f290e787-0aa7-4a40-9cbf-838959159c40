# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.buyin_channelBindAuthors.param.BuyinChannelBindAuthorsParam import BuyinChannelBindAuthorsParam


class BuyinChannelBindAuthorsRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = BuyinChannelBindAuthorsParam()

	def getUrlPath(self, ):
		return "/buyin/channelBindAuthors"

	def getParams(self, ):
		return self.params



