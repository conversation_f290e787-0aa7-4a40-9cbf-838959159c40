# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_listShopNetsite.param.LogisticsListShopNetsiteParam import LogisticsListShopNetsiteParam


class LogisticsListShopNetsiteRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsListShopNetsiteParam()

	def getUrlPath(self, ):
		return "/logistics/listShopNetsite"

	def getParams(self, ):
		return self.params



