from datetime import datetime, timedelta
import time
from sqlalchemy import delete, func, select, update, insert
from sqlalchemy.dialects.postgresql import insert as pg_insert
from app.core.oa_database import (
    GuanyierpGoods,
    PddMall,
    get_session,
    GuanyierpPurchaseArrive,
    GuanyierpPurchaseArriveDetail,
    GuanyierpStockOtherInOrder,
    GuanyierpStockOtherInOrderDetail,
    ProductionBatch,
    ProductionBatchAllocateRecord,
    ProductOrderSalesItemShopSum,
)
import typing
import re

GenerateResult = typing.TypedDict(
    "GenerateResult",
    {
        "update_count": int,
        "insert_count": int,
        "total_count": int,
    },
)
log_func = print


def write_log(msg):
    log_func(msg)


# 只允许大小写字母、数字、斜杆
re_batch_no = re.compile(r"^[a-zA-Z0-9/]+$")


async def generate() -> GenerateResult:
    # 取 60 天内的数据
    d = datetime.now() - timedelta(days=60)
    t = int(d.timestamp())
    async with get_session() as session:
        query = (
            select(
                GuanyierpStockOtherInOrderDetail.item_code,
                GuanyierpStockOtherInOrderDetail.memo_batch,
                func.sum(GuanyierpStockOtherInOrderDetail.instore_qty).label("qty"),
            )
            .join(
                GuanyierpStockOtherInOrder,
                GuanyierpStockOtherInOrder.code
                == GuanyierpStockOtherInOrderDetail.guanyierp_stock_other_in_order_code,
            )
            .where(
                GuanyierpStockOtherInOrder.create_time > t,
                GuanyierpStockOtherInOrderDetail.guanyierp_stock_other_in_order_code.isnot(
                    None
                ),
                GuanyierpStockOtherInOrderDetail.memo_oacode != "",
                GuanyierpStockOtherInOrderDetail.memo_batch != "",
            )
            .group_by(
                GuanyierpStockOtherInOrderDetail.item_code,
                GuanyierpStockOtherInOrderDetail.memo_batch,
            )
        )
        other_in_order = await session.execute(query)
        other_in_order = other_in_order.mappings().all()
        query = (
            select(
                GuanyierpPurchaseArriveDetail.item_code,
                GuanyierpPurchaseArriveDetail.note_batch,
                func.sum(GuanyierpPurchaseArriveDetail.qty).label("qty"),
            )
            .join(
                GuanyierpPurchaseArrive,
                GuanyierpPurchaseArrive.code
                == GuanyierpPurchaseArriveDetail.guanyierp_purchase_arrive_code,
            )
            .where(
                GuanyierpPurchaseArrive.create_date > d,
                GuanyierpPurchaseArriveDetail.guanyierp_purchase_arrive_code.isnot(
                    None
                ),
                GuanyierpPurchaseArriveDetail.note_batch != "",
            )
            .group_by(
                GuanyierpPurchaseArriveDetail.item_code,
                GuanyierpPurchaseArriveDetail.note_batch,
            )
        )
        purchase_arrive = await session.execute(query)
        purchase_arrive = purchase_arrive.mappings().all()
        res = {}

        # 合并数据
        for x in other_in_order:
            key = (x["item_code"], x["memo_batch"])
            res[key] = {
                "item_code": x["item_code"],
                "batch_no": x["memo_batch"],
                "qty": x["qty"],
            }
        for x in purchase_arrive:
            key = (x["item_code"], x["note_batch"])
            if key not in res:
                res[key] = {
                    "item_code": x["item_code"],
                    "batch_no": x["note_batch"],
                    "qty": x["qty"],
                }
            else:
                res[key]["qty"] += x["qty"]
        res = list(res.values())
        # 获取相应产品的名称
        item_codes = list(set(x["item_code"] for x in res))
        query = select(GuanyierpGoods.code, GuanyierpGoods.name).where(
            GuanyierpGoods.code.in_(item_codes)
        )
        result = await session.execute(query)
        item_names = {x["code"]: x["name"] for x in result.mappings().all()}
        # 批量查询现有记录
        existing_records = {}
        query = select(ProductionBatch).where(
            ProductionBatch.item_code.in_([x["item_code"] for x in res]),
            ProductionBatch.batch_no.in_([x["batch_no"] for x in res]),
        )
        result = await session.execute(query)
        for record in result.scalars().all():
            existing_records[(record.item_code, record.batch_no)] = record

        to_update = []
        to_insert = []
        unchange_count = 0

        # 在内存中分类处理
        for x in res:
            if x["batch_no"].startswith("OA"):
                continue
            if not re_batch_no.match(x["batch_no"]):
                continue
            key = (x["item_code"], x["batch_no"])
            if key in existing_records:
                existing = existing_records[key]
                if existing.qty != x["qty"] or existing.item_name != item_names.get(
                    x["item_code"], ""
                ):
                    to_update.append(
                        {
                            "id": existing.id,
                            "qty": x["qty"],
                            "item_name": item_names.get(x["item_code"], ""),
                            "update_time": int(time.time()),
                        }
                    )
                else:
                    unchange_count += 1
            else:
                to_insert.append(
                    {
                        "item_code": x["item_code"],
                        "batch_no": x["batch_no"],
                        "qty": x["qty"],
                        "item_name": item_names.get(x["item_code"], ""),
                        "create_time": int(time.time()),
                        "update_time": int(time.time()),
                    }
                )

        # 批量更新
        if to_update:
            await session.execute(update(ProductionBatch), to_update)

        # 批量插入
        if to_insert:
            await session.execute(insert(ProductionBatch), to_insert)

        await session.commit()
        return {
            "update_count": len(to_update),
            "insert_count": len(to_insert),
            "unchange_count": unchange_count,
            "total_count": len(to_update) + len(to_insert) + unchange_count,
        }


# 分配到店铺
async def allocate_to_delivery(sales_item_shop_sum_ids: list[int] = None):
    d = datetime.now().astimezone() - timedelta(days=30)
    min_date = datetime.fromisoformat("2024-12-01T00:00:00+08:00")
    if sales_item_shop_sum_ids:
        d = min_date
    elif d < min_date:
        d = min_date
    t = int(d.timestamp())
    write_log(
        f"开始分配, 时间范围: {d.strftime('%Y-%m-%d')} - {datetime.now().strftime('%Y-%m-%d')}"
    )
    async with get_session() as session:
        # 获取销售数据
        allocated_qty_subquery = (
            select(
                ProductionBatchAllocateRecord.order_sales_item_shop_sum_id,
                func.coalesce(
                    func.sum(ProductionBatchAllocateRecord.allocated_qty), 0
                ).label("total_allocated_qty"),
            )
            .group_by(ProductionBatchAllocateRecord.order_sales_item_shop_sum_id)
            .subquery()
        )

        sales_query = (
            select(
                ProductOrderSalesItemShopSum.id,
                ProductOrderSalesItemShopSum.item_code,
                ProductOrderSalesItemShopSum.sales_number,
                ProductOrderSalesItemShopSum.shop_code,
                ProductOrderSalesItemShopSum.shop_name,
                ProductOrderSalesItemShopSum.date_time,
                ProductOrderSalesItemShopSum.platform_code,
                ProductOrderSalesItemShopSum.platform_name,
                func.coalesce(allocated_qty_subquery.c.total_allocated_qty, 0).label(
                    "allocated_qty"
                ),
            )
            .outerjoin(
                allocated_qty_subquery,
                ProductOrderSalesItemShopSum.id
                == allocated_qty_subquery.c.order_sales_item_shop_sum_id,
            )
            .where(
                ProductOrderSalesItemShopSum.sales_number
                > func.coalesce(allocated_qty_subquery.c.total_allocated_qty, 0),
                ProductOrderSalesItemShopSum.date_time >= t,
            )
        )
        if sales_item_shop_sum_ids:
            sales_query = sales_query.where(
                ProductOrderSalesItemShopSum.id.in_(sales_item_shop_sum_ids)
            )
        sales_result = await session.execute(sales_query)
        sales_result = sales_result.mappings().all()
        if not sales_result:
            write_log("没有需要分配的数据")
            return
        # 获取相关的产品编号
        item_codes = list(set(x["item_code"] for x in sales_result))
        # 未分配的数量
        unallocated_qty = sum(
            x["sales_number"] - x["allocated_qty"] for x in sales_result
        )
        write_log(
            f"获取销售数据成功, 数量: {len(sales_result)}, 产品数量: {len(item_codes)}, 未分配数量: {unallocated_qty}"
        )
        write_log(f"产品代码未分配数量明细:")
        for code in item_codes:
            qty = sum(
                x["sales_number"] - x["allocated_qty"]
                for x in sales_result
                if x["item_code"] == code
            )
            write_log(f"【{code}】: {qty}")

        # 批量获取所有相关批次
        batch_query = select(ProductionBatch).where(
            ProductionBatch.item_code.in_(item_codes),
            ProductionBatch.allocated_qty < ProductionBatch.qty,
        )
        batch_result = await session.execute(batch_query)
        all_batches = list(batch_result.scalars().all())
        write_log(
            f"获取可用批次数据成功, 数量: {len(all_batches)}, 总库存: {sum(x.qty for x in all_batches)}"
        )
        if not all_batches:
            write_log("没有需要分配的批次")
            return
        # 按产品代码分组批次
        batches_by_item = {}
        for batch in all_batches:
            if batch.item_code not in batches_by_item:
                batches_by_item[batch.item_code] = []
            batches_by_item[batch.item_code].append(batch)

        # 对每个产品的批次进行排序
        for item_batches in batches_by_item.values():
            item_batches.sort(key=lambda x: (x.batch_no))

        # 已存在的分配记录
        sales_ids = [x["id"] for x in sales_result]
        if sales_ids:
            sales_allocated_records = await session.execute(
                select(
                    ProductionBatchAllocateRecord.id,
                    ProductionBatchAllocateRecord.order_sales_item_shop_sum_id,
                    ProductionBatchAllocateRecord.allocated_qty,
                ).where(
                    ProductionBatchAllocateRecord.order_sales_item_shop_sum_id.in_(
                        sales_ids
                    )
                )
            )
            sales_allocated_records = sales_allocated_records.mappings().all()
            sales_allocated_records = {
                x["order_sales_item_shop_sum_id"]: {
                    "id": x["id"],
                    "allocated_qty": x["allocated_qty"],
                }
                for x in sales_allocated_records
            }
        # 获取相关店铺名称
        shop_codes = list(set(x["shop_code"] for x in sales_result))
        query = select(PddMall.gyCode, PddMall.mallName).where(
            PddMall.gyCode.in_(shop_codes)
        )
        result = await session.execute(query)
        # shop_names = {x["gyCode"]: x["mallName"] for x in result.mappings().all()}
        # 准备批量更新和插入的数据
        batch_updates_dict = {}  # 使用字典避免重复更新
        allocation_records = {}  # 使用字典按batch_no和item_code合并记录

        for sale in sales_result:
            item_code = sale["item_code"]
            sales_number = sale["sales_number"]
            allocated_qty = sale["allocated_qty"]
            remaining_qty = sales_number - allocated_qty
            is_allocated = sale["id"] in sales_allocated_records
            if is_allocated:
                remaining_qty -= sales_allocated_records[sale["id"]]["allocated_qty"]
            if item_code not in batches_by_item:
                continue

            for batch in batches_by_item[item_code]:
                if batch.qty <= batch.allocated_qty:
                    continue
                # 使用当前批次在字典中的已分配数量（如果有的话）
                current_allocated_qty = batch_updates_dict.get(batch.id, {}).get(
                    "allocated_qty", batch.allocated_qty
                )
                available_qty = batch.qty - current_allocated_qty

                if available_qty <= 0:
                    continue

                allocation = min(available_qty, remaining_qty)
                if allocation <= 0:
                    break

                new_allocated_qty = current_allocated_qty + allocation
                batch_updates_dict[batch.id] = {
                    "id": batch.id,
                    "allocated_qty": new_allocated_qty,
                }

                # 按batch_no和item_code合并记录
                allocation_key = (batch.batch_no, item_code, sale["id"])
                if allocation_key in allocation_records:
                    allocation_records[allocation_key].allocated_qty += allocation
                else:
                    allocation_records[allocation_key] = ProductionBatchAllocateRecord(
                        batch_no=batch.batch_no,
                        shop_code=sale["shop_code"],
                        shop_name=sale["shop_name"],
                        item_code=batch.item_code,
                        item_name=batch.item_name,
                        platform_code=sale["platform_code"],
                        platform_name=sale["platform_name"],
                        allocated_qty=allocation,
                        date=datetime.fromtimestamp(sale["date_time"]).date(),
                        order_sales_item_shop_sum_id=sale["id"],
                    )
                    # 如果分配过，应该为更新，需指定ID
                    if is_allocated:
                        allocation_records[allocation_key].id = sales_allocated_records[
                            sale["id"]
                        ]["id"]
                remaining_qty -= allocation
                if remaining_qty <= 0:
                    break

        # 批量更新批次分配数量
        write_log(f"批次更新数量: {len(batch_updates_dict)}")
        if batch_updates_dict:
            await session.execute(
                update(ProductionBatch), list(batch_updates_dict.values())
            )

        update_records: list[ProductionBatchAllocateRecord] = []
        insert_records: list[ProductionBatchAllocateRecord] = []
        for x in list(allocation_records.values()):
            if x.id:
                update_records.append(x)
            else:
                insert_records.append(x)

        write_log(f"插入数量: {len(insert_records)}")
        write_log(f"更新数量: {len(update_records)}")
        if insert_records:
            session.add_all(insert_records)
        if update_records:
            # 多条记录更新
            update_values = [
                {
                    "id": record.id,
                    "allocated_qty": record.allocated_qty,
                    "update_time": int(time.time()),
                }
                for record in update_records
            ]
            await session.execute(update(ProductionBatchAllocateRecord), update_values)

        await session.commit()


# 重新统计并且分配负数批号数据
async def re_statistic_and_allocate_negative_batch_number():
    # 第一步：收集所有需要重新分配的数据
    groups = {}
    async with get_session() as session:
        # 获取所有剩余数量负数批号数据
        query = select(ProductionBatch).where(
            ProductionBatch.qty < ProductionBatch.allocated_qty
        )
        result = await session.execute(query)
        negative_batchs = list(result.scalars().all())
        if len(negative_batchs) == 0:
            write_log("没有需要重新分配的数据")
            return
        # 收集所有相关的sales_item_shop_sum_ids
        for batch in negative_batchs:
            query = select(
                ProductionBatchAllocateRecord.order_sales_item_shop_sum_id
            ).where(
                ProductionBatchAllocateRecord.batch_no == batch.batch_no,
                ProductionBatchAllocateRecord.item_code == batch.item_code,
            )
            result = await session.execute(query)
            for osiss_id in result.scalars().all():
                key = (batch.batch_no, batch.item_code)
                if key not in groups:
                    groups[key] = set()
                groups[key].add(osiss_id)

    # 第二步：对每组数据进行重置和重新分配
    for key, ids in groups.items():
        batch_no, item_code = key
        async with get_session() as session:
            # 先删除相关分配记录
            await session.execute(
                delete(ProductionBatchAllocateRecord).where(
                    ProductionBatchAllocateRecord.order_sales_item_shop_sum_id.in_(ids)
                )
            )
            # 重置批号已分配数量
            await session.execute(
                update(ProductionBatch)
                .where(
                    ProductionBatch.batch_no == batch_no,
                    ProductionBatch.item_code == item_code,
                )
                .values(allocated_qty=0)
            )
            await session.commit()

        # 重新分配（这个函数会创建自己的session）
        await allocate_to_delivery(list(ids))


if __name__ == "__main__":
    number = "下沙留样R0120B"
    result = re_batch_no.match(number)
    if not result:
        print("no match")
    else:
        print(result.group(1))
