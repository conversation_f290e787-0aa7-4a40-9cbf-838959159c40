from sqlalchemy import (
    Column,
    String,
    DateTime,
    Integer,
    Text,
    SmallInteger,
    LargeBinary,
    ForeignKey,
    Numeric,
    BigInteger,
)
from sqlalchemy.orm import relationship, Mapped
from typing import List
from datetime import datetime
from .base import Base


class GuanyierpStockOtherInOrderDetail(Base):
    __tablename__ = "guanyierp_stock_other_in_order_detail"

    id: Mapped[int] = Column(Integer, primary_key=True)
    item_code: Mapped[str] = Column(String(50))
    sku_code: Mapped[str] = Column(String(50))
    qty: Mapped[int] = Column(Integer, nullable=False)
    instore_qty: Mapped[int] = Column(Integer, nullable=False)
    stock_qty: Mapped[int] = Column(Integer, nullable=False)
    memo: Mapped[str] = Column(String(255))
    batch: Mapped[str] = Column(String(50))
    unique: Mapped[str] = Column(String(50))
    sku_type: Mapped[str] = Column(String(50))
    memo_oacode: Mapped[str] = Column(String(50))
    memo_batch: Mapped[str] = Column(String(50))
    guanyierp_stock_other_in_order_code: Mapped[str] = Column(
        String(50), nullable=False
    )
    create_time: Mapped[datetime] = Column(DateTime, nullable=False)
    update_time: Mapped[datetime] = Column(DateTime, nullable=False)

    stock_other_in_order: Mapped["GuanyierpStockOtherInOrder"] = relationship(
        "GuanyierpStockOtherInOrder",
        back_populates="details_list",
    )


class GuanyierpStockOtherInOrder(Base):
    __tablename__ = "guanyierp_stock_other_in_order"

    id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    update_time: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="修改时间"
    )
    origin_data: Mapped[str] = Column(Text, nullable=False, comment="接口源数据")
    create_time: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="创建时间"
    )
    code: Mapped[str] = Column(
        String(32),
        ForeignKey(
            "guanyierp_stock_other_in_order_detail.guanyierp_stock_other_in_order_code"
        ),
        nullable=False,
        default="",
        comment="单据编号",
    )
    status: Mapped[int] = Column(
        SmallInteger, nullable=False, default=0, comment="状态"
    )
    del_: Mapped[int] = Column(
        "del", SmallInteger, nullable=False, default=0, comment="作废"
    )
    details: Mapped[str] = Column(Text, nullable=False, comment="明细")
    create_time: Mapped[int] = Column(BigInteger, nullable=False)

    # 关联关系
    details_list: Mapped[List["GuanyierpStockOtherInOrderDetail"]] = relationship(
        "GuanyierpStockOtherInOrderDetail",
        back_populates="stock_other_in_order",
    )


class GuanyierpPurchase(Base):
    __tablename__ = "guanyierp_purchase"

    id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True, comment="序号"
    )
    code: Mapped[str] = Column(String(255), nullable=True, comment="单据编号")
    note: Mapped[str] = Column(String(255), nullable=True)
    create_date: Mapped[datetime] = Column(DateTime, nullable=True, comment="创建时间")
    approve_date: Mapped[datetime] = Column(DateTime, nullable=True, comment="审核时间")
    warehouse_code: Mapped[bytes] = Column(LargeBinary, nullable=True)
    warehouse_name: Mapped[bytes] = Column(LargeBinary, nullable=True)
    supplier_code: Mapped[bytes] = Column(LargeBinary, nullable=True)
    supplier_name: Mapped[str] = Column(
        String(255), nullable=True, comment="供应商名称"
    )
    status: Mapped[int] = Column(SmallInteger, nullable=True, default=0)
    create_name: Mapped[bytes] = Column(LargeBinary, nullable=True)
    order_type: Mapped[bytes] = Column(LargeBinary, nullable=True)
    expected_receipt_date: Mapped[bytes] = Column(LargeBinary, nullable=True)
    detailList: Mapped[str] = Column(Text, nullable=True)
    status_text: Mapped[str] = Column(String(32), nullable=True)
    op_time: Mapped[datetime] = Column(DateTime, nullable=True)


class GuanyierpPurchaseArrive(Base):
    __tablename__ = "guanyierp_purchase_arrive"

    id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True, comment="序号"
    )
    code: Mapped[str] = Column(String(255), nullable=True, comment="单据编号")
    note: Mapped[str] = Column(Text, nullable=True, comment="备注")
    create_date: Mapped[datetime] = Column(DateTime, nullable=True, comment="创建时间")
    approve_date: Mapped[datetime] = Column(DateTime, nullable=True, comment="审核时间")
    warehouse_code: Mapped[str] = Column(String(255), nullable=True, comment="仓库代码")
    warehouse_name: Mapped[str] = Column(String(255), nullable=True, comment="仓库名称")
    supplier_code: Mapped[str] = Column(
        String(255), nullable=True, comment="供应商代码"
    )
    supplier_name: Mapped[str] = Column(
        String(255), nullable=True, comment="供应商名称"
    )
    status: Mapped[int] = Column(
        SmallInteger, nullable=True, default=0, comment="审核状态"
    )
    details: Mapped[str] = Column(Text, nullable=True, comment="采购入库明细")
    create_name: Mapped[str] = Column(String(255), nullable=True, comment="制单人")
    order_type: Mapped[str] = Column(String(255), nullable=True, comment="单据类型名称")
    purchase_code: Mapped[str] = Column(
        String(255), nullable=True, comment="采购订单号"
    )
    cancel: Mapped[int] = Column(Integer, nullable=True, comment="作废状态")
    account: Mapped[int] = Column(Integer, nullable=True, comment="记账状态")
    account_date: Mapped[datetime] = Column(DateTime, nullable=True, comment="记账时间")
    modify_date: Mapped[datetime] = Column(DateTime, nullable=True, comment="修改时间")
    status_text: Mapped[str] = Column(String(20), nullable=True, comment="审核状态")
    cancel_text: Mapped[str] = Column(String(20), nullable=True, comment="作废状态")
    account_text: Mapped[str] = Column(String(20), nullable=True, comment="记账状态")
    op_time: Mapped[datetime] = Column(DateTime, nullable=True)

    # 关联关系
    arrive_details: Mapped[List["GuanyierpPurchaseArriveDetail"]] = relationship(
        "GuanyierpPurchaseArriveDetail",
        back_populates="purchase_arrive",
        cascade="all, delete-orphan",
    )


class GuanyierpPurchaseArriveDetail(Base):
    __tablename__ = "guanyierp_purchase_arrive_details"

    id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True, comment="序号"
    )
    pid: Mapped[int] = Column(Integer, nullable=False)
    qty: Mapped[int] = Column(Integer, nullable=True, comment="数量")
    price: Mapped[float] = Column(Numeric(10, 2), nullable=True, comment="实际进价")
    amount: Mapped[float] = Column(Numeric(10, 2), nullable=True, comment="实际金额")
    note: Mapped[str] = Column(Text, nullable=True, comment="备注")
    item_id: Mapped[int] = Column(BigInteger, nullable=True, comment="商品ID")
    item_sku_id: Mapped[str] = Column(String(255), nullable=True, comment="规格ID")
    item_code: Mapped[str] = Column(String(255), nullable=True, comment="商品编号")
    sku_code: Mapped[str] = Column(String(255), nullable=True, comment="规格代码")
    sku_note: Mapped[str] = Column(String(255), nullable=True, comment="规格备注")
    sku_name: Mapped[str] = Column(String(255), nullable=True, comment="规格名称")
    tax_rate: Mapped[float] = Column(Numeric(10, 2), nullable=True, comment="税率")
    tax_amount: Mapped[float] = Column(Numeric(10, 2), nullable=True, comment="税额")
    detail_unique: Mapped[str] = Column(Text, nullable=True, comment="唯一码列表")
    detail_batch: Mapped[str] = Column(Text, nullable=True, comment="批次列表")
    guanyierp_purchase_arrive_code: Mapped[str] = Column(
        String(50),
        ForeignKey("guanyierp_purchase_arrive.code"),
        nullable=True,
        comment="采购入库单据编号",
    )
    note_oacode: Mapped[str] = Column(String(50), nullable=True, comment="OA单据编号")
    note_batch: Mapped[str] = Column(String(100), nullable=True, comment="批次")

    # 关联关系
    purchase_arrive: Mapped["GuanyierpPurchaseArrive"] = relationship(
        "GuanyierpPurchaseArrive", back_populates="arrive_details"
    )


class GuanyierpGoods3(Base):
    __tablename__ = "guanyierp_goods3"

    id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    mallId: Mapped[str] = Column(String(20), nullable=True, comment="店铺Id")
    item_id: Mapped[str] = Column(String(255), nullable=True)
    item_code: Mapped[str] = Column(String(255), nullable=True)
    item_name: Mapped[str] = Column(String(255), nullable=True)
    sku_sn: Mapped[str] = Column(String(255), nullable=True)
    sku_name: Mapped[str] = Column(String(255), nullable=True)
    goods_id: Mapped[str] = Column(String(255), nullable=True)
    sku_id: Mapped[str] = Column(String(255), nullable=True)


class GuanyierpGoods(Base):
    __tablename__ = "guanyierp_goods"

    id: Mapped[int] = Column(
        BigInteger, primary_key=True, autoincrement=True, comment="序号"
    )
    code: Mapped[str] = Column(String(255), nullable=True, comment="商品代码")
    name: Mapped[str] = Column(String(255), nullable=True, comment="商品名称")
    note: Mapped[str] = Column(String(255), nullable=True, comment="备注")
    weight: Mapped[float] = Column(Numeric(12, 4), nullable=True, comment="重量")
    length: Mapped[float] = Column(Numeric(12, 4), nullable=True, comment="长")
    width: Mapped[float] = Column(Numeric(12, 4), nullable=True, comment="宽")
    height: Mapped[float] = Column(Numeric(12, 4), nullable=True, comment="高")
    volume: Mapped[float] = Column(Numeric(12, 4), nullable=True, comment="体积")
    simple_name: Mapped[str] = Column(String(255), nullable=True, comment="商品简称")
    category_code: Mapped[str] = Column(String(255), nullable=True, comment="分类代码")
    category_name: Mapped[str] = Column(String(255), nullable=True, comment="分类名称")
    supplier_code: Mapped[str] = Column(
        String(255), nullable=True, comment="供应商代码"
    )
    item_unit_code: Mapped[str] = Column(
        String(255), nullable=True, comment="商品单位代码"
    )
    item_unit_name: Mapped[str] = Column(
        String(255), nullable=True, comment="商品单位名称"
    )
    package_point: Mapped[float] = Column(
        Numeric(12, 4), nullable=True, comment="打包积分"
    )
    sales_point: Mapped[float] = Column(
        Numeric(12, 4), nullable=True, comment="销售积分"
    )
    sales_price: Mapped[float] = Column(
        Numeric(12, 4), nullable=True, comment="标准售价"
    )
    purchase_price: Mapped[float] = Column(
        Numeric(12, 4), nullable=True, comment="标准进价"
    )
    agent_price: Mapped[float] = Column(
        Numeric(12, 4), nullable=True, comment="代理售价"
    )
    cost_price: Mapped[float] = Column(Numeric(12, 4), nullable=True, comment="成本价")
    stock_status_code: Mapped[str] = Column(
        String(255), nullable=True, comment="库存状态代码"
    )
    pic_url: Mapped[str] = Column(String(255), nullable=True, comment="图片地址")
    tax_no: Mapped[str] = Column(String(255), nullable=True, comment="税号")
    tax_rate: Mapped[float] = Column(Numeric(12, 4), nullable=True, comment="税率")
    origin_area: Mapped[str] = Column(String(255), nullable=True, comment="原产地")
    supplier_outerid: Mapped[str] = Column(
        String(255), nullable=True, comment="供应商货号"
    )
    shelf_life: Mapped[int] = Column(Integer, nullable=True, comment="保质期")
    warning_days: Mapped[int] = Column(Integer, nullable=True, comment="预警天数")
    combine: Mapped[int] = Column(Integer, nullable=True, comment="是否为组合商品")
    del_: Mapped[int] = Column("del", Integer, nullable=True, comment="是否已停用")
    skus: Mapped[str] = Column(Text, nullable=True, comment="规格信息")
    combine_items: Mapped[str] = Column(Text, nullable=True, comment="组合明细")
    custom_attr: Mapped[str] = Column(Text, nullable=True, comment="自定义属性")
    item_add_attribute: Mapped[int] = Column(
        Integer, nullable=True, comment="商品附加属性"
    )
    item_brand_code: Mapped[str] = Column(
        String(255), nullable=True, comment="品牌代码"
    )
    item_brand_name: Mapped[str] = Column(
        String(255), nullable=True, comment="品牌名称"
    )
    goods_id: Mapped[str] = Column(String(255), nullable=True)
    bar_code: Mapped[str] = Column(String(255), nullable=True)
    item_id: Mapped[str] = Column(String(255), nullable=True)
    item_codes: Mapped[str] = Column(String(1024), nullable=True)
    create_date: Mapped[datetime] = Column(DateTime, nullable=True, comment="创建时间")
    modify_date: Mapped[datetime] = Column(DateTime, nullable=True, comment="修改时间")
    op_time: Mapped[datetime] = Column(DateTime, nullable=False)
