import os
from pathlib import Path
from functools import lru_cache
import configparser

from app.core.logger import get_logger


class Settings:
    # 应用配置
    APP_NAME: str = "DATAPILOT"
    APP_ENV: str = "development"  # development, test, production
    DEBUG: bool = True
    API_KEY: str = ""  # API密钥，为空时不进行验证
    API_KEY_EXEMPT_PREFIXES: list[str] = []  # 需要忽略API密钥验证的路由前缀列表

    # 管易ERP配置
    GUANYI_APPKEY: str = ""
    GUANYI_SESSIONKEY: str = ""
    GUANYI_SECRET: str = ""

    # 数据库配置
    MONGO_DATABASE_URL: str = ""

    # 日志配置
    LOG_LEVEL: str = "DEBUG"
    LOG_FILE: str = "logs/app.log"

    ENV_FILES: list[Path] = []

    OA: dict = {
        "HOST": "",
        "PORT": "",
        "USER": "",
        "PASSWORD": "",
        "DATABASE": "",
    }

    DOUDIAN: dict = {
        "APPKEY": "",
        "APPSECRET": "",
    }

    JWT: dict = {
        "PUBLIC_KEY": "",
    }

    # 小红书API配置
    XHS_APP_ID: str = ""
    XHS_APP_SECRET: str = ""

    def __init__(self):

        self.ENV_FILES = []

    def load_config(self, config: configparser.ConfigParser):
        """从配置文件加载配置"""
        # 应用配置
        if "app" in config:
            self.APP_NAME = config.get("app", "APP_NAME", fallback=self.APP_NAME)
            self.APP_ENV = config.get("app", "APP_ENV", fallback=self.APP_ENV)
            self.DEBUG = config.getboolean("app", "DEBUG", fallback=self.DEBUG)
            self.API_KEY = config.get("app", "API_KEY", fallback=self.API_KEY)

            # 加载API密钥忽略路由前缀
            exempt_prefixes = config.get("app", "API_KEY_EXEMPT_PREFIXES", fallback="")
            if exempt_prefixes:
                self.API_KEY_EXEMPT_PREFIXES = [
                    p.strip() for p in exempt_prefixes.split(",")
                ]

        # 管易ERP配置
        if "guanyi" in config:
            self.GUANYI_APPKEY = config.get(
                "guanyi", "APPKEY", fallback=self.GUANYI_APPKEY
            )
            self.GUANYI_SESSIONKEY = config.get(
                "guanyi", "SESSIONKEY", fallback=self.GUANYI_SESSIONKEY
            )
            self.GUANYI_SECRET = config.get(
                "guanyi", "SECRET", fallback=self.GUANYI_SECRET
            )

        # 数据库配置
        if "database" in config:
            self.MONGO_DATABASE_URL = config.get(
                "database", "MONGO_URL", fallback=self.MONGO_DATABASE_URL
            )

        # OA配置
        if "oa" in config:
            self.OA["HOST"] = config.get("oa", "HOST", fallback=self.OA["HOST"])
            self.OA["PORT"] = config.get("oa", "PORT", fallback=self.OA["PORT"])
            self.OA["USER"] = config.get("oa", "USER", fallback=self.OA["USER"])
            self.OA["PASSWORD"] = config.get(
                "oa", "PASSWORD", fallback=self.OA["PASSWORD"]
            )
            self.OA["DATABASE"] = config.get(
                "oa", "DATABASE", fallback=self.OA["DATABASE"]
            )

        # 日志配置
        if "logging" in config:
            self.LOG_LEVEL = config.get("logging", "LEVEL", fallback=self.LOG_LEVEL)
            self.LOG_FILE = config.get("logging", "FILE", fallback=self.LOG_FILE)

        # 抖店配置
        if "doudian" in config:
            self.DOUDIAN["APPKEY"] = config.get(
                "doudian", "APPKEY", fallback=self.DOUDIAN["APPKEY"]
            )
            self.DOUDIAN["APPSECRET"] = config.get(
                "doudian", "APPSECRET", fallback=self.DOUDIAN["APPSECRET"]
            )

        if "jwt" in config:
            self.JWT["PUBLIC_KEY"] = config.get(
                "jwt", "PUBLIC_KEY", fallback=self.JWT["PUBLIC_KEY"]
            )

        # 小红书配置
        if "xiaohongshu" in config:
            self.XHS_APP_ID = config.get(
                "xiaohongshu", "APP_ID", fallback=self.XHS_APP_ID
            )
            self.XHS_APP_SECRET = config.get(
                "xiaohongshu", "APP_SECRET", fallback=self.XHS_APP_SECRET
            )


def get_env_file() -> tuple[Path, Path]:
    """获取环境配置文件路径"""
    env = os.getenv("APP_ENV", "development")

    # 获取当前工作目录
    current_dir = Path.cwd()
    config_dir = current_dir / "config"
    env_file = config_dir / f"{env}.ini"
    if not env_file.exists():
        raise FileNotFoundError(f"配置文件不存在: {env_file}")
    default_env_file = config_dir / "default.ini"
    return (default_env_file, env_file)


@lru_cache()
def get_settings() -> Settings:
    """获取应用配置"""
    default_env_file, env_file = get_env_file()

    config = configparser.ConfigParser()

    # 首先读取默认配置
    if default_env_file.exists():
        config.read(default_env_file)

    # 然后读取环境特定配置，会覆盖默认配置
    config.read(env_file)

    settings = Settings()
    settings.ENV_FILES.append(env_file)
    settings.ENV_FILES.append(default_env_file)
    settings.load_config(config)
    return settings


# 全局配置实例
settings = get_settings()
