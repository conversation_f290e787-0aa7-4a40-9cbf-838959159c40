# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.afterSale_OpenAfterSaleChannel.param.AfterSaleOpenAfterSaleChannelParam import AfterSaleOpenAfterSaleChannelParam


class AfterSaleOpenAfterSaleChannelRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AfterSaleOpenAfterSaleChannelParam()

	def getUrlPath(self, ):
		return "/afterSale/OpenAfterSaleChannel"

	def getParams(self, ):
		return self.params



