import asyncio
from datetime import datetime, timedelta
import json
from typing import List

from app.core.database import db
from app.core.logger import logger

# 从libs中导入API功能
from libs.xiaohongshu import (
    # 认证相关
    get_access_token,
    get_access_token_only,
    get_approval_advertisers,
    # API调用
    get_campaign_list,
    get_creativity_search,
    get_creative_offline_report,
    get_note_offline_report,
    get_sub_account_list,
    # 类型定义
    GetCampaignListParams,
    GetCreativitySearchParams,
    GetCreativeOfflineReportParams,
    GetNoteOfflineReportParams,
    FilterClause,
    TokenInfo,
    ApprovalAdvertisersList,
    CampaignListResponse,
    CreativitySearchResponse,
    CreativeOfflineReportResponse,
    NoteOfflineReportResponse,
)

"""
小红书聚光平台业务服务模块

本模块专注于业务逻辑实现，包括：

业务功能：
1. 创意数据同步到数据库
2. 创意基础信息同步
3. 汇总数据生成和管理
4. 数据同步任务调度

技术特性：
- 异步数据库操作
- 分页数据处理
- 错误处理和重试机制
- 数据一致性保证

API功能请使用：
- from libs.xiaohongshu import get_note_offline_report, GetNoteOfflineReportParams
- 详细使用方法请参考 libs/xiaohongshu/README.md
"""

# ==================== 创意数据同步业务逻辑 ====================


async def sync_xiaohongshu_creativity_data(
    start_date: str = None, end_date: str = None, advertiser_ids: list[int] = None
) -> dict:
    """
    同步小红书创意数据到数据库

    Args:
        start_date: 开始日期，格式 YYYY-MM-DD，默认为昨天
        end_date: 结束日期，格式 YYYY-MM-DD，默认为昨天
        advertiser_ids: 广告主ID列表，默认为所有授权的广告主

    Returns:
        dict: 同步结果统计
    """
    from app.core.oa_database import get_session, BrandXhsJgCreativityDataReport

    # 设置默认日期为昨天
    if not start_date or not end_date:
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
        start_date = start_date or yesterday
        end_date = end_date or yesterday

    logger.info(f"开始同步小红书创意数据，日期范围: {start_date} 到 {end_date}")

    # 获取访问令牌和授权广告主
    token_info = await get_access_token()
    if not token_info:
        logger.error("获取小红书访问令牌失败")
        return {"success": False, "error": "获取访问令牌失败"}

    access_token = token_info["access_token"]
    approval_advertisers = token_info["approval_advertisers"]

    if not approval_advertisers:
        logger.error("没有授权的广告主")
        return {"success": False, "error": "没有授权的广告主"}

    # 过滤广告主ID
    if advertiser_ids:
        approval_advertisers = [
            adv
            for adv in approval_advertisers
            if adv["advertiser_id"] in advertiser_ids
        ]

    total_processed = 0
    total_inserted = 0
    total_updated = 0
    errors = []

    async with get_session(transaction=True) as session:
        try:
            for advertiser in approval_advertisers:
                advertiser_id = advertiser["advertiser_id"]
                advertiser_name = advertiser["advertiser_name"]

                logger.info(f"处理广告主: {advertiser_name} (ID: {advertiser_id})")

                # 分页获取数据
                page_num = 1
                page_size = 500  # 最大页面大小

                while True:
                    try:
                        # 构建查询参数
                        params = GetCreativeOfflineReportParams(
                            advertiser_id=advertiser_id,
                            start_date=start_date,
                            end_date=end_date,
                            time_unit="DAY",  # 按天获取数据
                            page_num=page_num,
                            page_size=page_size,
                        )

                        # 调用API获取数据
                        result = get_creative_offline_report(access_token, params)

                        if not result.get("success"):
                            error_msg = f"API调用失败: {result.get('msg', '未知错误')}"
                            logger.error(f"广告主 {advertiser_name}: {error_msg}")
                            errors.append(f"{advertiser_name}: {error_msg}")
                            break

                        data_list = result["data"]["data_list"]

                        if not data_list:
                            logger.info(
                                f"广告主 {advertiser_name} 第 {page_num} 页无数据，结束分页"
                            )
                            break

                        logger.info(
                            f"广告主 {advertiser_name} 第 {page_num} 页获取到 {len(data_list)} 条数据"
                        )

                        # 处理每条数据
                        for item in data_list:
                            try:
                                processed, inserted, updated = (
                                    await _process_creativity_item(
                                        session, item, advertiser_id
                                    )
                                )
                                total_processed += processed
                                total_inserted += inserted
                                total_updated += updated

                            except Exception as e:
                                error_msg = f"处理创意数据失败: {str(e)}"
                                logger.error(
                                    f"创意ID {item.get('creativity_id', 'N/A')}: {error_msg}"
                                )
                                errors.append(
                                    f"创意ID {item.get('creativity_id', 'N/A')}: {error_msg}"
                                )

                        # 检查是否还有更多数据
                        if len(data_list) < page_size:
                            logger.info(f"广告主 {advertiser_name} 数据获取完成")
                            break

                        page_num += 1

                    except Exception as e:
                        error_msg = f"获取第 {page_num} 页数据失败: {str(e)}"
                        logger.error(f"广告主 {advertiser_name}: {error_msg}")
                        errors.append(
                            f"{advertiser_name} 第 {page_num} 页: {error_msg}"
                        )
                        break

            # 提交事务
            await session.commit()

            result = {
                "success": True,
                "total_processed": total_processed,
                "total_inserted": total_inserted,
                "total_updated": total_updated,
                "errors": errors,
                "date_range": f"{start_date} 到 {end_date}",
                "advertisers_count": len(approval_advertisers),
            }

            logger.info(
                f"同步完成: 处理 {total_processed} 条，新增 {total_inserted} 条，更新 {total_updated} 条"
            )
            return result

        except Exception as e:
            await session.rollback()
            logger.error(f"同步过程中发生异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "total_processed": total_processed,
                "total_inserted": total_inserted,
                "total_updated": total_updated,
                "errors": errors,
            }


async def _process_creativity_item(
    session, item: dict, advertiser_id: int
) -> tuple[int, int, int]:
    """
    处理单个创意数据项

    Args:
        session: 数据库会话
        item: 创意数据项
        advertiser_id: 广告主ID

    Returns:
        tuple: (processed_count, inserted_count, updated_count)
    """
    from app.core.oa_database import BrandXhsJgCreativityDataReport
    from sqlalchemy import select, and_

    creativity_id = str(item.get("creativity_id", ""))
    note_id = str(item.get("note_id", ""))
    time_str = item.get("time", "")

    if not creativity_id or not note_id or not time_str:
        logger.warning(
            f"跳过无效数据: creativity_id={creativity_id}, note_id={note_id}, time={time_str}"
        )
        return 0, 0, 0

    # 解析时间
    try:
        date_obj = datetime.strptime(time_str, "%Y-%m-%d")
        date_time = int(date_obj.timestamp())
        date_year = date_obj.strftime("%Y")
        date_month = date_obj.strftime("%m")
        date_day = date_obj.strftime("%d")
    except ValueError:
        logger.warning(f"时间格式错误: {time_str}")
        return 0, 0, 0

    # 准备数据
    def safe_float(value, default=0.0):
        """安全转换为浮点数"""
        if value is None or value == "":
            return default
        try:
            return float(value)
        except (ValueError, TypeError):
            return default

    def safe_str(value, default=""):
        """安全转换为字符串"""
        if value is None:
            return default
        return str(value)

    # 构建数据字典
    data = {
        "api": True,
        "origin_data": json.dumps(item, ensure_ascii=False),
        "creativity_id": creativity_id,
        "note_id": note_id,
        "date_time": date_time,
        "date_year": date_year,
        "date_month": date_month,
        "date_day": date_day,
        "fee": safe_float(item.get("fee")),
        "impression": safe_str(item.get("impression")),
        "click": safe_str(item.get("click")),
        "ctr": safe_str(item.get("ctr")),
        "acp": safe_float(item.get("acp")),
        "cpm": safe_float(item.get("cpm")),
        "like": safe_str(item.get("like")),
        "comment": safe_str(item.get("comment")),
        "collect": safe_str(item.get("collect")),
        "follow": safe_str(item.get("follow")),
        "share": safe_str(item.get("share")),
        "interaction": safe_str(item.get("interaction")),
        "cpi": safe_float(item.get("cpi")),
        "action_button_click": safe_str(item.get("action_button_click")),
        "action_button_ctr": safe_str(item.get("action_button_ctr")),
        "screenshot": safe_str(item.get("screenshot")),
        "pic_save": safe_str(item.get("pic_save")),
        "reserve_pv": safe_str(item.get("reserve_pv")),
        "search_cmt_click": safe_str(item.get("search_cmt_click")),
        "search_cmt_click_cvr": safe_str(item.get("search_cmt_click_cvr")),
        "search_cmt_after_read_avg": safe_float(item.get("search_cmt_after_read_avg")),
        "search_cmt_after_read": safe_str(item.get("search_cmt_after_read")),
        "delete_time": 0,  # 设置默认删除时间为0，表示未删除
    }

    # 检查是否已存在记录（基于创意ID、笔记ID和日期的唯一性）
    stmt = select(BrandXhsJgCreativityDataReport).where(
        and_(
            BrandXhsJgCreativityDataReport.creativity_id == creativity_id,
            BrandXhsJgCreativityDataReport.note_id == note_id,
            BrandXhsJgCreativityDataReport.date_time == date_time,
        )
    )

    existing_record = await session.execute(stmt)
    existing_record = existing_record.scalar_one_or_none()

    if existing_record:
        # 更新现有记录
        for key, value in data.items():
            if key not in ["create_time", "delete_time"]:  # 不更新创建时间和删除时间
                setattr(existing_record, key, value)

        logger.debug(
            f"更新记录: creativity_id={creativity_id}, note_id={note_id}, date={time_str}"
        )
        return 1, 0, 1
    else:
        # 插入新记录
        new_record = BrandXhsJgCreativityDataReport(**data)
        session.add(new_record)

        logger.debug(
            f"新增记录: creativity_id={creativity_id}, note_id={note_id}, date={time_str}"
        )
        return 1, 1, 0


async def sync_xiaohongshu_creativity_data_for_date_range(
    days_back: int = 7, advertiser_ids: list[int] = None
) -> dict:
    """
    同步指定天数范围内的小红书创意数据

    Args:
        days_back: 往前同步的天数，默认7天
        advertiser_ids: 广告主ID列表，默认为所有授权的广告主

    Returns:
        dict: 同步结果统计
    """
    end_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")  # 昨天
    start_date = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")

    return await sync_xiaohongshu_creativity_data(
        start_date=start_date, end_date=end_date, advertiser_ids=advertiser_ids
    )


# ==================== 创意基础信息同步业务逻辑 ====================


async def sync_xiaohongshu_creativity_info(advertiser_ids: list[int] = None) -> dict:
    """
    同步小红书创意基础信息到数据库

    Args:
        advertiser_ids: 广告主ID列表，默认为所有授权的广告主

    Returns:
        dict: 同步结果统计
    """
    from app.core.oa_database import get_session, BrandXhsJgCreativity

    logger.info("开始同步小红书创意基础信息")

    # 获取访问令牌和授权广告主
    token_info = await get_access_token()
    if not token_info:
        logger.error("获取小红书访问令牌失败")
        return {"success": False, "error": "获取访问令牌失败"}

    access_token = token_info["access_token"]
    approval_advertisers = token_info["approval_advertisers"]

    if not approval_advertisers:
        logger.error("没有授权的广告主")
        return {"success": False, "error": "没有授权的广告主"}

    # 过滤广告主ID
    if advertiser_ids:
        approval_advertisers = [
            adv
            for adv in approval_advertisers
            if adv["advertiser_id"] in advertiser_ids
        ]

    total_processed = 0
    total_inserted = 0
    total_updated = 0
    errors = []

    async with get_session(transaction=True) as session:
        try:
            for advertiser in approval_advertisers:
                advertiser_id = advertiser["advertiser_id"]
                advertiser_name = advertiser["advertiser_name"]

                logger.info(f"处理广告主: {advertiser_name} (ID: {advertiser_id})")

                # 首先获取广告计划列表
                campaigns = await _get_campaigns_for_advertiser(
                    access_token, advertiser_id
                )

                for campaign in campaigns:
                    campaign_id = campaign["campaign_id"]
                    campaign_name = campaign["campaign_name"]

                    logger.info(f"处理计划: {campaign_name} (ID: {campaign_id})")

                    # 获取该计划下的创意列表
                    creativities = await _get_creativities_for_campaign(
                        access_token, advertiser_id, campaign_id
                    )
                    logger.info(f"获取到创意列表, 数量: {len(creativities)}")

                    for creativity in creativities:
                        try:
                            processed, inserted, updated = (
                                await _process_creativity_info_item(
                                    session, creativity, campaign, advertiser_id
                                )
                            )
                            total_processed += processed
                            total_inserted += inserted
                            total_updated += updated

                        except Exception as e:
                            error_msg = f"处理创意信息失败: {str(e)}"
                            logger.error(
                                f"创意ID {creativity.get('creativity_id', 'N/A')}: {error_msg}"
                            )
                            errors.append(
                                f"创意ID {creativity.get('creativity_id', 'N/A')}: {error_msg}"
                            )

            # 提交事务
            await session.commit()

            result = {
                "success": True,
                "total_processed": total_processed,
                "total_inserted": total_inserted,
                "total_updated": total_updated,
                "errors": errors,
                "advertisers_count": len(approval_advertisers),
            }

            logger.info(
                f"创意信息同步完成: 处理 {total_processed} 条，新增 {total_inserted} 条，更新 {total_updated} 条"
            )
            return result

        except Exception as e:
            await session.rollback()
            logger.error(f"同步过程中发生异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "total_processed": total_processed,
                "total_inserted": total_inserted,
                "total_updated": total_updated,
                "errors": errors,
            }


async def _get_campaigns_for_advertiser(access_token: str, advertiser_id: int) -> list:
    """
    获取指定广告主的所有广告计划

    Args:
        access_token: 访问令牌
        advertiser_id: 广告主ID

    Returns:
        list: 广告计划列表
    """
    campaigns = []
    page_index = 1
    page_size = 20

    while True:
        try:
            params = GetCampaignListParams(
                advertiser_id=advertiser_id,
                status=6,  # 所有未删除状态
                page={"page_index": page_index, "page_size": page_size},
            )

            result = get_campaign_list(access_token, params)

            if not result.get("success"):
                logger.error(f"获取计划列表失败: {result.get('msg', '未知错误')}")
                break

            campaign_list = result["data"]["base_campaign_dtos"]

            if not campaign_list:
                break

            campaigns.extend(campaign_list)

            # 检查是否还有更多数据
            if len(campaign_list) < page_size:
                break

            page_index += 1

        except Exception as e:
            logger.error(f"获取计划列表异常: {str(e)}")
            break

    return campaigns


async def _get_creativities_for_campaign(
    access_token: str, advertiser_id: int, campaign_id: int
) -> list:
    """
    获取指定计划下的所有创意

    Args:
        access_token: 访问令牌
        advertiser_id: 广告主ID
        campaign_id: 计划ID

    Returns:
        list: 创意列表
    """
    creativities = []
    page_index = 1
    page_size = 20

    while True:
        try:
            params = GetCreativitySearchParams(
                advertiser_id=advertiser_id,
                campaign_id=campaign_id,
                status=2,  # 所有未删除状态
                page={"page_index": page_index, "page_size": page_size},
            )

            result = get_creativity_search(access_token, params)

            if not result.get("success"):
                logger.error(f"获取创意列表失败: {result.get('msg', '未知错误')}")
                break

            creativity_list = result["data"]["creativity_dtos"]

            if not creativity_list:
                break

            creativities.extend(creativity_list)

            # 检查是否还有更多数据
            if len(creativity_list) < page_size:
                break

            page_index += 1

        except Exception as e:
            logger.error(f"获取创意列表异常: {str(e)}")
            break

    return creativities


async def _process_creativity_info_item(
    session, creativity: dict, campaign: dict, advertiser_id: int
) -> tuple[int, int, int]:
    """
    处理单个创意信息项

    Args:
        session: 数据库会话
        creativity: 创意数据
        campaign: 计划数据
        advertiser_id: 广告主ID

    Returns:
        tuple: (processed_count, inserted_count, updated_count)
    """
    from app.core.oa_database import BrandXhsJgCreativity
    from sqlalchemy import select
    import time

    creativity_id = str(creativity.get("creativity_id", ""))

    if not creativity_id:
        logger.warning("跳过无效创意数据: creativity_id为空")
        return 0, 0, 0

    # 准备数据
    def safe_str(value, default=""):
        """安全转换为字符串"""
        if value is None:
            return default
        return str(value)

    def safe_int(value, default=0):
        """安全转换为整数"""
        if value is None:
            return default
        try:
            return int(value)
        except (ValueError, TypeError):
            return default

    # 构建数据字典
    current_time = int(time.time())

    # 构建抓取数据包（用于后续数据抓取的参数）
    catch_data = {
        "advertiser_id": advertiser_id,
        "campaign_id": creativity.get("campaign_id"),
        "unit_id": creativity.get("unit_id"),
        "creativity_id": creativity_id,
        "note_id": creativity.get("note_id"),
    }

    data = {
        "catch_time": current_time,
        "last_catch_error_time": 0,
        "catch_data": json.dumps(catch_data, ensure_ascii=False),
        "campaign_id": safe_str(creativity.get("campaign_id")),
        "campaign_name": safe_str(campaign.get("campaign_name")),
        "marketing_target": safe_int(campaign.get("marketing_target")),
        "placement": safe_int(campaign.get("placement")),
        "unit_id": safe_str(creativity.get("unit_id")),
        "unit_name": safe_str(
            creativity.get("unit_name", "")
        ),  # API可能不返回unit_name
        "creativity_id": creativity_id,
        "creativity_name": safe_str(creativity.get("creativity_name")),
        "note_id": safe_str(creativity.get("note_id")),
        "note_title": safe_str(
            creativity.get("note_title", "")
        ),  # 可能需要从其他接口获取
        "brand_resource_id": 0,  # 默认值，可能需要后续关联
        "delete_time": 0,  # 设置默认删除时间为0，表示未删除
    }

    # 检查是否已存在记录（基于创意ID的唯一性）
    stmt = select(BrandXhsJgCreativity).where(
        BrandXhsJgCreativity.creativity_id == creativity_id
    )

    existing_record = await session.execute(stmt)
    existing_record = existing_record.scalar_one_or_none()

    if existing_record:
        # 更新现有记录
        for key, value in data.items():
            if key not in ["create_time", "delete_time"]:  # 不更新创建时间和删除时间
                setattr(existing_record, key, value)

        logger.info(f"更新创意信息: creativity_id={creativity_id}")
        return 1, 0, 1
    else:
        # 插入新记录
        new_record = BrandXhsJgCreativity(**data)
        session.add(new_record)

        logger.info(f"新增创意信息: creativity_id={creativity_id}")
        return 1, 1, 0


# ==================== 小红书汇总数据生成业务逻辑 ====================


async def generate_xiaohongshu_overall_data_report(
    start_date: str = None, end_date: str = None, force_regenerate: bool = False
) -> dict:
    """
    从创意报表数据生成汇总报表数据

    Args:
        start_date: 开始日期，格式 YYYY-MM-DD，默认为昨天
        end_date: 结束日期，格式 YYYY-MM-DD，默认为昨天
        force_regenerate: 是否强制重新生成已存在的数据

    Returns:
        dict: 生成结果统计
    """
    from app.core.oa_database import (
        get_session,
        BrandXhsJgCreativityDataReport,
        BrandXhsJgOverallDataReport,
    )
    from sqlalchemy import select, func, and_

    # 设置默认日期为昨天
    if not start_date or not end_date:
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
        start_date = start_date or yesterday
        end_date = end_date or yesterday

    logger.info(f"开始生成小红书汇总数据，日期范围: {start_date} 到 {end_date}")

    total_processed = 0
    total_inserted = 0
    total_updated = 0
    errors = []

    async with get_session(transaction=True) as session:
        try:
            # 解析日期范围
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")

            # 按日期循环处理
            current_date = start_date_obj
            while current_date <= end_date_obj:
                date_str = current_date.strftime("%Y-%m-%d")
                date_timestamp = int(current_date.timestamp())

                logger.info(f"处理日期: {date_str}")

                try:
                    processed, inserted, updated = await _process_overall_data_for_date(
                        session, date_timestamp, date_str, force_regenerate
                    )
                    total_processed += processed
                    total_inserted += inserted
                    total_updated += updated

                except Exception as e:
                    error_msg = f"处理日期 {date_str} 失败: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)

                current_date += timedelta(days=1)

            # 提交事务
            await session.commit()

            result = {
                "success": True,
                "total_processed": total_processed,
                "total_inserted": total_inserted,
                "total_updated": total_updated,
                "errors": errors,
                "date_range": f"{start_date} 到 {end_date}",
            }

            logger.info(
                f"汇总数据生成完成: 处理 {total_processed} 条，新增 {total_inserted} 条，更新 {total_updated} 条"
            )
            return result

        except Exception as e:
            await session.rollback()
            logger.error(f"生成汇总数据过程中发生异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "total_processed": total_processed,
                "total_inserted": total_inserted,
                "total_updated": total_updated,
                "errors": errors,
            }


async def _process_overall_data_for_date(
    session, date_timestamp: int, date_str: str, force_regenerate: bool = False
) -> tuple[int, int, int]:
    """
    处理单个日期的汇总数据

    Args:
        session: 数据库会话
        date_timestamp: 日期时间戳
        date_str: 日期字符串 YYYY-MM-DD
        force_regenerate: 是否强制重新生成

    Returns:
        tuple: (processed_count, inserted_count, updated_count)
    """
    from app.core.oa_database import (
        BrandXhsJgCreativityDataReport,
        BrandXhsJgOverallDataReport,
    )
    from sqlalchemy import select, func

    # 检查是否已存在汇总数据
    existing_stmt = select(BrandXhsJgOverallDataReport).where(
        BrandXhsJgOverallDataReport.date_time == date_timestamp
    )
    existing_record = await session.execute(existing_stmt)
    existing_record = existing_record.scalar_one_or_none()

    if existing_record and not force_regenerate:
        logger.debug(f"日期 {date_str} 的汇总数据已存在，跳过")
        return 0, 0, 0

    # 查询该日期的创意数据进行汇总
    creativity_stmt = select(
        func.sum(BrandXhsJgCreativityDataReport.fee).label("total_fee"),
        func.count(BrandXhsJgCreativityDataReport.id).label("record_count"),
    ).where(BrandXhsJgCreativityDataReport.date_time == date_timestamp)

    result = await session.execute(creativity_stmt)
    aggregation = result.first()

    if not aggregation or aggregation.record_count == 0:
        logger.debug(f"日期 {date_str} 没有创意数据，跳过汇总")
        return 0, 0, 0

    # 获取所有记录进行手动汇总（因为impression和click是字符串类型）
    detail_stmt = select(
        BrandXhsJgCreativityDataReport.impression,
        BrandXhsJgCreativityDataReport.click,
    ).where(BrandXhsJgCreativityDataReport.date_time == date_timestamp)

    detail_result = await session.execute(detail_stmt)
    detail_records = detail_result.fetchall()

    # 手动计算汇总指标
    total_fee = float(aggregation.total_fee or 0)
    total_impression = 0
    total_click = 0

    for record in detail_records:
        # 安全转换字符串到数字
        try:
            impression_val = (
                int(record.impression)
                if record.impression and record.impression.strip()
                else 0
            )
            total_impression += impression_val
        except (ValueError, AttributeError):
            pass

        try:
            click_val = (
                int(record.click) if record.click and record.click.strip() else 0
            )
            total_click += click_val
        except (ValueError, AttributeError):
            pass

    # 计算点击率
    ctr = (total_click / total_impression * 100) if total_impression > 0 else 0

    # 计算平均点击价格
    acp = (total_fee / total_click) if total_click > 0 else 0

    # 计算环比（与前一天对比）
    previous_date_timestamp = date_timestamp - 86400  # 前一天
    previous_stmt = select(BrandXhsJgOverallDataReport).where(
        BrandXhsJgOverallDataReport.date_time == previous_date_timestamp
    )
    previous_result = await session.execute(previous_stmt)
    previous_record = previous_result.scalar_one_or_none()

    # 计算环比
    def calculate_chain_ratio(current_value, previous_value):
        """计算环比"""
        if previous_value == 0:
            return "100.00%" if current_value > 0 else "0.00%"
        ratio = ((current_value - previous_value) / previous_value) * 100
        return f"{ratio:+.2f}%"

    fee_chain_ratio = "0.00%"
    impression_chain_ratio = "0.00%"
    click_chain_ratio = "0.00%"
    ctr_chain_ratio = "0.00%"
    acp_chain_ratio = "0.00%"

    if previous_record:
        try:
            prev_fee = float(previous_record.fee if previous_record.fee else "0")
            prev_impression = int(
                previous_record.impression if previous_record.impression else "0"
            )
            prev_click = int(previous_record.click if previous_record.click else "0")
            prev_ctr = float(
                previous_record.ctr.replace("%", "") if previous_record.ctr else "0"
            )
            prev_acp = float(previous_record.acp if previous_record.acp else "0")

            fee_chain_ratio = calculate_chain_ratio(total_fee, prev_fee)
            impression_chain_ratio = calculate_chain_ratio(
                total_impression, prev_impression
            )
            click_chain_ratio = calculate_chain_ratio(total_click, prev_click)
            ctr_chain_ratio = calculate_chain_ratio(ctr, prev_ctr)
            acp_chain_ratio = calculate_chain_ratio(acp, prev_acp)
        except (ValueError, AttributeError) as e:
            logger.warning(f"计算环比时出错: {e}")

    # 解析日期
    date_obj = datetime.fromtimestamp(date_timestamp)
    date_year = date_obj.strftime("%Y")
    date_month = date_obj.strftime("%m")
    date_day = date_obj.strftime("%d")

    # 构建汇总数据
    overall_data = {
        "api": True,
        "origin_data": json.dumps(
            {
                "source": "aggregated_from_creativity_data",
                "date": date_str,
                "total_records": aggregation.record_count,
                "aggregation_time": datetime.now().isoformat(),
            },
            ensure_ascii=False,
        ),
        "date_time": date_timestamp,
        "date_year": date_year,
        "date_month": date_month,
        "date_day": date_day,
        "fee": str(total_fee),
        "fee_chain_ratio": fee_chain_ratio,
        "impression": str(total_impression),
        "impression_chain_ratio": impression_chain_ratio,
        "click": str(total_click),
        "click_chain_ratio": click_chain_ratio,
        "ctr": f"{ctr:.2f}%",
        "ctr_chain_ratio": ctr_chain_ratio,
        "acp": f"{acp:.2f}",
        "acp_chain_ratio": acp_chain_ratio,
        "delete_time": 0,
    }

    if existing_record:
        # 更新现有记录
        for key, value in overall_data.items():
            if key not in ["create_time", "delete_time"]:
                setattr(existing_record, key, value)

        logger.debug(f"更新汇总数据: date={date_str}")
        return 1, 0, 1
    else:
        # 插入新记录
        new_record = BrandXhsJgOverallDataReport(**overall_data)
        session.add(new_record)

        logger.debug(f"新增汇总数据: date={date_str}")
        return 1, 1, 0


async def generate_xiaohongshu_overall_data_for_date_range(
    days_back: int = 7, force_regenerate: bool = False
) -> dict:
    """
    生成指定天数范围内的小红书汇总数据

    Args:
        days_back: 往前生成的天数，默认7天
        force_regenerate: 是否强制重新生成已存在的数据

    Returns:
        dict: 生成结果统计
    """
    end_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")  # 昨天
    start_date = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")

    return await generate_xiaohongshu_overall_data_report(
        start_date=start_date, end_date=end_date, force_regenerate=force_regenerate
    )


async def sync_and_generate_xiaohongshu_data(
    start_date: str = None,
    end_date: str = None,
    advertiser_ids: list[int] = None,
    generate_overall: bool = True,
) -> dict:
    """
    同步创意数据并生成汇总数据的一站式函数

    Args:
        start_date: 开始日期，格式 YYYY-MM-DD，默认为昨天
        end_date: 结束日期，格式 YYYY-MM-DD，默认为昨天
        advertiser_ids: 广告主ID列表，默认为所有授权的广告主
        generate_overall: 是否生成汇总数据

    Returns:
        dict: 同步和生成结果统计
    """
    logger.info("开始执行小红书数据同步和汇总生成")

    # 1. 同步创意数据
    logger.info("第一步：同步创意数据")
    sync_result = await sync_xiaohongshu_creativity_data(
        start_date=start_date, end_date=end_date, advertiser_ids=advertiser_ids
    )

    if not sync_result.get("success"):
        logger.error("创意数据同步失败，停止后续操作")
        return {
            "success": False,
            "sync_result": sync_result,
            "overall_result": None,
            "error": "创意数据同步失败",
        }

    overall_result = None
    if generate_overall:
        # 2. 生成汇总数据
        logger.info("第二步：生成汇总数据")
        overall_result = await generate_xiaohongshu_overall_data_report(
            start_date=start_date, end_date=end_date, force_regenerate=True
        )

        if not overall_result.get("success"):
            logger.warning("汇总数据生成失败，但创意数据同步成功")

    result = {
        "success": True,
        "sync_result": sync_result,
        "overall_result": overall_result,
        "summary": {
            "creativity_processed": sync_result.get("total_processed", 0),
            "creativity_inserted": sync_result.get("total_inserted", 0),
            "creativity_updated": sync_result.get("total_updated", 0),
            "overall_processed": (
                overall_result.get("total_processed", 0) if overall_result else 0
            ),
            "overall_inserted": (
                overall_result.get("total_inserted", 0) if overall_result else 0
            ),
            "overall_updated": (
                overall_result.get("total_updated", 0) if overall_result else 0
            ),
        },
    }

    logger.info(f"小红书数据同步和汇总生成完成: {result['summary']}")
    return result


# ==================== 测试和示例函数 ====================


async def main():
    """
    主函数，用于测试功能
    """
    token_info = await get_access_token()
    if token_info:
        access_token = token_info["access_token"]
        approval_advertisers = token_info["approval_advertisers"]

        print(f"获取到的访问令牌: {access_token}")
        print(f"授权广告主列表: {approval_advertisers}")

        # 测试API调用功能
        try:
            for advertiser in approval_advertisers:
                # 测试获取广告计划列表
                campaign_result = get_campaign_list(
                    access_token,
                    GetCampaignListParams(
                        advertiser_id=advertiser["advertiser_id"],
                        status=6,  # 所有未删除状态
                        page={"page_index": 1, "page_size": 10},
                    ),
                )
                print(
                    f"广告计划列表结果: {json.dumps(campaign_result, ensure_ascii=False, indent=2)}"
                )

                # 测试获取笔记离线报表
                end_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
                start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")

                note_report_result = get_note_offline_report(
                    access_token,
                    GetNoteOfflineReportParams(
                        advertiser_id=advertiser["advertiser_id"],
                        start_date=start_date,
                        end_date=end_date,
                        time_unit="DAY",
                        page_num=1,
                        page_size=10,
                    ),
                )
                print(
                    f"笔记离线报表结果: {json.dumps(note_report_result, ensure_ascii=False, indent=2)}"
                )

        except Exception as e:
            import traceback

            logger.error(f"获取数据失败: {e}")
            logger.error(traceback.format_exc())

        # 测试数据同步功能
        print("\n=== 测试数据同步功能 ===")
        sync_result = await sync_xiaohongshu_creativity_data()
        print(f"同步结果: {sync_result}")

    else:
        logger.error("未能获取访问令牌")


if __name__ == "__main__":
    asyncio.run(main())
