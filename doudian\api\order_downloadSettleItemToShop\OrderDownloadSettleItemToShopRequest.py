# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_downloadSettleItemToShop.param.OrderDownloadSettleItemToShopParam import OrderDownloadSettleItemToShopParam


class OrderDownloadSettleItemToShopRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderDownloadSettleItemToShopParam()

	def getUrlPath(self, ):
		return "/order/downloadSettleItemToShop"

	def getParams(self, ):
		return self.params



