# 🚀 项目说明

## 📦 环境配置

### 🛠️ 安装包管理工具 UV

```bash
pip install uv
```

### 📥 安装项目依赖

<details>
<summary><b>普通环境</b></summary>

```bash
uv pip install .
```

</details>

<details>
<summary><b>Docker 环境</b></summary>

```bash
uv pip install . --system
```

</details>

## ❗ 常见问题

### Windows 硬链接警告

> ⚠️ 如果遇到以下警告：

```
warning: Failed to hardlink files; falling back to full copy. This may lead to degraded performance.
         If the cache and target directories are on different filesystems, hardlinking may not be supported.
         If this is intentional, set `export UV_LINK_MODE=copy` or use `--link-mode=copy` to suppress this warning.
```

**💡 解决方案：**  
添加环境变量：

<details>
<summary><b>Windows</b></summary>

```powershell
$env:UV_LINK_MODE = "copy"
```

</details>

<details>
<summary><b>Linux</b></summary>

```bash
export UV_LINK_MODE=copy
```

</details>

## 🚦 项目运行

### 开发环境

```bash
uv fastapi dev  # 启动开发服务器
```

### 生产环境

```bash
uv run run.py  # 启动生产服务器
```

---

### 📝 注意事项

- 确保已安装所有依赖
- 运行前检查配置文件
- 建议使用虚拟环境

## 🔐 API 密钥验证

### 配置 API 密钥

在配置文件中设置 API 密钥：

```ini
[app]
API_KEY = your_api_key_here
```

### 忽略特定路由的 API 密钥验证

可以通过以下两种方式忽略特定路由的 API 密钥验证：

1. **内置忽略路由**：系统默认忽略以下路由的 API 密钥验证：

   - `/health` - 健康检查
   - `/` - 欢迎页面
   - `/docs` - Swagger 文档
   - `/redoc` - ReDoc 文档
   - `/openapi.json` - OpenAPI 规范

2. **自定义忽略路由前缀**：在配置文件中设置需要忽略的路由前缀：

   ```ini
   [app]
   API_KEY_EXEMPT_PREFIXES = /sms, /public, /api/v1/open
   ```

   以上配置将忽略所有以`/sms`、`/public`或`/api/v1/open`开头的路由的 API 密钥验证。

### 使用 API 密钥

在请求头中添加`X-API-Key`字段：

```
X-API-Key: your_api_key_here
```
