# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.coupons_syncV2.param.CouponsSyncV2Param import CouponsSyncV2Param


class CouponsSyncV2Request(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = CouponsSyncV2Param()

	def getUrlPath(self, ):
		return "/coupons/syncV2"

	def getParams(self, ):
		return self.params



