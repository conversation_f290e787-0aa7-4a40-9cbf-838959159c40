from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi
import json
from fastapi.encoders import jsonable_encoder


def custom_openapi(app: FastAPI):
    """自定义 OpenAPI 配置，移除所有 422 响应，并添加API密钥和JWT验证"""
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # 移除所有路径中的 422 响应
    for path in openapi_schema["paths"].values():
        for operation in path.values():
            if "responses" in operation:
                operation["responses"].pop("422", None)
            # 明确为每个操作设置空的安全要求，移除锁图标
            operation["security"] = []

    # 添加安全方案
    # 确保components键存在
    if "components" not in openapi_schema:
        openapi_schema["components"] = {}

    # 确保securitySchemes键存在
    if "securitySchemes" not in openapi_schema["components"]:
        openapi_schema["components"]["securitySchemes"] = {}

    # 添加安全方案
    openapi_schema["components"]["securitySchemes"] = {
        "ApiKeyAuth": {
            "type": "apiKey",
            "in": "header",
            "name": "X-API-Key",
            "description": "API密钥验证，请在此处输入您的API密钥",
        },
        "JwtAuth": {
            "type": "apiKey",
            "in": "header",
            "name": "X-Access-Token",
            "description": "JWT令牌验证，请在此处输入您的JWT令牌",
        },
    }

    # 移除全局安全要求配置，这样默认情况下不会显示锁图标
    # openapi_schema["security"] = [{"ApiKeyAuth": []}, {"JwtAuth": []}]

    # 为健康检查端点移除安全要求
    if "/health" in openapi_schema["paths"]:
        for method in openapi_schema["paths"]["/health"].values():
            method["security"] = []

    app.openapi_schema = openapi_schema
    return app.openapi_schema


def get_swagger_ui_html(
    *,
    openapi_url: str,
    title: str,
    swagger_js_url: str = "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
    swagger_css_url: str = "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
    swagger_favicon_url: str = "https://fastapi.tiangolo.com/img/favicon.png",
    swagger_ui_parameters=None,
):
    """
    生成自定义的Swagger UI HTML，添加请求拦截器确保认证头被正确添加
    """
    if swagger_ui_parameters is None:
        swagger_ui_parameters = {}

    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
    <link type="text/css" rel="stylesheet" href="{swagger_css_url}">
    <link rel="shortcut icon" href="{swagger_favicon_url}">
    <title>{title}</title>
    </head>
    <body>
    <div id="swagger-ui">
    </div>
    <script src="{swagger_js_url}"></script>
    <!-- `SwaggerUIBundle` is now available on the page -->
    <script>
    const ui = SwaggerUIBundle({{
        url: '{openapi_url}',
    """

    # 添加Swagger UI参数
    for key, value in swagger_ui_parameters.items():
        html += f"{json.dumps(key)}: {json.dumps(jsonable_encoder(value))},\n"

    # 添加请求拦截器，确保认证头被正确添加
    html += """
        requestInterceptor: (request) => {
            // 从localStorage获取存储的认证信息
            const auth = JSON.parse(localStorage.getItem('authorized') || '{}');
            
            // 设置X-API-Key认证头（如果存在）
            if (auth['ApiKeyAuth']) {
                request.headers['X-API-Key'] = auth['ApiKeyAuth']['value'];
            }
            
            // 设置X-Access-Token认证头（如果存在）
            if (auth['JwtAuth']) {
                request.headers['X-Access-Token'] = auth['JwtAuth']['value'];
            }
            
            return request;
        },
        
        // 添加一个自定义的钩子，以在用户授权时保存认证信息
        onComplete: () => {
            // 监听authorize按钮点击
            const authorizeBtn = document.querySelector('.auth-wrapper .authorize');
            if (authorizeBtn) {
                const observer = new MutationObserver(() => {
                    const authBtns = document.querySelectorAll('.auth-btn-wrapper button');
                    if (authBtns && authBtns.length) {
                        // 找到授权按钮
                        const authBtn = Array.from(authBtns).find(btn => btn.innerText.includes('Authorize'));
                        if (authBtn) {
                            authBtn.addEventListener('click', () => {
                                // 在点击授权后，保存认证信息
                                setTimeout(() => {
                                    const authInputs = document.querySelectorAll('.auth-container input');
                                    const auth = JSON.parse(localStorage.getItem('authorized') || '{}');
                                    
                                    authInputs.forEach(input => {
                                        if (input.value && input.dataset.name) {
                                            const scheme = input.closest('.authorization__btn').dataset.name;
                                            auth[scheme] = input.value;
                                        }
                                    });
                                    
                                    localStorage.setItem('authorized', JSON.stringify(auth));
                                }, 500);
                            });
                        }
                    }
                });
                
                observer.observe(document.body, { childList: true, subtree: true });
            }
        },
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
            SwaggerUIBundle.presets.apis,
            SwaggerUIBundle.SwaggerUIStandalonePreset
        ],
        layout: "BaseLayout",
    })
    </script>
    </body>
    </html>
    """

    from fastapi.responses import HTMLResponse

    return HTMLResponse(html)
