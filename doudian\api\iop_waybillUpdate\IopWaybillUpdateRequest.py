# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.iop_waybillUpdate.param.IopWaybillUpdateParam import IopWaybillUpdateParam


class IopWaybillUpdateRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = IopWaybillUpdateParam()

	def getUrlPath(self, ):
		return "/iop/waybillUpdate"

	def getParams(self, ):
		return self.params



