# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_logisticsCompanyList.param.OrderLogisticsCompanyListParam import OrderLogisticsCompanyListParam


class OrderLogisticsCompanyListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderLogisticsCompanyListParam()

	def getUrlPath(self, ):
		return "/order/logisticsCompanyList"

	def getParams(self, ):
		return self.params



