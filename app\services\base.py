from typing import Any, Dict, Optional
import httpx
from app.services.exceptions import ServiceRequestError


class BaseService:
    def __init__(self):
        self.timeout = 30
        self.client = httpx.AsyncClient(timeout=self.timeout)

    async def _request(
        self,
        method: str,
        url: str,
        params: Optional[Dict] = None,
        json: Optional[Dict] = None,
        headers: Optional[Dict] = None,
    ) -> Any:
        """
        通用请求方法
        """
        try:
            response = await self.client.request(
                method=method,
                url=url,
                params=params,
                json=json,
                headers=headers,
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            raise ServiceRequestError(f"请求失败: {str(e)}")

    async def get(
        self, url: str, params: Optional[Dict] = None, headers: Optional[Dict] = None
    ) -> Any:
        return await self._request("GET", url, params=params, headers=headers)

    async def post(
        self,
        url: str,
        json: Optional[Dict] = None,
        params: Optional[Dict] = None,
        headers: Optional[Dict] = None,
    ) -> Any:
        return await self._request(
            "POST", url, json=json, params=params, headers=headers
        )

    async def close(self):
        """
        关闭HTTP客户端
        """
        await self.client.aclose()
