# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.warehouse_createBatch.param.WarehouseCreateBatchParam import WarehouseCreateBatchParam


class WarehouseCreateBatchRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = WarehouseCreateBatchParam()

	def getUrlPath(self, ):
		return "/warehouse/createBatch"

	def getParams(self, ):
		return self.params



