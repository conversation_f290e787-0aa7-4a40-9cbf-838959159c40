# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_onlineChannelProduct.param.ProductOnlineChannelProductParam import ProductOnlineChannelProductParam


class ProductOnlineChannelProductRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductOnlineChannelProductParam()

	def getUrlPath(self, ):
		return "/product/onlineChannelProduct"

	def getParams(self, ):
		return self.params



