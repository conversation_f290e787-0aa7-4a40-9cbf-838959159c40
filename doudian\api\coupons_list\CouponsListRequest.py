# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.coupons_list.param.CouponsListParam import CouponsListParam


class CouponsListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = CouponsListParam()

	def getUrlPath(self, ):
		return "/coupons/list"

	def getParams(self, ):
		return self.params



