# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.buyin_instituteOrderPick.param.BuyinInstituteOrderPickParam import BuyinInstituteOrderPickParam


class BuyinInstituteOrderPickRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = BuyinInstituteOrderPickParam()

	def getUrlPath(self, ):
		return "/buyin/instituteOrderPick"

	def getParams(self, ):
		return self.params



