# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_batchUploadImageSync.param.MaterialBatchUploadImageSyncParam import MaterialBatchUploadImageSyncParam


class MaterialBatchUploadImageSyncRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialBatchUploadImageSyncParam()

	def getUrlPath(self, ):
		return "/material/batchUploadImageSync"

	def getParams(self, ):
		return self.params



