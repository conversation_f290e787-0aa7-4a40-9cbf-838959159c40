# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_newCreateOrder.param.LogisticsNewCreateOrderParam import LogisticsNewCreateOrderParam


class LogisticsNewCreateOrderRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsNewCreateOrderParam()

	def getUrlPath(self, ):
		return "/logistics/newCreateOrder"

	def getParams(self, ):
		return self.params



