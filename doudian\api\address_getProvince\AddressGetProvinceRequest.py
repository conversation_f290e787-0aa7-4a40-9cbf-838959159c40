# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.address_getProvince.param.AddressGetProvinceParam import AddressGetProvinceParam


class AddressGetProvinceRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AddressGetProvinceParam()

	def getUrlPath(self, ):
		return "/address/getProvince"

	def getParams(self, ):
		return self.params



