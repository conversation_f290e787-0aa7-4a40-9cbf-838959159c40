# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_editMaterial.param.MaterialEditMaterialParam import MaterialEditMaterialParam


class MaterialEditMaterialRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialEditMaterialParam()

	def getUrlPath(self, ):
		return "/material/editMaterial"

	def getParams(self, ):
		return self.params



