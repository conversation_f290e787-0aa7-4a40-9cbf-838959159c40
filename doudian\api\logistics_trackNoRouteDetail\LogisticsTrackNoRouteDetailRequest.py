# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_trackNoRouteDetail.param.LogisticsTrackNoRouteDetailParam import LogisticsTrackNoRouteDetailParam


class LogisticsTrackNoRouteDetailRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsTrackNoRouteDetailParam()

	def getUrlPath(self, ):
		return "/logistics/trackNoRouteDetail"

	def getParams(self, ):
		return self.params



