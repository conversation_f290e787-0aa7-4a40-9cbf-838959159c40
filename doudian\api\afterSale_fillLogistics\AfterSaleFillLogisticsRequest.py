# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.afterSale_fillLogistics.param.AfterSaleFillLogisticsParam import AfterSaleFillLogisticsParam


class AfterSaleFillLogisticsRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AfterSaleFillLogisticsParam()

	def getUrlPath(self, ):
		return "/afterSale/fillLogistics"

	def getParams(self, ):
		return self.params



