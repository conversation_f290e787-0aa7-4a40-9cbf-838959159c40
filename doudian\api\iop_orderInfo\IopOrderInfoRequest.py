# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.iop_orderInfo.param.IopOrderInfoParam import IopOrderInfoParam


class IopOrderInfoRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = IopOrderInfoParam()

	def getUrlPath(self, ):
		return "/iop/orderInfo"

	def getParams(self, ):
		return self.params



