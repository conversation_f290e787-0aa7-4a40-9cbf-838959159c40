# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.iop_sellerDistribute.param.IopSellerDistributeParam import IopSellerDistributeParam


class IopSellerDistributeRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = IopSellerDistributeParam()

	def getUrlPath(self, ):
		return "/iop/sellerDistribute"

	def getParams(self, ):
		return self.params



