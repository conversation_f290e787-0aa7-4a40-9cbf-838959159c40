# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_getShopKey.param.LogisticsGetShopKeyParam import LogisticsGetShopKeyParam


class LogisticsGetShopKeyRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsGetShopKeyParam()

	def getUrlPath(self, ):
		return "/logistics/getShopKey"

	def getParams(self, ):
		return self.params



