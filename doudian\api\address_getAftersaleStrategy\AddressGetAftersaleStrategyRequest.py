# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.address_getAftersaleStrategy.param.AddressGetAftersaleStrategyParam import AddressGetAftersaleStrategyParam


class AddressGetAftersaleStrategyRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AddressGetAftersaleStrategyParam()

	def getUrlPath(self, ):
		return "/address/getAftersaleStrategy"

	def getParams(self, ):
		return self.params



