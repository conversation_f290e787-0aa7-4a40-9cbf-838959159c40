# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_salesInherit_permission.param.ProductSalesInheritPermissionParam import ProductSalesInheritPermissionParam


class ProductSalesInheritPermissionRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductSalesInheritPermissionParam()

	def getUrlPath(self, ):
		return "/product/salesInherit/permission"

	def getParams(self, ):
		return self.params



