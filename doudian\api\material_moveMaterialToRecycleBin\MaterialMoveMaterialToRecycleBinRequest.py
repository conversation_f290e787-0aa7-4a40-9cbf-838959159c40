# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_moveMaterialToRecycleBin.param.MaterialMoveMaterialToRecycleBinParam import MaterialMoveMaterialToRecycleBinParam


class MaterialMoveMaterialToRecycleBinRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialMoveMaterialToRecycleBinParam()

	def getUrlPath(self, ):
		return "/material/moveMaterialToRecycleBin"

	def getParams(self, ):
		return self.params



