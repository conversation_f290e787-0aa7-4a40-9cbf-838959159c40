# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_editFolder.param.MaterialEditFolderParam import MaterialEditFolderParam


class MaterialEditFolderRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialEditFolderParam()

	def getUrlPath(self, ):
		return "/material/editFolder"

	def getParams(self, ):
		return self.params



