import hashlib
import json
from fastapi import Depends, Request
from pydantic import BaseModel, Field
from app.core.base_router import BaseRouter
from app.core.config import settings
from app.services.providers.guanyi import GuanyiService
from app.services.exceptions import ServiceError
from app.core.logger import get_logger
from app.core.database import db
from libs.qimen.guanyi import tradeDetailGet, tradeGet, tradeDeliverysDetailGet

logger = get_logger()

router = BaseRouter()


def get_guanyi_service() -> GuanyiService:
    return GuanyiService(
        appkey=settings.GUANYI_APPKEY,
        sessionkey=settings.GUANYI_SESSIONKEY,
        secret=settings.GUANYI_SECRET,
    )


@router.get("/shops")
async def get_shops(service: GuanyiService = Depends(get_guanyi_service)):
    """获取店铺列表"""
    try:
        result = await service.get_shop_list()
        return router.success(data=result)
    except ServiceError as e:
        return router.error(message=str(e))
    finally:
        await service.close()


@router.get("/trades")
async def get_trades(
    page: int = 1, size: int = 100, service: GuanyiService = Depends(get_guanyi_service)
):
    """获取订单列表"""
    try:
        result = await service.get_trade_list(page_no=page, page_size=size)
        return router.success(data=result)
    except ServiceError as e:
        return router.error(message=str(e))
    finally:
        await service.close()


@router.get("/inventory")
async def get_inventory(
    page: int = 1, size: int = 100, service: GuanyiService = Depends(get_guanyi_service)
):
    """获取库存列表"""
    try:
        result = await service.get_inventory_list(page_no=page, page_size=size)
        return router.success(data=result)
    except ServiceError as e:
        return router.error(message=str(e))
    finally:
        await service.close()


class TradeCallback(BaseModel):
    """订单回调"""

    sign: str = Field(alias="sign")
    code: str = Field(alias="code")
    timestamp: str = Field(alias="timeStamp")
    order: dict | None = Field(default=None, alias="order")

    # 验证签名
    def verify_sign(self):
        # 签名规则：
        # 为了防止API调用过程中被黑客恶意篡改，在推送报文中需要增加验签功能, 外部系统对推送的报文需要验签,目前支持的签名方式为：MD5(appsecret+sessionkey+code单据编号+timeStamp当前时间的时间戳+ appsecret)，签名过程如下：
        # 1. 对所有API请求参数（除去sign参数），根据参数名称按字母顺序排序。如：code:1, timeStamp:2排序后的顺序是code:1, timeStamp:2。
        # 2. 将排序好的参数名和参数值拼接在一起，根据上面的示例得到的结果为：code1timeStamp2。
        # 3. 将拼接好的字符串前后直接拼接上appsecret的值，然后使用MD5进行摘要，如：md5(secret+sessionkey+code1timeStamp2+secret)。
        # 4. 将摘要得到的二进制字节流转换成十六进制形式，再转换成大写形式，即是签名字段的值。

        # 获取appsecret
        appsecret = settings.GUANYI_SECRET
        # 获取sessionkey
        sessionkey = settings.GUANYI_SESSIONKEY
        # 获取code
        code = self.code
        # 获取timeStamp
        timestamp = self.timestamp

        order = self.order
        if order:
            order = json.dumps(order, ensure_ascii=False)
        # 拼接字符串
        str = f"{appsecret}{sessionkey}code{code}timeStamp{timestamp}{appsecret}"
        # 计算MD5
        md5 = hashlib.md5(str.encode()).hexdigest().upper()
        return md5 == self.sign


# 订单回调
@router.post("/trade/callback")
async def trade_callback(data: TradeCallback, request: Request):
    """订单回调"""
    # 验证签名
    if not data.verify_sign():
        logger.error("签名验证失败")
        return router.error(message="签名验证失败")
    code = data.code
    gy_data = db["gy_data"]

    # trade = await tradeGet(code)
    # if trade:
    #     collection = gy_data["trade"]
    #     await collection.replace_one({"code": code}, trade, upsert=True)
    detail = await tradeDetailGet(code)
    if detail:
        collection = gy_data["trade_detail"]
        await collection.replace_one({"code": code}, detail, upsert=True)
        if detail.get("delivery_state", 0) > 0:
            trade_delivery = await tradeDeliverysDetailGet(
                {
                    "code": code,
                }
            )
            delivery = trade_delivery.get("delivery", None)
            if delivery:
                delivery_collection = gy_data["trade_delivery"]
                await delivery_collection.replace_one(
                    {"code": code}, delivery, upsert=True
                )

    return {"success": True}
