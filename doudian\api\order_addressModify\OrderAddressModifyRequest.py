# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_addressModify.param.OrderAddressModifyParam import OrderAddressModifyParam


class OrderAddressModifyRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderAddressModifyParam()

	def getUrlPath(self, ):
		return "/order/addressModify"

	def getParams(self, ):
		return self.params



