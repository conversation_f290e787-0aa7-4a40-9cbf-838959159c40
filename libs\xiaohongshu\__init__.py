"""
小红书聚光平台 API 库

提供小红书聚光平台的完整API接口封装，包括：
- 访问令牌管理
- 广告计划查询
- 创意查询
- 离线报表数据获取
"""

from .auth import (
    get_access_token,
    get_access_token_only,
    get_approval_advertisers,
    refresh_access_token,
)

from .api import (
    get_campaign_list,
    get_creativity_search,
    get_creative_offline_report,
    get_note_offline_report,
    get_sub_account_list,
)

from .types import (
    # 通用类型
    XiaohongshuApiResponse,
    TokenInfo,
    AdvertiserInfo,
    PageInfo,
    FilterClause,
    # 广告计划相关
    GetCampaignListParams,
    BaseCampaignDto,
    CampaignListData,
    CampaignListResponse,
    # 创意相关
    GetCreativitySearchParams,
    CreativityDto,
    CreativitySearchData,
    CreativitySearchResponse,
    # 创意离线报表相关
    GetCreativeOfflineReportParams,
    CreativeReportData,
    CreativeOfflineReportData,
    CreativeOfflineReportResponse,
    # 笔记离线报表相关
    GetNoteOfflineReportParams,
    NoteReportData,
    NoteOfflineReportData,
    NoteOfflineReportResponse,
    # 子账号相关
    SubAccountDto,
    SubAccountListData,
    SubAccountListResponse,
    # 类型别名
    ApprovalAdvertisersList,
)

from .constants import BASE_URL

__version__ = "1.0.0"
__author__ = "KOPHENIX DATAPILOT Team"

__all__ = [
    # 认证模块
    "get_access_token",
    "get_access_token_only",
    "get_approval_advertisers",
    "refresh_access_token",
    # API模块
    "get_campaign_list",
    "get_creativity_search",
    "get_creative_offline_report",
    "get_note_offline_report",
    "get_sub_account_list",
    # 类型定义
    "XiaohongshuApiResponse",
    "TokenInfo",
    "AdvertiserInfo",
    "PageInfo",
    "FilterClause",
    "GetCampaignListParams",
    "BaseCampaignDto",
    "CampaignListData",
    "CampaignListResponse",
    "GetCreativitySearchParams",
    "CreativityDto",
    "CreativitySearchData",
    "CreativitySearchResponse",
    "GetCreativeOfflineReportParams",
    "CreativeReportData",
    "CreativeOfflineReportData",
    "CreativeOfflineReportResponse",
    "GetNoteOfflineReportParams",
    "NoteReportData",
    "NoteOfflineReportData",
    "NoteOfflineReportResponse",
    "SubAccountDto",
    "SubAccountListData",
    "SubAccountListResponse",
    "ApprovalAdvertisersList",
    # 常量
    "BASE_URL",
]
