# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_getFolderInfo.param.MaterialGetFolderInfoParam import MaterialGetFolderInfoParam


class MaterialGetFolderInfoRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialGetFolderInfoParam()

	def getUrlPath(self, ):
		return "/material/getFolderInfo"

	def getParams(self, ):
		return self.params



