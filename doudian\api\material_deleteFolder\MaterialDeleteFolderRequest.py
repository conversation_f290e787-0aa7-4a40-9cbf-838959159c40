# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_deleteFolder.param.MaterialDeleteFolderParam import MaterialDeleteFolderParam


class MaterialDeleteFolderRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialDeleteFolderParam()

	def getUrlPath(self, ):
		return "/material/deleteFolder"

	def getParams(self, ):
		return self.params



