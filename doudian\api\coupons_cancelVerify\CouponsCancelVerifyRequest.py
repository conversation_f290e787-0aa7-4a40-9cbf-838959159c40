# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.coupons_cancelVerify.param.CouponsCancelVerifyParam import CouponsCancelVerifyParam


class CouponsCancelVerifyRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = CouponsCancelVerifyParam()

	def getUrlPath(self, ):
		return "/coupons/cancelVerify"

	def getParams(self, ):
		return self.params



