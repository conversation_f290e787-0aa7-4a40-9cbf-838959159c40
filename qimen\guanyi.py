from datetime import datetime, timedelta, timezone
import hashlib
import json
import re
import aiohttp
import urllib.parse
from app.core.config import settings

api_key = "35004015"
api_secret = "59f6b87d8e106aad93566e2705479e25"
endpoint = "https://tp8z6548i2.api.taobao.com/router/qm"


class JsonEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime("%Y-%m-%d %H:%M:%S")
        return super().default(obj)


# 计算 sign
def generate_sign(params: dict, secret: str) -> str:
    """
    生成签名
    """
    # 按参数名称升序排序
    sorted_params = dict(sorted(params.items()))
    # 构造签名字符串
    sign_str = secret
    for k, v in sorted_params.items():
        if v is not None and v != "":
            sign_str += f"{k}{v}"
    sign_str += secret
    # MD5加密
    return hashlib.md5(sign_str.encode()).hexdigest().upper()


number_pattern = re.compile(r"^-?\d+\.\d+$")
datetime_pattern = re.compile(r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$")
# 确保不超过 int 最大位数（8字节整数的最大值约为 2^63-1，即19位数）
int_pattern = re.compile(r"^-?(?:\d{1,18}|9(?:(?:22337203)(?:6854775807)?))$")
IGNORE_FORMAT_FIELDS = ["platform_code", "express_code", "mail_no", "shop_code"]


def _format_data(data: dict) -> dict:
    """
    格式化数据

    递归处理

    字符串 true/false 转换为 bool

    整数串转为 int

    小数串转为 float
    """
    for k, v in data.items():
        if k in IGNORE_FORMAT_FIELDS:
            continue
        if isinstance(v, str):
            if v == "true":
                data[k] = True
            elif v == "false":
                data[k] = False
            elif int_pattern.match(v):
                data[k] = int(v)
            elif number_pattern.match(v):
                data[k] = float(v)
            elif datetime_pattern.match(v):
                t = datetime.strptime(v, "%Y-%m-%d %H:%M:%S")
                # 东八区
                t = t.replace(tzinfo=timezone(timedelta(hours=8)))
                data[k] = t
        elif isinstance(v, dict):
            data[k] = _format_data(v)
        elif isinstance(v, list):
            data[k] = [_format_data(item) for item in v]
    return data


def _get_base_params():
    return {
        "app_key": api_key,
        "sign_method": "md5",
        "v": "2.0",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "format": "json",
        "target_app_key": "21151296",
        "sessionkey": settings.GUANYI_SESSIONKEY,
    }


async def _do_request(params: dict) -> dict | None:
    """
    发送请求
    """
    params = {
        **_get_base_params(),
        **params,
    }
    params["sign"] = generate_sign(params, api_secret)

    async with aiohttp.ClientSession() as session:
        async with session.get(endpoint, params=params) as response:
            text = await response.text()
            resp = json.loads(text)
            _format_data(resp)
            result = resp.get("response", {})
            success = result.get("success", False)
            if not success:
                return None
            return result


async def tradeDetailGet(code: str) -> dict | None:
    """
    通过奇门获取单个管易订单详情
    """
    params = {
        "method": "gy.erp.trade.detail.get",
        "code": code,
    }
    result = await _do_request(params)
    if not result:
        return None
    orderDetail: dict = result.get("orderDetail", {})

    details: list[dict] = orderDetail.get("details", [])
    platform_code: str = str(orderDetail.get("platform_code", ""))
    is_xhs = platform_code.startswith("P")
    for detail in details:
        # 如果是小红书订单，则需要 SKU 从 platform_item_name 里提取
        if is_xhs:
            sku_name = detail.get("platform_item_name", "")
        else:
            sku_name = detail.get("platform_sku_name", "")
        parts = sku_name.split("||")
        if len(parts) > 1:
            sku = parts[0]
            detail["platform_sku"] = sku
    return orderDetail


async def tradeGet(code: str) -> dict | None:
    """
    通过奇门获取单个管易订单
    """
    params = {
        "method": "gy.erp.trade.get",
        "code": code,
    }
    result = await _do_request(params)
    if not result:
        return None
    total = result.get("total", 0)
    if total == 0:
        return None
    orders = result.get("orders", [])
    if not orders:
        return None
    return orders[0]
