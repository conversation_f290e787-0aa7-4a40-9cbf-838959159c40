# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.buyin_queryShopAllianceProducts.param.BuyinQueryShopAllianceProductsParam import BuyinQueryShopAllianceProductsParam


class BuyinQueryShopAllianceProductsRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = BuyinQueryShopAllianceProductsParam()

	def getUrlPath(self, ):
		return "/buyin/queryShopAllianceProducts"

	def getParams(self, ):
		return self.params



