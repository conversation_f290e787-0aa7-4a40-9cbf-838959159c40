# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_updatePostAmount.param.OrderUpdatePostAmountParam import OrderUpdatePostAmountParam


class OrderUpdatePostAmountRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderUpdatePostAmountParam()

	def getUrlPath(self, ):
		return "/order/updatePostAmount"

	def getParams(self, ):
		return self.params



