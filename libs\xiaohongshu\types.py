"""
小红书聚光平台 API 类型定义

包含所有API请求和响应的类型定义，提供完整的类型提示支持。
"""

import dataclasses
from typing import TypedDict, List, Generic, TypeVar

# 通用类型变量
T = TypeVar("T")


class XiaohongshuApiResponse(TypedDict, Generic[T]):
    """小红书API通用响应结构"""

    data: T
    code: int
    success: bool
    msg: str


class AdvertiserInfo(TypedDict):
    """授权广告主信息结构"""

    advertiser_id: int
    advertiser_name: str


class TokenInfo(TypedDict):
    """访问令牌信息结构"""

    access_token: str
    approval_advertisers: List[AdvertiserInfo]


class PageInfo(TypedDict):
    """分页信息结构"""

    page_index: int
    total_count: int


@dataclasses.dataclass
class FilterClause:
    """过滤条件结构"""

    column: str
    """筛选列"""
    operator: str
    """操作符：>（大于）、<（小于）、in（等于）"""
    values: list[str]
    """值"""


# ==================== 广告计划相关类型定义 ====================


@dataclasses.dataclass
class GetCampaignListParams:
    """广告计划查询参数"""

    advertiser_id: int
    """广告主ID - 必填"""
    campaign_ids: list[int] | None = None
    """计划ID列表 - 最多传20个"""
    start_time: str | None = None
    """开始时间，格式：yyyy-MM-dd"""
    expire_time: str | None = None
    """结束时间，格式：yyyy-MM-dd"""
    status: int | None = None
    """计划状态：1-有效 2-暂停 3-已删除 4-计划预算不足 5-现金余额不足 6-所有未删除状态 7-账户日预算不足 8-处于暂停阶段"""
    page: dict | None = None
    """分页参数，包含page_index和page_size"""


class BaseCampaignDto(TypedDict):
    """广告计划基础信息结构"""

    campaign_id: int
    """计划ID"""
    campaign_name: str
    """计划名称"""
    campaign_enable: int
    """计划启用状态：0-暂停，1-启用"""
    campaign_filter_state: int
    """计划状态"""
    campaign_create_time: str
    """计划创建时间"""
    marketing_target: int
    """营销目标"""
    placement: int
    """广告类型"""
    optimize_target: int
    """优化目标"""
    promotion_target: int
    """投放标的"""
    bidding_strategy: int
    """出价策略"""
    constraint_type: int
    """成本控制类型"""
    constraint_value: int
    """成本值"""
    limit_day_budget: int
    """预算类型"""
    campaign_day_budget: int
    """计划日预算（分）"""
    budget_state: int
    """推广计划日预算是否充足"""
    smart_switch: int
    """智能开关"""
    platform: int
    """创建来源"""
    pacing_mode: int
    """投放速率"""
    start_time: str
    """推广开始时间"""
    expire_time: str
    """推广结束时间"""
    time_period: str
    """推广时间的bitmap"""
    time_period_type: int
    """推广时间段类型"""
    feed_flag: int
    """是否开启搜索追投"""
    build_type: int
    """构建类型"""
    creativity_state: int
    """创意聚合状态"""
    event_asset_id: int
    """事件资产"""
    asset_event: int
    """资产事件"""
    asset_event_id: int
    """资产事件ID"""
    page_category: int
    """落地页类型"""
    search_flag: int
    """搜索快投开关"""
    search_bid_ratio: float
    """定向拓展"""
    # 可选字段
    deeplink_id: int | None
    universal_link_id: int | None
    detect_url_link: str | None
    not_available_status: int | None


class CampaignListData(TypedDict):
    """广告计划列表数据结构"""

    page: PageInfo
    base_campaign_dtos: List[BaseCampaignDto]


# ==================== 创意相关类型定义 ====================


@dataclasses.dataclass
class GetCreativitySearchParams:
    """创意查询参数"""

    advertiser_id: int
    """广告主ID"""
    campaign_id: int | None = None
    """计划ID"""
    unit_id: int | None = None
    """单元ID"""
    creativity_ids: list[int] | None = None
    """创意ID集，一次查询不超过20个"""
    status: int | None = None
    """创意状态"""
    start_time: str | None = None
    """开始时间，格式：yyyy-MM-dd"""
    end_time: str | None = None
    """结束时间，格式：yyyy-MM-dd"""
    note_id: str | None = None
    """笔记ID"""
    page: dict | None = None
    """分页参数，包含page_index和page_size"""


class CreativityDto(TypedDict):
    """创意信息结构"""

    advertiser_id: int
    """广告主id"""
    campaign_id: int
    """计划id"""
    unit_id: int
    """单元id"""
    creativity_id: int
    """创意id"""
    creativity_name: str
    """创意名称"""
    creativity_enable: int
    """创意开启状态"""
    creativity_filter_state: int
    """创意状态"""
    creativity_create_time: str
    """创意创建时间"""
    material_type: int
    """物料载体类型"""
    conversion_type: int
    """组件类型"""
    note_id: str
    """笔记id"""
    note_type: int
    """笔记类型"""
    audit_status: int
    """审核状态"""
    programmatic: int
    """是否是程序化创意"""
    # 可选字段
    custom_mask: int | None
    custom_title: int | None
    title_fills: list[str] | None
    mask_gen: int | None
    title_gen: int | None
    mask_prefer: bool | None
    title_mask_prefer: bool | None
    audit_comment: dict[str, str] | None
    page_id: str | None
    click_urls: list[str] | None
    expo_urls: list[str] | None
    jump_url: str | None
    bar_content: str | None
    image: str | None
    item_invalid_reason: int | None
    conversion_component_types: list[int] | None
    comment: str | None
    creativity_extra_info: str | None
    into_shop_param: str | None
    boot_screen_info: dict | None
    poi_id: str | None
    poi_jump_type: str | None
    monitor_company: str | None
    monitor_params: str | None
    item_id: str | None
    title: str | None
    goods_selling_point: str | None
    data_post_url: str | None
    kos_msg_type: int | None
    qual_info: dict | None
    mini_program_path: str | None
    primary_title: str | None
    action_button_content: str | None
    horse_racing_result: str | None


class CreativitySearchData(TypedDict):
    """创意搜索数据结构"""

    page: PageInfo
    creativity_dtos: list[CreativityDto]


# ==================== 创意离线报表相关类型定义 ====================


@dataclasses.dataclass
class GetCreativeOfflineReportParams:
    """创意离线报表查询参数"""

    advertiser_id: int
    """广告主ID - 必填"""
    start_date: str
    """开始时间，格式 yyyy-MM-dd - 必填"""
    end_date: str
    """结束时间，格式 yyyy-MM-dd - 必填"""
    time_unit: str | None = None
    """时间维度：DAY（分天）、HOUR（分时）、SUMMARY（汇总）"""
    marketing_target: list[int] | None = None
    """营销目标过滤条件"""
    bidding_strategy: list[int] | None = None
    """出价方式过滤条件"""
    optimize_target: list[int] | None = None
    """推广目标过滤条件"""
    placement: list[int] | None = None
    """广告类型过滤条件"""
    promotion_target: list[int] | None = None
    """推广标的类型过滤条件"""
    programmatic: list[int] | None = None
    """创意组合方式过滤条件"""
    delivery_mode: list[int] | None = None
    """投放模式过滤条件"""
    split_columns: list[str] | None = None
    """细分条件(相当于group by)"""
    sort_column: str | None = None
    """排序字段"""
    sort: str | None = None
    """升降序：asc（升序）、desc（降序）"""
    page_num: int | None = None
    """页数，默认1"""
    page_size: int | None = None
    """页大小，默认20，最大500"""
    data_caliber: int | None = None
    """数据指标归因时间类型：0-点击时间、1-转化时间"""
    filters: list[FilterClause] | None = None
    """过滤条件"""


class CreativeReportData(TypedDict):
    """创意报表数据结构"""

    # 业务字段
    campaign_id: str | None
    """计划id"""
    campaign_name: str | None
    """计划名称"""
    unit_id: str | None
    """单元id"""
    unit_name: str | None
    """单元名称"""
    creativity_id: str | None
    """创意id"""
    creativity_name: str | None
    """创意名称"""
    creativity_image: str | None
    """创意图片"""
    note_id: str | None
    """笔记id"""
    time: str | None
    """时间"""
    placement: str | None
    """广告类型"""
    optimize_target: str | None
    """优化目标"""
    promotion_target: str | None
    """推广标的"""
    bidding_strategy: str | None
    """出价方式"""
    build_type: str | None
    """搭建类型"""
    marketing_target: str | None
    """营销诉求"""
    page_id: str | None
    """落地页id"""
    item_id: str | None
    """商品id"""
    live_red_id: str | None
    """直播间id"""
    country_name: str | None
    """国家"""
    province: str | None
    """省份"""
    city: str | None
    """城市"""

    # 基础指标
    fee: str | None
    """消费（单位：元）"""
    impression: str | None
    """展现量"""
    click: str | None
    """点击量"""
    ctr: str | None
    """点击率"""
    acp: str | None
    """平均点击成本"""
    cpm: str | None
    """平均千次曝光"""

    # 笔记互动指标
    like: str | None
    """点赞"""
    comment: str | None
    """评论"""
    collect: str | None
    """收藏"""
    follow: str | None
    """关注"""
    share: str | None
    """分享"""
    interaction: str | None
    """互动量"""
    cpi: str | None
    """平均互动成本"""
    action_button_click: str | None
    """行动按钮点击量"""
    action_button_ctr: str | None
    """行动按钮点击率"""
    screenshot: str | None
    """截图"""
    pic_save: str | None
    """保存图片"""
    reserve_pv: str | None
    """预告组件点击"""

    # 更多指标字段...（这里为了简洁省略了其他字段，实际应包含所有字段）


class CreativeOfflineReportData(TypedDict):
    """创意离线报表响应数据结构"""

    data_list: list[CreativeReportData]
    """数据列表"""
    aggregation_data: CreativeReportData | None
    """汇总数据"""


# ==================== 笔记离线报表相关类型定义 ====================


@dataclasses.dataclass
class GetNoteOfflineReportParams:
    """笔记离线报表查询参数"""

    advertiser_id: int
    """广告主ID - 必填"""
    start_date: str
    """开始时间，格式 yyyy-MM-dd - 必填"""
    end_date: str
    """结束时间，格式 yyyy-MM-dd - 必填"""
    time_unit: str | None = None
    """时间维度：DAY（分天）、HOUR（分时）、SUMMARY（汇总）"""
    marketing_target: list[int] | None = None
    """营销目标过滤条件"""
    bidding_strategy: list[int] | None = None
    """出价方式过滤条件"""
    optimize_target: list[int] | None = None
    """推广目标过滤条件"""
    placement: list[int] | None = None
    """广告类型过滤条件"""
    promotion_target: list[int] | None = None
    """推广标的类型过滤条件"""
    programmatic: list[int] | None = None
    """创意组合方式过滤条件"""
    delivery_mode: list[int] | None = None
    """投放模式过滤条件"""
    split_columns: list[str] | None = None
    """细分条件(相当于group by)"""
    sort_column: str | None = None
    """排序字段"""
    sort: str | None = None
    """升降序：asc（升序）、desc（降序）"""
    page_num: int | None = None
    """页数，默认1"""
    page_size: int | None = None
    """页大小，默认20，最大500"""
    data_caliber: int | None = None
    """数据指标归因时间类型：0-点击时间、1-转化时间"""
    filters: list[FilterClause] | None = None
    """过滤条件"""


class NoteReportData(TypedDict):
    """笔记报表数据结构"""

    # 笔记基础信息
    note_id: str | None
    """笔记ID"""
    note_title: str | None
    """标题"""
    note_image: str | None
    """图片"""
    note_jump_url: str | None
    """链接"""
    time: str | None
    """时间"""
    placement: str | None
    """广告类型"""
    optimize_target: str | None
    """优化目标"""
    promotion_target: str | None
    """推广标的"""
    bidding_strategy: str | None
    """出价方式"""
    build_type: str | None
    """搭建类型"""
    marketing_target: str | None
    """营销诉求"""
    page_id: str | None
    """落地页ID"""
    item_id: str | None
    """商品ID"""
    live_red_id: str | None
    """直播间ID"""
    country_name: str | None
    """国家"""
    province: str | None
    """省份"""
    city: str | None
    """城市"""

    # 基础指标
    fee: str | None
    """消费（单位：元）"""
    impression: str | None
    """展现量"""
    click: str | None
    """点击量"""
    ctr: str | None
    """点击率"""
    acp: str | None
    """平均点击成本"""
    cpm: str | None
    """千次曝光成本"""

    # 笔记互动指标
    like: str | None
    """点赞数"""
    comment: str | None
    """评论数"""
    collect: str | None
    """收藏数"""
    follow: str | None
    """关注数"""
    share: str | None
    """分享数"""
    interaction: str | None
    """互动量"""
    cpi: str | None
    """平均互动成本"""
    action_button_click: str | None
    """行动按钮点击量"""
    action_button_ctr: str | None
    """行动按钮点击率"""
    screenshot: str | None
    """截图数"""
    pic_save: str | None
    """保存图片数"""
    reserve_pv: str | None
    """预告组件点击"""

    # 更多指标字段...（实际应包含所有字段）


class NoteOfflineReportData(TypedDict):
    """笔记离线报表响应数据结构"""

    total_count: int
    """总条数"""
    data_list: list[NoteReportData]
    """数据列表"""
    aggregation_data: NoteReportData | None
    """汇总数据"""


# ==================== 子账号相关类型定义 ====================


class SubAccountDto(TypedDict):
    """子账号信息结构"""

    user_id: str
    account_name: str
    # 可以根据实际API返回添加更多字段


class SubAccountListData(TypedDict):
    """子账号列表数据结构"""

    page: PageInfo
    sub_accounts: List[SubAccountDto]


# ==================== 类型别名定义 ====================

ApprovalAdvertisersList = List[AdvertiserInfo]
CampaignListResponse = XiaohongshuApiResponse[CampaignListData]
CreativitySearchResponse = XiaohongshuApiResponse[CreativitySearchData]
CreativeOfflineReportResponse = XiaohongshuApiResponse[CreativeOfflineReportData]
NoteOfflineReportResponse = XiaohongshuApiResponse[NoteOfflineReportData]
SubAccountListResponse = XiaohongshuApiResponse[SubAccountListData]
