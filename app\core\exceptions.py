import sys
import traceback
from fastapi import Request
from fastapi.responses import JSONResponse
from fastapi import HTTPException
from app.core.logger import logger


async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    return JSONResponse(content={"code": exc.status_code, "msg": str(exc.detail)})


async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    exc_type, exc_value, exc_traceback = sys.exc_info()

    # 获取详细的错误信息
    tb_lines = traceback.format_exception(exc_type, exc_value, exc_traceback)
    tb_str = "".join(tb_lines)

    # 获取错误发生的文件和行号
    if exc_traceback:
        filename = exc_traceback.tb_frame.f_code.co_filename
        line_number = exc_traceback.tb_lineno
        function_name = exc_traceback.tb_frame.f_code.co_name

        # 记录详细的错误日志
        logger.error(f"全局异常捕获:")
        logger.error(f"  请求路径: {request.url}")
        logger.error(f"  请求方法: {request.method}")
        logger.error(f"  异常类型: {exc_type.__name__}")
        logger.error(f"  异常信息: {exc_value}")
        logger.error(f"  发生文件: {filename}")
        logger.error(f"  发生行号: {line_number}")
        logger.error(f"  发生函数: {function_name}")
        logger.error(f"  完整堆栈:\n{tb_str}")
    else:
        logger.error(f"全局异常捕获: {exc_type.__name__}: {exc_value}")
        logger.error(f"完整堆栈:\n{tb_str}")

    return JSONResponse(
        content={"code": 500, "msg": f"{exc_type.__name__}: {exc_value}"},
    )
