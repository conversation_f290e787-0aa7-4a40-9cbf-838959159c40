# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.brand_getSug.param.BrandGetSugParam import BrandGetSugParam


class BrandGetSugRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = BrandGetSugParam()

	def getUrlPath(self, ):
		return "/brand/getSug"

	def getParams(self, ):
		return self.params



