#!/usr/bin/env python3
"""
小红书 API 类型提示使用示例

展示如何使用新添加的类型提示来开发类型安全的代码。
"""

import asyncio
from typing import List
from app.services.providers.xiaohongshu import (
    get_access_token,
    get_access_token_only,
    get_approval_advertisers,
    get_campaign_list,
    GetCampaignListParams,
    TokenInfo,
    AdvertiserInfo,
    ApprovalAdvertisersList,
)


async def demonstrate_type_hints():
    """演示类型提示的使用"""
    print("=" * 60)
    print("小红书 API 类型提示使用示例")
    print("=" * 60)

    # 1. 获取完整令牌信息 - 返回 TokenInfo 类型
    print("\n1. 获取完整令牌信息:")
    token_info: TokenInfo | None = await get_access_token()

    if token_info:
        # 类型提示确保我们知道字段类型
        access_token: str = token_info["access_token"]
        approval_advertisers: List[AdvertiserInfo] = token_info["approval_advertisers"]

        print(f"   Access Token: {access_token[:20]}..." if access_token else "None")
        print(f"   授权广告主数量: {len(approval_advertisers)}")

        # 遍历授权广告主 - 类型提示确保字段存在
        for advertiser in approval_advertisers:
            advertiser_id: int = advertiser["advertiser_id"]
            advertiser_name: str = advertiser["advertiser_name"]
            print(f"     - ID: {advertiser_id}, 名称: {advertiser_name}")

    # 2. 单独获取授权广告主列表 - 返回 ApprovalAdvertisersList 类型
    print("\n2. 单独获取授权广告主列表:")
    advertisers: ApprovalAdvertisersList = await get_approval_advertisers()

    for advertiser in advertisers:
        # 类型提示确保字段类型正确
        print(
            f"   广告主: {advertiser['advertiser_name']} (ID: {advertiser['advertiser_id']})"
        )

    # 3. 向后兼容 - 返回 str | None 类型
    print("\n3. 向后兼容用法:")
    access_token_only: str | None = await get_access_token_only()
    if access_token_only:
        print(f"   Access Token Only: {access_token_only[:20]}...")


def process_advertiser_campaigns(advertiser: AdvertiserInfo, access_token: str) -> None:
    """
    处理单个广告主的广告计划

    Args:
        advertiser: 广告主信息，包含 advertiser_id 和 advertiser_name
        access_token: 访问令牌
    """
    try:
        # 类型提示确保字段存在且类型正确
        advertiser_id: int = advertiser["advertiser_id"]
        advertiser_name: str = advertiser["advertiser_name"]

        print(f"正在处理广告主: {advertiser_name} (ID: {advertiser_id})")

        params = GetCampaignListParams(advertiser_id=advertiser_id, page_size=5)

        # 调用 API（这里只是示例，实际环境需要有效数据）
        campaigns = get_campaign_list(access_token, params)
        print(f"  获取到广告计划数据: {type(campaigns)}")

    except Exception as e:
        print(f"  处理失败: {e}")


async def batch_process_advertisers():
    """批量处理所有授权广告主"""
    print("\n" + "=" * 60)
    print("批量处理授权广告主示例")
    print("=" * 60)

    # 获取令牌信息
    token_info: TokenInfo | None = await get_access_token()
    if not token_info:
        print("无法获取访问令牌")
        return

    access_token: str = token_info["access_token"]
    approval_advertisers: List[AdvertiserInfo] = token_info["approval_advertisers"]

    print(f"开始处理 {len(approval_advertisers)} 个授权广告主")

    # 处理每个广告主
    for i, advertiser in enumerate(approval_advertisers, 1):
        print(f"\n[{i}/{len(approval_advertisers)}]", end=" ")
        process_advertiser_campaigns(advertiser, access_token)


def validate_advertiser_structure(data: dict) -> AdvertiserInfo:
    """
    验证并转换广告主数据结构

    Args:
        data: 原始数据字典

    Returns:
        AdvertiserInfo: 验证后的广告主信息

    Raises:
        ValueError: 数据结构不正确时
    """
    required_fields = ["advertiser_id", "advertiser_name"]

    for field in required_fields:
        if field not in data:
            raise ValueError(f"缺少必需字段: {field}")

    if not isinstance(data["advertiser_id"], int):
        raise ValueError("advertiser_id 必须是整数")

    if not isinstance(data["advertiser_name"], str):
        raise ValueError("advertiser_name 必须是字符串")

    # 返回类型安全的 AdvertiserInfo
    return AdvertiserInfo(
        advertiser_id=data["advertiser_id"], advertiser_name=data["advertiser_name"]
    )


async def test_type_safety():
    """测试类型安全"""
    print("\n" + "=" * 60)
    print("类型安全测试")
    print("=" * 60)

    # 测试数据验证
    test_data = [
        {"advertiser_id": 1234, "advertiser_name": "品牌测试账号222"},
        {"advertiser_id": 5678, "advertiser_name": "另一个测试账号"},
    ]

    print("验证测试数据结构:")
    for data in test_data:
        try:
            advertiser: AdvertiserInfo = validate_advertiser_structure(data)
            print(
                f"  ✓ 验证通过: {advertiser['advertiser_name']} (ID: {advertiser['advertiser_id']})"
            )
        except ValueError as e:
            print(f"  ✗ 验证失败: {e}")


async def main():
    """主函数"""
    try:
        await demonstrate_type_hints()
        await batch_process_advertisers()
        await test_type_safety()

        print("\n" + "=" * 60)
        print("类型提示演示完成")
        print("=" * 60)
        print("\n类型提示的优势:")
        print("1. ✓ IDE 自动补全和错误检查")
        print("2. ✓ 编译时类型验证 (使用 mypy 等工具)")
        print("3. ✓ 更好的代码可读性和维护性")
        print("4. ✓ 减少运行时类型错误")
        print("5. ✓ 更好的重构支持")

    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
