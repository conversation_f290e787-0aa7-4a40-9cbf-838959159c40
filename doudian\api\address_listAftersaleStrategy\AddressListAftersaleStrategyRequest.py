# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.address_listAftersaleStrategy.param.AddressListAftersaleStrategyParam import AddressListAftersaleStrategyParam


class AddressListAftersaleStrategyRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AddressListAftersaleStrategyParam()

	def getUrlPath(self, ):
		return "/address/listAftersaleStrategy"

	def getParams(self, ):
		return self.params



