#!/usr/bin/env python3
"""
小红书 API 集成示例

展示如何使用新的 Redis 缓存系统来管理小红书 API 的访问令牌和授权广告主信息。
"""

import asyncio
import json
from typing import List, Dict, Any
from app.services.providers.xiaohongshu import (
    get_access_token,
    get_access_token_only,
    get_approval_advertisers,
    get_campaign_list,
    GetCampaignListParams,
)


class XiaohongshuAPIClient:
    """小红书 API 客户端，演示如何使用缓存的令牌信息"""

    def __init__(self):
        self.access_token = None
        self.approval_advertisers = []

    async def initialize(self):
        """初始化客户端，获取令牌信息"""
        print("正在初始化小红书 API 客户端...")

        token_info = await get_access_token()
        if token_info:
            self.access_token = token_info["access_token"]
            self.approval_advertisers = token_info["approval_advertisers"]
            print(f"✓ 成功获取访问令牌")
            print(f"✓ 授权广告主数量: {len(self.approval_advertisers)}")
            return True
        else:
            print("✗ 获取访问令牌失败")
            return False

    async def get_campaigns_for_all_advertisers(self) -> Dict[int, Any]:
        """为所有授权广告主获取广告计划"""
        if not self.access_token:
            raise ValueError("客户端未初始化，请先调用 initialize()")

        results = {}

        for advertiser_id in self.approval_advertisers:
            print(f"正在获取广告主 {advertiser_id} 的广告计划...")

            try:
                params = GetCampaignListParams(
                    advertiser_id=advertiser_id, page_size=10
                )

                campaign_data = get_campaign_list(self.access_token, params)
                results[advertiser_id] = campaign_data

                # 模拟处理结果
                if campaign_data and "data" in campaign_data:
                    campaign_count = len(campaign_data["data"].get("list", []))
                    print(f"  ✓ 获取到 {campaign_count} 个广告计划")
                else:
                    print(f"  - 暂无广告计划数据")

            except Exception as e:
                print(f"  ✗ 获取失败: {e}")
                results[advertiser_id] = {"error": str(e)}

        return results

    async def refresh_token_info(self):
        """刷新令牌信息（演示缓存更新）"""
        print("正在刷新令牌信息...")

        # 这将触发缓存检查和可能的令牌刷新
        token_info = await get_access_token()
        if token_info:
            old_advertisers = set(self.approval_advertisers)
            self.access_token = token_info["access_token"]
            self.approval_advertisers = token_info["approval_advertisers"]
            new_advertisers = set(self.approval_advertisers)

            # 检查授权广告主是否有变化
            if old_advertisers != new_advertisers:
                added = new_advertisers - old_advertisers
                removed = old_advertisers - new_advertisers

                if added:
                    print(f"✓ 新增授权广告主: {list(added)}")
                if removed:
                    print(f"✓ 移除授权广告主: {list(removed)}")
                if not added and not removed:
                    print("✓ 授权广告主列表无变化")
            else:
                print("✓ 令牌信息已刷新，授权广告主无变化")
        else:
            print("✗ 刷新令牌信息失败")


async def demo_basic_usage():
    """演示基本用法"""
    print("=" * 60)
    print("演示 1: 基本用法")
    print("=" * 60)

    # 获取完整令牌信息
    token_info = await get_access_token()
    if token_info:
        print(
            f"Access Token: {token_info['access_token'][:20]}..."
            if token_info["access_token"]
            else "None"
        )
        print(f"Approval Advertisers: {token_info['approval_advertisers']}")

    # 向后兼容用法
    access_token = await get_access_token_only()
    if access_token:
        print(f"Access Token (兼容模式): {access_token[:20]}...")

    # 单独获取授权广告主
    advertisers = await get_approval_advertisers()
    print(f"授权广告主列表: {advertisers}")


async def demo_advanced_usage():
    """演示高级用法"""
    print("\n" + "=" * 60)
    print("演示 2: 高级用法 - API 客户端")
    print("=" * 60)

    client = XiaohongshuAPIClient()

    # 初始化客户端
    if await client.initialize():
        # 为所有授权广告主获取数据
        print(f"\n正在为 {len(client.approval_advertisers)} 个授权广告主获取数据...")

        if client.approval_advertisers:
            # 只处理前3个广告主作为演示
            demo_advertisers = client.approval_advertisers[:3]

            for advertiser_id in demo_advertisers:
                try:
                    params = GetCampaignListParams(
                        advertiser_id=advertiser_id, page_size=5
                    )

                    print(f"\n正在获取广告主 {advertiser_id} 的广告计划...")
                    # 注意：这里只是演示调用，实际环境中需要有效的 advertiser_id

                except Exception as e:
                    print(f"  ✗ 获取失败: {e}")
        else:
            print("  当前没有授权的广告主")

        # 演示令牌刷新
        print(f"\n演示令牌刷新...")
        await client.refresh_token_info()


async def demo_caching_behavior():
    """演示缓存行为"""
    print("\n" + "=" * 60)
    print("演示 3: 缓存行为测试")
    print("=" * 60)

    import time

    # 第一次调用 - 从数据库/API获取
    print("第一次调用（可能从数据库获取）:")
    start_time = time.time()
    token_info1 = await get_access_token()
    elapsed1 = time.time() - start_time
    print(f"  耗时: {elapsed1:.3f}秒")

    # 第二次调用 - 应该从缓存获取
    print("\n第二次调用（应该从 Redis 缓存获取）:")
    start_time = time.time()
    token_info2 = await get_access_token()
    elapsed2 = time.time() - start_time
    print(f"  耗时: {elapsed2:.3f}秒")

    # 验证数据一致性
    if token_info1 and token_info2:
        tokens_match = token_info1["access_token"] == token_info2["access_token"]
        advertisers_match = (
            token_info1["approval_advertisers"] == token_info2["approval_advertisers"]
        )

        print(f"\n数据一致性检查:")
        print(f"  Access Token 一致: {tokens_match}")
        print(f"  Approval Advertisers 一致: {advertisers_match}")
        print(
            f"  缓存加速比: {elapsed1/elapsed2:.1f}x"
            if elapsed2 > 0
            else "  缓存加速比: N/A"
        )


async def main():
    """主演示函数"""
    print("小红书 Redis 缓存系统演示")
    print("=" * 60)

    try:
        # 演示1: 基本用法
        await demo_basic_usage()

        # 演示2: 高级用法
        await demo_advanced_usage()

        # 演示3: 缓存行为
        await demo_caching_behavior()

    except Exception as e:
        print(f"\n演示过程中发生错误: {e}")
        import traceback

        traceback.print_exc()

    print("\n" + "=" * 60)
    print("演示完成")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
