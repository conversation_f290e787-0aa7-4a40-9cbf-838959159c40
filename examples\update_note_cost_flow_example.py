#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新小红书笔记流量推广费示例

此示例展示如何使用服务来更新笔记的流量推广费字段。
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.update_note_cost_flow import update_note_cost_flow_from_report


async def example_update_all_historical_data():
    """
    示例1：更新所有历史数据（从2020-01-01到昨天）
    """
    print("=== 示例1：更新所有历史数据 ===")

    result = await update_note_cost_flow_from_report(
        start_date="2020-01-01", end_date=None, time_unit="SUMMARY"  # 默认到昨天
    )

    print(f"更新结果：{result}")
    return result


async def example_update_recent_data():
    """
    示例2：更新最近30天的数据
    """
    print("\n=== 示例2：更新最近30天的数据 ===")

    # 计算日期范围
    end_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")

    print(f"更新时间范围：{start_date} 到 {end_date}")

    result = await update_note_cost_flow_from_report(
        start_date=start_date, end_date=end_date, time_unit="SUMMARY"
    )

    print(f"更新结果：{result}")
    return result


async def example_update_specific_month():
    """
    示例3：更新特定月份的数据（例如2024年1月）
    """
    print("\n=== 示例3：更新特定月份的数据 ===")

    start_date = "2024-01-01"
    end_date = "2024-01-31"

    print(f"更新时间范围：{start_date} 到 {end_date}")

    result = await update_note_cost_flow_from_report(
        start_date=start_date, end_date=end_date, time_unit="SUMMARY"
    )

    print(f"更新结果：{result}")
    return result


async def example_daily_update_task():
    """
    示例4：每日更新任务（更新昨天的数据）
    """
    print("\n=== 示例4：每日更新任务 ===")

    # 昨天的日期
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

    print(f"更新昨天的数据：{yesterday}")

    result = await update_note_cost_flow_from_report(
        start_date=yesterday, end_date=yesterday, time_unit="DAY"  # 按天查询
    )

    print(f"更新结果：{result}")
    return result


async def main():
    """
    主函数，运行所有示例
    """
    print("小红书笔记流量推广费更新示例")
    print("=" * 50)

    try:
        # 选择要运行的示例
        print("请选择要运行的示例：")
        print("1. 更新所有历史数据（从2020-01-01到昨天）")
        print("2. 更新最近30天的数据")
        print("3. 更新特定月份的数据（2024年1月）")
        print("4. 每日更新任务（昨天的数据）")
        print("5. 运行所有示例")

        choice = input("请输入选择（1-5）：").strip()

        if choice == "1":
            await example_update_all_historical_data()
        elif choice == "2":
            await example_update_recent_data()
        elif choice == "3":
            await example_update_specific_month()
        elif choice == "4":
            await example_daily_update_task()
        elif choice == "5":
            # 运行所有示例
            await example_daily_update_task()
            await example_update_recent_data()
            await example_update_specific_month()
            # 注意：历史数据更新可能需要较长时间，建议单独运行
            # await example_update_all_historical_data()
        else:
            print("无效的选择")
            return

        print("\n示例运行完成！")

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n运行示例时发生错误：{e}")


if __name__ == "__main__":
    asyncio.run(main())
