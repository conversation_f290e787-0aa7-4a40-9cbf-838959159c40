# Technology Stack

## Core Framework
- **FastAPI**: Modern Python web framework for building APIs
- **Python 3.13+**: Required Python version
- **Uvicorn**: ASGI server for production deployment

## Package Management
- **UV**: Modern Python package manager (preferred over pip)
- **pyproject.toml**: Project configuration and dependencies

## Databases
- **MongoDB**: Primary NoSQL database with Motor async driver
- **MySQL**: Relational database with aiomysql async driver
- **Redis**: Caching and session storage

## Authentication & Security
- **JWT**: Token-based authentication with ES256 algorithm
- **API Key**: Header-based authentication (X-API-Key)
- **CORS**: Cross-origin resource sharing enabled

## External Integrations
- **Alibaba Cloud SMS**: SMS service integration
- **DingTalk**: Notification and messaging
- **Xiaohongshu API**: Social media platform integration
- **GuanYi ERP**: E-commerce management system
- **Doudian/PDD**: E-commerce platforms

## Development Tools
- **Pytest**: Testing framework with async support
- **Coverage**: Code coverage reporting
- **Ruff**: Code linting and formatting
- **Docker**: Containerization

## Common Commands

### Development
```bash
# Install dependencies
uv pip install .

# Start development server
uv fastapi dev

# Run tests
pytest

# Run with coverage
pytest --cov=app --cov-report=term-missing
```

### Production
```bash
# Start production server
uv run run.py

# Docker build
docker build -t datapilot .
```

### Environment Setup
```bash
# Windows - Set UV link mode
$env:UV_LINK_MODE = "copy"

# Linux/Mac - Set UV link mode  
export UV_LINK_MODE=copy
```

## Configuration
- Configuration files in `config/` directory (INI format)
- Environment-specific configs: development.ini, production.ini, test.ini
- Settings loaded via app.core.config module