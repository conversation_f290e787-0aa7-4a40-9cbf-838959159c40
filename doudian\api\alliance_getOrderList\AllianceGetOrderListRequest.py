# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.alliance_getOrderList.param.AllianceGetOrderListParam import AllianceGetOrderListParam


class AllianceGetOrderListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AllianceGetOrderListParam()

	def getUrlPath(self, ):
		return "/alliance/getOrderList"

	def getParams(self, ):
		return self.params



