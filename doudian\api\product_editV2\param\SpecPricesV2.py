# auto generated file
class SpecPricesV2:

	def __init__(self):
		self.multi_time_stocks = None
		self.sku_status = None
		self.sku_classification_type = None
		self.package_sku = None
		self.sku_id = None
		self.gold_process_charge = None
		self.sell_properties = None
		self.supply_price = None
		self.delivery_infos = None
		self.cargo = None
		self.barcodes = None
		self.presell_delay = None
		self.spec_value_lib_id = None
		self.tax_exemption_sku_info = None
		self.stock_num_map = None
		self.sku_type = None
		self.customs_report_info = None
		self.outer_sku_id = None
		self.supplier_id = None
		self.step_stock_num = None
		self.code = None
		self.price = None
		self.stock_num = None




