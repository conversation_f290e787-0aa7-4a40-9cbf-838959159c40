# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_getCategoryPropertyValue.param.ProductGetCategoryPropertyValueParam import ProductGetCategoryPropertyValueParam


class ProductGetCategoryPropertyValueRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductGetCategoryPropertyValueParam()

	def getUrlPath(self, ):
		return "/product/getCategoryPropertyValue"

	def getParams(self, ):
		return self.params



