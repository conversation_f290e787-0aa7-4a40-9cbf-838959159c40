# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_getRecommendedAndDeliveryExpressByOrder.param.LogisticsGetRecommendedAndDeliveryExpressByOrderParam import LogisticsGetRecommendedAndDeliveryExpressByOrderParam


class LogisticsGetRecommendedAndDeliveryExpressByOrderRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsGetRecommendedAndDeliveryExpressByOrderParam()

	def getUrlPath(self, ):
		return "/logistics/getRecommendedAndDeliveryExpressByOrder"

	def getParams(self, ):
		return self.params



