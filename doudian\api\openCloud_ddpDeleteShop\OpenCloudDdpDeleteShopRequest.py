# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.openCloud_ddpDeleteShop.param.OpenCloudDdpDeleteShopParam import OpenCloudDdpDeleteShopParam


class OpenCloudDdpDeleteShopRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OpenCloudDdpDeleteShopParam()

	def getUrlPath(self, ):
		return "/openCloud/ddpDeleteShop"

	def getParams(self, ):
		return self.params



