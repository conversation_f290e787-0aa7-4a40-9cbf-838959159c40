# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_updateOrderAmount.param.OrderUpdateOrderAmountParam import OrderUpdateOrderAmountParam


class OrderUpdateOrderAmountRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderUpdateOrderAmountParam()

	def getUrlPath(self, ):
		return "/order/updateOrderAmount"

	def getParams(self, ):
		return self.params



