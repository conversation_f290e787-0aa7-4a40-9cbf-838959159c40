import os
import uvicorn
import logging
from app.core.config import settings
from xxl.main import register
from multiprocessing import Process

if __name__ == "__main__":

    # 打印所有 settings
    print("Settings:")
    for key, value in settings.__dict__.items():
        print(f"{key}: {value}")

    if settings.APP_ENV != "development":
        # 注册 xxl-job
        process = Process(target=register)
        process.start()

    if settings.APP_ENV == "production":
        # 获取CPU核心数
        cpu_count = os.cpu_count()
        # 使用 50% 的CPU核心数
        workers = int(cpu_count * 0.5)
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8000,
            workers=workers,
            use_colors=True,
            proxy_headers=True,
            forwarded_allow_ips="*",
        )
    else:
        # 启动FastAPI应用
        uvicorn.run("app.main:app", host="0.0.0.0", port=8888)
