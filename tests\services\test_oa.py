import time
import pytest
from sqlalchemy import select
from sqlalchemy.orm import joinedload
from app.core.oa_database import (
    GuanyierpPurchaseArrive,
    ProductionBatch,
    get_session,
    engine,
)
from app.services.providers import product_batch


@pytest.fixture(autouse=True)
async def setup_teardown():
    """在每个测试前后设置和清理"""
    yield
    await engine.dispose()


# pytest -s tests/services/test_oa.py
@pytest.mark.asyncio(loop_scope="session")
async def test_oa_database():
    async with get_session() as session:
        result = await session.execute(select(ProductionBatch))
        data = result.scalars().all()
        assert data is not None


# pytest -s tests/services/test_oa.py::test_product_batch
@pytest.mark.asyncio(loop_scope="session")
async def test_product_batch():
    result = await product_batch.generate()
    print(result)
    assert result is not None


@pytest.mark.asyncio(loop_scope="session")
@pytest.mark.skip(reason="测试用例")
async def test_get_purchase_arrive():
    async with get_session() as session:
        # 首先获取采购到货单
        result = await session.execute(
            select(GuanyierpPurchaseArrive)
            .options(joinedload(GuanyierpPurchaseArrive.arrive_details))
            .limit(1)
        )
        arrive = result.unique().scalars().one()
        print(f"采购到货单ID: {arrive.id}")
        print(f"采购到货单编号: {arrive.code}")
        assert arrive is not None
        # 将明细转换为可序列化的格式
        details_list = []
        for detail in arrive.arrive_details:
            details_list.append(detail.to_dict())
        assert details_list is not None


# pytest -s tests/services/test_oa.py::test_product_batch_allocate
@pytest.mark.asyncio(loop_scope="session")
async def test_product_batch_allocate():
    await product_batch.allocate_to_delivery()

# pytest -s tests/services/test_oa.py::test_re_statistic_and_allocate_negative_batch_number
@pytest.mark.asyncio(loop_scope="session")
async def test_re_statistic_and_allocate_negative_batch_number():
    await product_batch.re_statistic_and_allocate_negative_batch_number()


# pytest -s tests/services/test_oa.py::test_time
def test_time():
    print(int(time.time()))
