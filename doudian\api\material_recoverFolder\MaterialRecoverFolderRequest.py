# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_recoverFolder.param.MaterialRecoverFolderParam import MaterialRecoverFolderParam


class MaterialRecoverFolderRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialRecoverFolderParam()

	def getUrlPath(self, ):
		return "/material/recoverFolder"

	def getParams(self, ):
		return self.params



