#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书创意离线报表接口使用示例

此示例展示如何使用小红书广告API获取创意离线报表数据
"""

import asyncio
import json
from datetime import datetime, timedelta
from app.services.providers.xiaohongshu import (
    get_access_token,
    get_creative_offline_report,
    GetCreativeOfflineReportParams,
    FilterClause,
)


async def main():
    """主函数"""
    print("=== 小红书创意离线报表接口示例 ===\n")

    # 1. 获取访问令牌
    print("1. 获取访问令牌...")
    token_info = await get_access_token()
    if not token_info:
        print("❌ 获取访问令牌失败")
        return

    access_token = token_info["access_token"]
    approval_advertisers = token_info["approval_advertisers"]
    print(f"✅ 成功获取访问令牌")
    print(f"授权广告主数量: {len(approval_advertisers)}")

    if not approval_advertisers:
        print("❌ 没有授权的广告主")
        return

    # 使用第一个广告主进行测试
    advertiser = approval_advertisers[0]
    advertiser_id = advertiser["advertiser_id"]
    print(f"使用广告主: {advertiser['advertiser_name']} (ID: {advertiser_id})\n")

    # 2. 基础查询示例
    print("2. 基础查询示例...")
    try:
        # 查询最近7天的数据
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")

        params = GetCreativeOfflineReportParams(
            advertiser_id=advertiser_id,
            start_date=start_date,
            end_date=end_date,
            time_unit="DAY",  # 按天分组
            page_num=1,
            page_size=10,
        )

        result = get_creative_offline_report(access_token, params)

        if result.get("success"):
            data_list = result["data"]["data_list"]
            print(f"✅ 查询成功，返回 {len(data_list)} 条记录")

            # 显示前3条记录的关键信息
            for i, item in enumerate(data_list[:3]):
                print(f"  记录 {i+1}:")
                print(f"    创意ID: {item.get('creativity_id', 'N/A')}")
                print(f"    创意名称: {item.get('creativity_name', 'N/A')}")
                print(f"    消费: {item.get('fee', '0')} 元")
                print(f"    展现量: {item.get('impression', '0')}")
                print(f"    点击量: {item.get('click', '0')}")
                print(f"    点击率: {item.get('ctr', '0')}%")
        else:
            print(f"❌ 查询失败: {result.get('msg', '未知错误')}")

    except Exception as e:
        print(f"❌ 基础查询异常: {e}")

    print()

    # 3. 带过滤条件的查询示例
    print("3. 带过滤条件的查询示例...")
    try:
        # 查询消费大于0的创意
        filter_clause = FilterClause(column="fee", operator=">", values=["0"])

        params_with_filter = GetCreativeOfflineReportParams(
            advertiser_id=advertiser_id,
            start_date=start_date,
            end_date=end_date,
            time_unit="SUMMARY",  # 汇总数据
            filters=[filter_clause],
            sort_column="fee",  # 按消费排序
            sort="desc",  # 降序
            page_num=1,
            page_size=5,
        )

        result = get_creative_offline_report(access_token, params_with_filter)

        if result.get("success"):
            data_list = result["data"]["data_list"]
            aggregation_data = result["data"].get("aggregation_data")

            print(f"✅ 过滤查询成功，返回 {len(data_list)} 条记录")

            if aggregation_data:
                print("  汇总数据:")
                print(f"    总消费: {aggregation_data.get('fee', '0')} 元")
                print(f"    总展现量: {aggregation_data.get('impression', '0')}")
                print(f"    总点击量: {aggregation_data.get('click', '0')}")

            # 显示前3条记录
            for i, item in enumerate(data_list[:3]):
                print(f"  记录 {i+1}:")
                print(f"    创意ID: {item.get('creativity_id', 'N/A')}")
                print(f"    消费: {item.get('fee', '0')} 元")
                print(f"    ROI: {item.get('roi', '0')}")
        else:
            print(f"❌ 过滤查询失败: {result.get('msg', '未知错误')}")

    except Exception as e:
        print(f"❌ 过滤查询异常: {e}")

    print()

    # 4. 细分维度查询示例
    print("4. 细分维度查询示例...")
    try:
        # 按广告类型细分
        params_split = GetCreativeOfflineReportParams(
            advertiser_id=advertiser_id,
            start_date=start_date,
            end_date=end_date,
            time_unit="SUMMARY",
            split_columns=["placement"],  # 按广告类型细分
            page_num=1,
            page_size=10,
        )

        result = get_creative_offline_report(access_token, params_split)

        if result.get("success"):
            data_list = result["data"]["data_list"]
            print(f"✅ 细分查询成功，返回 {len(data_list)} 条记录")

            # 按广告类型分组显示
            placement_groups = {}
            for item in data_list:
                placement = item.get("placement", "未知")
                if placement not in placement_groups:
                    placement_groups[placement] = []
                placement_groups[placement].append(item)

            for placement, items in placement_groups.items():
                total_fee = sum(float(item.get("fee", 0)) for item in items)
                print(
                    f"  广告类型 {placement}: {len(items)} 条记录，总消费 {total_fee:.2f} 元"
                )
        else:
            print(f"❌ 细分查询失败: {result.get('msg', '未知错误')}")

    except Exception as e:
        print(f"❌ 细分查询异常: {e}")

    print()

    # 5. 多条件过滤查询示例
    print("5. 多条件过滤查询示例...")
    try:
        # 查询消费大于0且点击量大于0的创意
        filters = [
            FilterClause(column="fee", operator=">", values=["0"]),
            FilterClause(column="click", operator=">", values=["0"]),
        ]

        params_multi_filter = GetCreativeOfflineReportParams(
            advertiser_id=advertiser_id,
            start_date=start_date,
            end_date=end_date,
            time_unit="DAY",
            filters=filters,
            marketing_target=[3, 4],  # 只查询商品推广和产品种草
            placement=[1, 2],  # 只查询信息流和搜索推广
            sort_column="ctr",  # 按点击率排序
            sort="desc",
            page_num=1,
            page_size=5,
        )

        result = get_creative_offline_report(access_token, params_multi_filter)

        if result.get("success"):
            data_list = result["data"]["data_list"]
            print(f"✅ 多条件查询成功，返回 {len(data_list)} 条记录")

            for i, item in enumerate(data_list):
                print(f"  记录 {i+1}:")
                print(f"    创意ID: {item.get('creativity_id', 'N/A')}")
                print(f"    广告类型: {item.get('placement', 'N/A')}")
                print(f"    营销目标: {item.get('marketing_target', 'N/A')}")
                print(f"    点击率: {item.get('ctr', '0')}%")
                print(f"    消费: {item.get('fee', '0')} 元")
        else:
            print(f"❌ 多条件查询失败: {result.get('msg', '未知错误')}")

    except Exception as e:
        print(f"❌ 多条件查询异常: {e}")

    print("\n=== 示例完成 ===")


if __name__ == "__main__":
    asyncio.run(main())
