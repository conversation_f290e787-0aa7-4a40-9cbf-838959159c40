pipeline {
    agent any

    options {
        retry(3)
    }
    
    environment {
        // 定义环境变量
        DOCKER_REGISTRY = 'crpi-rva6wk7sol3hb2na.cn-hangzhou.personal.cr.aliyuncs.com'
        IMAGE_NAME = 'kophenix/datapilot'
        IMAGE_TAG = "latest"
        // Docker登录信息
        DOCKER_USERNAME = 'aliyun1349305028'
        DOCKER_PASSWORD = 'kfc123456'
    }
    
    stages {
        stage('Checkout') {
            steps {
                sshagent(credentials: ['a23fd7fa-a9bc-46b9-8dca-0aaf501648b5']) {
                    checkout scm
                }
            }
        }
        
        stage('Build Docker Image') {
            steps {
                script {
                    sh "docker build -t ${DOCKER_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG} ."
                }
            }
        }
        
        stage('Push Docker Image') {
            steps {
                script {
                    // 使用账号密码登录 Docker 仓库
                    sh """
                        echo ${DOCKER_PASSWORD} | docker login ${DOCKER_REGISTRY} -u ${DOCKER_USERNAME} --password-stdin
                        docker push ${DOCKER_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}
                    """
                }
            }
        }
    }
    
    post {
        success {
            script {
                def durationStr = getBuildDuration()
                sh """
                    curl -X POST https://sctapi.ftqq.com/SCT245700TA-eE9nljTZ2VfC80BEUxXMxGMk.send \
                    -H 'Content-Type: application/json' \
                    -d '{"title": "${IMAGE_NAME}:${IMAGE_TAG} 构建成功", "desp": "耗时: ${durationStr}"}'
                """
            }
        }
        
        failure {
            script {
                def durationStr = getBuildDuration()
                sh """
                    curl -X POST https://sctapi.ftqq.com/SCT245700TA-eE9nljTZ2VfC80BEUxXMxGMk.send \
                    -H 'Content-Type: application/json' \
                    -d '{"title": "${IMAGE_NAME}:${IMAGE_TAG} 构建失败", "desp": "耗时: ${durationStr}"}'
                """
            }
        }
        
        always {
            sh "docker rmi ${DOCKER_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG} || true"
            sh "docker logout ${DOCKER_REGISTRY} || true"
        }
    }
}

// 在pipeline外部定义函数
def getBuildDuration() {
    def durationInSeconds = currentBuild.duration / 1000
    def minutes = (durationInSeconds / 60).intValue()
    def seconds = (durationInSeconds - (minutes * 60)).intValue()
    return "${minutes}分${seconds}秒"
}

