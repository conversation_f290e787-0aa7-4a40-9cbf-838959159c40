# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.buyin_allKolStrategyCreate.param.BuyinAllKolStrategyCreateParam import BuyinAllKolStrategyCreateParam


class BuyinAllKolStrategyCreateRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = BuyinAllKolStrategyCreateParam()

	def getUrlPath(self, ):
		return "/buyin/allKolStrategyCreate"

	def getParams(self, ):
		return self.params



