# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.iop_waybillCancel.param.IopWaybillCancelParam import IopWaybillCancelParam


class IopWaybillCancelRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = IopWaybillCancelParam()

	def getUrlPath(self, ):
		return "/iop/waybillCancel"

	def getParams(self, ):
		return self.params



