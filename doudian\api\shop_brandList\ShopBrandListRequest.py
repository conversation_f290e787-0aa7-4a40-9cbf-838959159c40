# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.shop_brandList.param.ShopBrandListParam import ShopBrandListParam


class ShopBrandListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ShopBrandListParam()

	def getUrlPath(self, ):
		return "/shop/brandList"

	def getParams(self, ):
		return self.params



