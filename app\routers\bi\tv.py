import asyncio
import re
from fastapi import Request
from motor.motor_asyncio import AsyncIOMotorCollection
from sqlalchemy import func, select
from app.core.base_router import BaseRouter
from app.core.database import db
from app.core.oa_database import BusinessAnnualPlanning, PddMall, get_session
from datetime import datetime, timedelta, timezone
import json
from app.schemas.response import ResponseModel
from libs.gateway import auth
from libs.oa import internal
import dateutil.relativedelta
from pydantic import BaseModel, Field
from typing import List, Dict, Any
from app.utils.tv_utils import (
    tv_cache_redis,
    calculate_increment_rate,
    calculate_increment_multiplier,
    format_float,
    get_today_start,
    calculate_group_rankings,
    process_group_champion_counts,
    tz,
)

# 常量定义
router = BaseRouter()
regex = {"DATE": r"(\d{4}-\d{2}-\d{2})"}

# 需要屏蔽的部门列表
BLOCKED_DEPARTMENTS = ["供应链部"]

# 需要屏蔽的小组列表
BLOCKED_GROUPS = ["其他分组"]

# 小组别名映射配置
GROUP_ALIAS_MAP = {
    # 格式: "部门名称|小组名称": "别名"
    "广州事业部|经销1组": "线下经销组"
}


def filter_blocked_departments(data_list, department_key="department"):
    """过滤屏蔽的部门数据"""
    return [
        item
        for item in data_list
        if item.get(department_key) not in BLOCKED_DEPARTMENTS
    ]


def filter_blocked_departments_dict(data_dict):
    """过滤屏蔽的部门数据（字典格式）"""
    return {k: v for k, v in data_dict.items() if k not in BLOCKED_DEPARTMENTS}


def apply_group_alias(group_name, department_name):
    """应用小组别名映射

    Args:
        group_name: 原始小组名称
        department_name: 部门名称（可能是字符串、列表或None）

    Returns:
        str: 应用别名后的小组名称
    """
    # 处理部门名称格式
    if isinstance(department_name, list):
        dept_names = department_name
    elif isinstance(department_name, str):
        dept_names = [dept.strip() for dept in department_name.split(",")]
    else:
        # 如果 department_name 为 None 或其他类型，直接返回原小组名称
        return group_name

    # 检查每个部门是否有对应的别名映射
    for dept_name in dept_names:
        alias_key = f"{dept_name}|{group_name}"
        if alias_key in GROUP_ALIAS_MAP:
            return GROUP_ALIAS_MAP[alias_key]

    return group_name


def filter_blocked_groups(groups_data):
    """过滤属于屏蔽部门和屏蔽小组的数据"""
    filtered_groups = []
    for group in groups_data:
        # 检查小组名称是否在屏蔽列表中
        group_name = group.get("mallGroup", "")
        if group_name in BLOCKED_GROUPS:
            continue

        # 检查小组所属的部门是否包含被屏蔽的部门
        group_departments = group.get("department", [])
        if group_departments is None:
            group_departments = []
        elif isinstance(group_departments, str):
            group_departments = group_departments.split(",")

        # 如果小组的任何一个部门在屏蔽列表中，则过滤掉该小组
        # 增加对 None 值的检查
        if not any(
            dept and dept.strip() in BLOCKED_DEPARTMENTS for dept in group_departments
        ):
            filtered_groups.append(group)

    return filtered_groups


def merge_innovation_department_groups(groups_data):
    """合并创新事业部的所有小组数据为一个"整体"小组"""
    innovation_groups = []
    other_groups = []

    # 分离创新事业部的小组和其他小组
    for group in groups_data:
        group_departments = group.get("department", [])
        if isinstance(group_departments, str):
            group_departments = group_departments.split(",")

        # 检查是否属于创新事业部
        # 增加对 None 值的检查
        if any(dept and dept.strip() == "创新事业部" for dept in group_departments):
            innovation_groups.append(group)
        else:
            other_groups.append(group)

    # 如果没有创新事业部的小组，直接返回原数据
    if not innovation_groups:
        return groups_data

    # 合并创新事业部的小组数据
    merged_group = {
        "mallGroup": "整体",
        "department": ["创新事业部"],
        "total": 0,
        "dest": 0,
    }

    # 合并所有数值字段
    for group in innovation_groups:
        # 合并总销售额
        if "total" in group and group["total"] is not None:
            try:
                merged_group["total"] += float(group["total"])
            except (ValueError, TypeError):
                pass

        # 合并目标销售额
        if "dest" in group and group["dest"] is not None:
            try:
                merged_group["dest"] += float(group["dest"])
            except (ValueError, TypeError):
                pass

        # 合并日期字段（如果存在）
        for key, value in group.items():
            if key.startswith("2025-") and value is not None:
                if key not in merged_group:
                    merged_group[key] = 0
                try:
                    merged_group[key] += float(value)
                except (ValueError, TypeError):
                    pass

    # 格式化合并后的数值
    merged_group["total"] = f"{merged_group['total']:.2f}"
    if merged_group["dest"] > 0:
        merged_group["dest"] = f"{merged_group['dest']:.4f}"
    else:
        merged_group["dest"] = None

    # 格式化日期字段
    for key in merged_group:
        if key.startswith("2025-") and isinstance(merged_group[key], (int, float)):
            if merged_group[key] == 0:
                merged_group[key] = None
            else:
                merged_group[key] = f"{merged_group[key]:.2f}"

    # 复制其他必要字段（使用第一个小组的值作为默认值）
    if innovation_groups:
        first_group = innovation_groups[0]
        for key in [
            "mallCompany",
            "platform",
            "mallId",
            "mallName",
            "gyCode",
            "order",
            "enable",
            "mallStatus",
            "progress",
            "directSalesType",
        ]:
            if key in first_group:
                merged_group[key] = first_group[key]

    # 返回合并后的数据：其他小组 + 创新事业部整体小组
    return other_groups + [merged_group]


def merge_innovation_groups_for_ranking(groups_data, previous_period_data=None):
    """
    为排名计算合并创新事业部小组数据
    在计算增量百分比时，如果是新小组（上个周期没有数据），返回空值（null）
    """
    innovation_groups = []
    other_groups = []

    # 分离创新事业部的小组和其他小组
    for group in groups_data:
        group_departments = group.get("department", [])
        if isinstance(group_departments, str):
            group_departments = group_departments.split(",")

        # 检查是否属于创新事业部
        # 增加对 None 值的检查
        if any(dept and dept.strip() == "创新事业部" for dept in group_departments):
            innovation_groups.append(group)
        else:
            other_groups.append(group)

    # 如果没有创新事业部的小组，直接返回原数据
    if not innovation_groups:
        return groups_data

    # 检查上个周期是否有创新事业部的数据
    has_previous_data = False
    if previous_period_data:
        for prev_group in previous_period_data:
            prev_departments = prev_group.get("department", [])
            if isinstance(prev_departments, str):
                prev_departments = prev_departments.split(",")
            # 增加对 None 值的检查
            if any(dept and dept.strip() == "创新事业部" for dept in prev_departments):
                has_previous_data = True
                break

    # 合并创新事业部的小组数据
    merged_group = {
        "mallGroup": "整体",
        "department": ["创新事业部"],
        "total": 0,
        "dest": 0,
        "_is_new_group": not has_previous_data,  # 标记是否为新小组
    }

    # 合并所有数值字段
    for group in innovation_groups:
        # 合并总销售额
        if "total" in group and group["total"] is not None:
            try:
                merged_group["total"] += float(group["total"])
            except (ValueError, TypeError):
                pass

        # 合并目标销售额
        if "dest" in group and group["dest"] is not None:
            try:
                merged_group["dest"] += float(group["dest"])
            except (ValueError, TypeError):
                pass

        # 合并日期字段（如果存在）
        for key, value in group.items():
            if key.startswith("2025-") and value is not None:
                if key not in merged_group:
                    merged_group[key] = 0
                try:
                    merged_group[key] += float(value)
                except (ValueError, TypeError):
                    pass

    # 格式化合并后的数值
    merged_group["total"] = f"{merged_group['total']:.2f}"
    if merged_group["dest"] > 0:
        merged_group["dest"] = f"{merged_group['dest']:.4f}"
    else:
        merged_group["dest"] = None

    # 格式化日期字段
    for key in merged_group:
        if key.startswith("2025-") and isinstance(merged_group[key], (int, float)):
            if merged_group[key] == 0:
                merged_group[key] = None
            else:
                merged_group[key] = f"{merged_group[key]:.2f}"

    # 复制其他必要字段（使用第一个小组的值作为默认值）
    if innovation_groups:
        first_group = innovation_groups[0]
        for key in [
            "mallCompany",
            "platform",
            "mallId",
            "mallName",
            "gyCode",
            "order",
            "enable",
            "mallStatus",
            "progress",
            "directSalesType",
        ]:
            if key in first_group:
                merged_group[key] = first_group[key]

    # 返回合并后的数据：其他小组 + 创新事业部整体小组
    return other_groups + [merged_group]


def calculate_filtered_total_summary(performance_data):
    """计算排除屏蔽部门后的总业绩汇总"""
    # 获取所有部门数据，排除屏蔽的部门
    filtered_dept_data = [
        item
        for item in performance_data["data"]["mallDepTotal"]
        if item["department"] not in BLOCKED_DEPARTMENTS
    ]

    # 计算总销售额和目标
    total_amount = sum(format_float(item["total"]) for item in filtered_dept_data)
    total_dest = sum(format_float(item["dest"]) for item in filtered_dept_data)

    # 计算完成率
    rate = total_amount / total_dest if total_dest > 0 else 0

    return {"total": total_amount, "dest": total_dest, "rate": rate}


def calculate_filtered_yearly_summary(full_year_data, current_month):
    """计算排除屏蔽部门后的年度总业绩汇总（用于月初查询）"""
    # 获取所有部门数据，排除屏蔽的部门
    filtered_dept_data = [
        item
        for item in full_year_data["data"]["mallDepTotal"]
        if item["department"] not in BLOCKED_DEPARTMENTS
    ]

    year = current_month.year
    total_amount = 0
    total_dest = 0

    # 计算到当前月之前的累计销售额
    for item in filtered_dept_data:
        for i in range(1, current_month.month):
            total_amount += format_float(item.get(f"{year}-{i:02d}", 0))
        total_dest += format_float(item.get("dest", 0))

    # 计算完成率
    rate = total_amount / total_dest if total_dest > 0 else 0

    return {"total": total_amount, "dest": total_dest, "rate": rate}


def calculate_filtered_v2_summary(
    current_month_data, last_month_data, two_months_ago_data
):
    """计算排除屏蔽部门后的V2版本总业绩汇总"""
    # 当前月
    current_filtered = [
        item
        for item in current_month_data["data"]["mallDepTotal"]
        if item["department"] not in BLOCKED_DEPARTMENTS
    ]
    current_total = sum(format_float(item["total"]) for item in current_filtered)
    current_dest = sum(format_float(item["dest"]) for item in current_filtered)

    # 上月
    last_filtered = [
        item
        for item in last_month_data["data"]["mallDepTotal"]
        if item["department"] not in BLOCKED_DEPARTMENTS
    ]
    last_total = sum(format_float(item["total"]) for item in last_filtered)

    # 前月
    two_months_ago_filtered = [
        item
        for item in two_months_ago_data["data"]["mallDepTotal"]
        if item["department"] not in BLOCKED_DEPARTMENTS
    ]
    two_months_ago_total = sum(
        format_float(item["total"]) for item in two_months_ago_filtered
    )

    # 计算完成率和增长率
    rate = current_total / current_dest if current_dest > 0 else 0
    month_increment_rate = calculate_increment_rate(current_total, last_total)

    return {
        "total": current_total,
        "dest": current_dest,
        "rate": rate,
        "last_month_total": last_total,
        "two_months_ago_total": two_months_ago_total,
        "month_increment_rate": month_increment_rate,
    }


# 数据模型
class SummaryModel(BaseModel):
    """汇总数据模型"""

    total: float = Field(..., description="总销售额")
    dest: float = Field(..., description="目标销售额")
    rate: float = Field(..., description="完成率")


class DepartmentDailyModel(BaseModel):
    """部门每日数据模型"""

    department: str = Field(..., description="部门名称")
    total: float = Field(..., description="部门销售额")
    dest: float = Field(..., description="部门目标销售额")
    rate: float = Field(..., description="部门完成率")


class GroupDailyModel(BaseModel):
    """小组每日数据模型"""

    group: str = Field(..., description="小组名称")
    department: str = Field(..., description="所属部门")
    total: float = Field(..., description="小组销售额")
    dest: float = Field(..., description="小组目标销售额")
    rate: float = Field(..., description="小组完成率")
    champion_count: int = Field(..., description="冠军次数")


class DailyResponseModel(BaseModel):
    """每日数据响应模型"""

    summary: SummaryModel = Field(..., description="总体汇总数据")
    departments: List[DepartmentDailyModel] = Field(..., description="部门数据列表")
    groups: List[GroupDailyModel] = Field(..., description="小组数据列表")


class DepartmentMonthlyModel(BaseModel):
    """部门月度数据模型"""

    department: str = Field(..., description="部门名称")
    total: float = Field(..., description="部门销售额")
    increment: float = Field(..., description="增量")


class GroupMonthlyModel(BaseModel):
    """小组月度数据模型"""

    group: str = Field(..., description="小组名称")
    department: str = Field(..., description="所属部门")
    rank: int = Field(..., description="排名")
    rank_change: int = Field(..., description="排名变化")
    increment_rate: float | None = Field(
        ..., description="增长倍数（1+增长率），新小组为空"
    )


class GroupMonthlyDebugModel(BaseModel):
    """小组月度数据模型（包含调试信息）"""

    group: str = Field(..., description="小组名称")
    department: str = Field(..., description="所属部门")
    rank: int = Field(..., description="排名")
    rank_change: int = Field(..., description="排名变化")
    increment_rate: float | None = Field(
        ..., description="增长倍数（1+增长率），新小组为空"
    )
    last_month_total: float = Field(..., description="上月业绩")
    two_months_ago_total: float = Field(..., description="前月业绩")


class MonthlyResponseModel(BaseModel):
    """月度数据响应模型"""

    summary: SummaryModel = Field(..., description="总体汇总数据")
    departments: List[DepartmentMonthlyModel] = Field(..., description="部门数据列表")
    groups: List[GroupMonthlyModel] = Field(..., description="小组数据列表")


class SummaryV2Model(BaseModel):
    """汇总数据V2模型"""

    total: float = Field(..., description="当前月总销售额")
    dest: float = Field(..., description="目标销售额")
    rate: float = Field(..., description="完成率")
    last_month_total: float = Field(..., description="上月总销售额")
    two_months_ago_total: float = Field(..., description="前月总销售额")
    month_increment_rate: float = Field(..., description="月度增长率")
    current_week_total: float = Field(..., description="当前周总销售额")
    last_week_total: float = Field(..., description="上周总销售额")
    two_weeks_ago_total: float = Field(..., description="前周总销售额")
    week_increment_rate: float = Field(..., description="周度增长率")


class DepartmentV2Model(BaseModel):
    """部门V2数据模型"""

    department: str = Field(..., description="部门名称")
    current_total: float = Field(..., description="当前月部门销售额")
    last_total: float = Field(..., description="上月部门销售额")
    increment: float = Field(..., description="增量")


class MonthlyV2ResponseModel(BaseModel):
    """月度数据V2响应模型"""

    summary: SummaryV2Model = Field(..., description="总体汇总数据")
    departments: List[DepartmentV2Model] = Field(..., description="部门数据列表")
    groups: List[GroupMonthlyModel] = Field(..., description="小组数据列表")


class MonthlyV2DebugResponseModel(BaseModel):
    """月度数据V2响应模型（包含调试信息）"""

    summary: SummaryV2Model = Field(..., description="总体汇总数据")
    departments: List[DepartmentV2Model] = Field(..., description="部门数据列表")
    groups: List[GroupMonthlyDebugModel] = Field(
        ..., description="小组数据列表（包含调试信息）"
    )


# 业务函数
@tv_cache_redis()
async def get_department_monthly_sales_target() -> List[Dict[str, Any]]:
    """获取部门当前月份目标销售额及销售额"""
    now = datetime.now(tz=tz)
    async with get_session() as session:
        query = (
            select(
                PddMall.caiwuDept.label("deparment"),
                func.sum(BusinessAnnualPlanning.shop_sales_volume_goal).label("goal"),
            )
            .join(
                BusinessAnnualPlanning,
                PddMall.gyCode == BusinessAnnualPlanning.shop_code,
            )
            .where(
                BusinessAnnualPlanning.month == now.month,
                BusinessAnnualPlanning.year == now.year,
                PddMall.caiwuDept.notin_(BLOCKED_DEPARTMENTS),
            )
            .group_by(PddMall.caiwuDept)
        )
        deparments = await session.execute(query)
        return deparments.scalars().all()


@tv_cache_redis()
async def get_monthly_summary_for_first_day(today: datetime) -> Dict[str, Any]:
    """获取月度汇总(每月首日特殊查询)"""
    current_month = today.replace(day=1)
    year = current_month.year
    full_year_data = await internal.get_performance(f"{year}")

    # 处理部门数据
    deparments_data = {
        item["department"]: item
        for item in full_year_data["data"]["mallDepTotal"]
        if item["department"] not in BLOCKED_DEPARTMENTS
    }
    deparments = {}
    for department, data in deparments_data.items():
        previous_2_month_amount = 0
        previous_month_amount = 0
        for i in range(1, current_month.month):
            if i < (current_month.month - 1):
                previous_2_month_amount += format_float(data.get(f"{year}-{i:02d}", 0))
            previous_month_amount += format_float(data.get(f"{year}-{i:02d}", 0))
        deparments[department] = {
            "department": department,
            "total": previous_month_amount,
            "increment": round(previous_month_amount - previous_2_month_amount, 2),
        }

    # 处理小组数据
    filtered_group_data = filter_blocked_groups(
        full_year_data["data"]["mallGroupTotal"]
    )
    # 合并创新事业部的小组数据
    merged_group_data = merge_innovation_department_groups(filtered_group_data)
    groups_data = {item["mallGroup"]: item for item in merged_group_data}
    groups = {}
    for group, data in groups_data.items():
        previous_3_month_amount = 0
        previous_2_month_amount = 0
        previous_month_amount = 0
        for i in range(1, current_month.month):
            if i < (current_month.month - 2):
                previous_3_month_amount += format_float(data.get(f"{year}-{i:02d}", 0))
            if i < (current_month.month - 1):
                previous_2_month_amount += format_float(data.get(f"{year}-{i:02d}", 0))
            previous_month_amount += format_float(data.get(f"{year}-{i:02d}", 0))

        groups[group] = {
            "department": (
                ",".join([dept for dept in data["department"] if dept is not None])
                if isinstance(data["department"], list)
                else (data["department"] or "")
            ),
            "group": group,
            "previous_2_month_rank": 0,
            "previous_2_month_increment_rate": calculate_increment_rate(
                previous_2_month_amount, previous_3_month_amount
            ),
            "rank": 0,
            "rank_change": 0,
            "increment_rate": calculate_increment_rate(
                previous_month_amount, previous_2_month_amount
            ),
        }

    # 处理排名
    groups_array = calculate_group_rankings(
        list(groups.values()), "previous_2_month_increment_rate"
    )
    for item in groups_array:
        item["previous_2_month_rank"] = item["rank"]
        del item["rank"]

    groups_array = calculate_group_rankings(groups_array, "increment_rate")
    for item in groups_array:
        item["rank_change"] = item["previous_2_month_rank"] - item["rank"]
        # 应用小组别名映射
        item["group"] = apply_group_alias(item["group"], item["department"])
        del item["previous_2_month_rank"]
        del item["previous_2_month_increment_rate"]

    # 计算排除屏蔽部门后的总业绩
    filtered_summary = calculate_filtered_yearly_summary(full_year_data, current_month)

    return {
        "summary": {
            "total": format_float(filtered_summary["total"]),
            "dest": format_float(filtered_summary["dest"]),
            "rate": filtered_summary["rate"],
        },
        "departments": list(deparments.values())[:5],
        "groups": groups_array[:5],
    }


@tv_cache_redis()
async def get_monthly_summary(today: datetime) -> Dict[str, Any]:
    """获取月度汇总"""
    today = today.replace(hour=0, minute=0, second=0, microsecond=0).replace(tzinfo=tz)
    year = today.year
    month = today.month
    date = datetime.strptime(f"{year}-{month}", "%Y-%m")

    # 获取性能数据
    performance_data = await internal.get_performance(date.strftime("%Y-%m"))
    trade_detail_collection = db["gy_data"]["trade_detail"]

    # 处理部门数据
    deparment_sales_data = {
        item["department"]: {
            "department": item["department"],
            "total": format_float(item["total"]),
            "increment": 0,
            "shop_codes": [],
        }
        for item in performance_data["data"]["mallDepTotal"]
        if item["department"] not in BLOCKED_DEPARTMENTS
    }

    # 获取当天销售数据
    today_shop_sales_data = await trade_detail_collection.aggregate(
        [
            {
                "$match": {
                    "paytime": {"$gte": today, "$lt": today + timedelta(days=1)},
                }
            },
            {"$group": {"_id": "$shop_code", "total_amount": {"$sum": "$payment"}}},
        ]
    ).to_list()

    today_shop_sales_data_map = {
        item["_id"]: item["total_amount"] for item in today_shop_sales_data
    }

    # 更新部门数据
    for department, data in deparment_sales_data.items():
        for m in performance_data["data"]["mallTotal"]:
            if m["department"] == department:
                data["shop_codes"].append(m["gyCode"])
        for shop_code in data["shop_codes"]:
            data["increment"] = round(
                data["increment"] + today_shop_sales_data_map.get(shop_code, 0), 2
            )
        del data["shop_codes"]

    # 处理小组数据
    group_sales_data = filter_blocked_groups(performance_data["data"]["mallGroupTotal"])
    # 合并创新事业部的小组数据
    merged_group_sales_data = merge_innovation_department_groups(group_sales_data)
    group_sales_data_map = {
        item["mallGroup"]: {
            "group": item["mallGroup"],
            "department": (
                ",".join([dept for dept in item["department"] if dept is not None])
                if isinstance(item["department"], list)
                else (item["department"] or "")
            ),
            "total": format_float(item["total"]),
            "raw_data": item,
            "previous_day_2_total": 0,
            "previous_day_2_increment_rate": 0,
            "previous_day_2_rank": 0,
            "increment_rate": 0,
            "rank": 0,
            "rank_change": 0,
            "previous_day_3_total": 0,
        }
        for item in merged_group_sales_data
    }

    # 计算小组数据
    yesterday = today - timedelta(days=1)
    start = datetime(today.year, today.month, 1, tzinfo=tz)

    while start < yesterday:
        date_str = start.strftime("%Y-%m-%d")
        for group, data in group_sales_data_map.items():
            if date_str not in data["raw_data"]:
                continue
            amount = format_float(data["raw_data"][date_str])
            if start < yesterday - timedelta(days=1):
                data["previous_day_3_total"] += amount
            data["previous_day_2_total"] += amount

            if data["previous_day_3_total"] != 0:
                data["previous_day_2_increment_rate"] = calculate_increment_rate(
                    data["previous_day_2_total"], data["previous_day_3_total"]
                )
            if data["previous_day_2_total"] != 0:
                data["increment_rate"] = calculate_increment_rate(
                    data["total"], data["previous_day_2_total"]
                )
        start += timedelta(days=1)

    # 处理小组排名
    group_sales_data_array = calculate_group_rankings(
        list(group_sales_data_map.values()), "previous_day_2_increment_rate"
    )
    for item in group_sales_data_array:
        item["previous_day_2_rank"] = item["rank"]
        del item["rank"]

    group_sales_data_array = calculate_group_rankings(
        group_sales_data_array, "increment_rate"
    )
    for item in group_sales_data_array:
        item["rank_change"] = item["previous_day_2_rank"] - item["rank"]
        # 应用小组别名映射
        item["group"] = apply_group_alias(item["group"], item["department"])
        del item["raw_data"]
        del item["previous_day_2_rank"]
        del item["previous_day_2_increment_rate"]
        del item["previous_day_2_total"]
        del item["previous_day_3_total"]
        del item["total"]

    # 计算排除屏蔽部门后的总业绩
    filtered_summary = calculate_filtered_total_summary(performance_data)

    return {
        "summary": {
            "total": format_float(filtered_summary["total"]),
            "dest": format_float(filtered_summary["dest"]),
            "rate": filtered_summary["rate"],
        },
        "departments": list(deparment_sales_data.values())[:5],
        "groups": group_sales_data_array[:5],
    }


@tv_cache_redis()
async def daily() -> Dict[str, Any]:
    """获取前5的部门销售额"""
    now = datetime.now(tz=tz)
    today = get_today_start()

    # 获取业绩数据
    performance_data = await internal.get_performance(now.strftime("%Y-%m"))

    # 获取当天销售数据
    collection = db["gy_data"]["trade_detail"]
    pipeline = [
        {"$match": {"paytime": {"$gte": today}}},
        {
            "$group": {
                "_id": "$shop_code",
                "total_amount": {"$sum": "$payment"},
            }
        },
        {"$sort": {"total_amount": -1}},
    ]

    today_sales_data = list(await collection.aggregate(pipeline).to_list())
    today_sales_data = {item["_id"]: item["total_amount"] for item in today_sales_data}

    # 获取店铺信息
    shop_codes = list(today_sales_data.keys())
    async with get_session() as session:
        shops = await session.execute(
            select(PddMall).where(PddMall.gyCode.in_(shop_codes))
        )
        shops = shops.scalars().all()
        shop_dict = {shop.gyCode: shop.caiwuDept for shop in shops}
        # 同时获取店铺对应的小组信息
        shop_group_dict = {shop.gyCode: shop.mallGroup for shop in shops}

    # 聚合部门数据
    summary_result = {}
    for shop_code, caiwu_dept in shop_dict.items():
        # 跳过被屏蔽的部门
        if caiwu_dept in BLOCKED_DEPARTMENTS:
            continue
        if caiwu_dept not in summary_result:
            summary_result[caiwu_dept] = 0
        summary_result[caiwu_dept] += today_sales_data[shop_code]

    array = [{"department": k, "total": v} for k, v in summary_result.items()]

    # 处理部门目标数据
    deparment_sales_data = {
        item["department"]: {
            "total": format_float(item["total"]),
            "dest": format_float(item["dest"]),
        }
        for item in performance_data["data"]["mallDepTotal"]
        if item["department"] not in BLOCKED_DEPARTMENTS
    }

    # 处理小组数据
    group_sales_data = filter_blocked_groups(performance_data["data"]["mallGroupTotal"])
    # 合并创新事业部的小组数据
    merged_group_sales_data = merge_innovation_department_groups(group_sales_data)
    group_sales_data_map = {
        item["mallGroup"]: {
            "department": (
                ",".join([dept for dept in item["department"] if dept is not None])
                if isinstance(item["department"], list)
                else (item["department"] or "")
            ),
            "total": format_float(item["total"]),
            "dest": format_float(item["dest"]),
            "champion_count": 0,
            "raw_data": item,
        }
        for item in merged_group_sales_data
    }

    # 聚合今天的小组销售数据
    today_group_sales = {}
    innovation_today_sales = 0  # 创新事业部今天的销售额

    for shop_code, amount in today_sales_data.items():
        group_name = shop_group_dict.get(shop_code)
        dept_name = shop_dict.get(shop_code)

        # 如果是创新事业部的店铺，累计到创新事业部总额
        if dept_name == "创新事业部":
            innovation_today_sales += amount
            continue

        if group_name and group_name in group_sales_data_map:
            # 跳过被屏蔽的小组（按小组名称屏蔽）
            if group_name in BLOCKED_GROUPS:
                continue

            # 跳过被屏蔽的小组（按部门屏蔽）
            group_dept = group_sales_data_map[group_name]["department"]
            if any(
                dept and dept.strip() in BLOCKED_DEPARTMENTS
                for dept in group_dept.split(",")
            ):
                continue
            if group_name not in today_group_sales:
                today_group_sales[group_name] = 0
            today_group_sales[group_name] += amount

    # 将今天的销售数据加入到小组总业绩中
    for group_name, today_amount in today_group_sales.items():
        if group_name in group_sales_data_map:
            group_sales_data_map[group_name]["total"] += today_amount

    # 将创新事业部今天的销售额加入到"整体"小组
    if innovation_today_sales > 0 and "整体" in group_sales_data_map:
        group_sales_data_map["整体"]["total"] += innovation_today_sales

    # 计算小组冠军次数
    date_range_list = [f"{now.year}-{now.month:02d}-{i:02d}" for i in range(1, now.day)]
    group_daily_champion = process_group_champion_counts(
        group_sales_data_map, date_range_list
    )

    # 更新小组冠军次数
    for group, data in group_sales_data_map.items():
        data["champion_count"] = group_daily_champion.get(group, 0)

    # 计算排除屏蔽部门后的总业绩
    filtered_summary = calculate_filtered_total_summary(performance_data)

    # 汇总数据
    total = {
        "total": filtered_summary["total"],
        "dest": filtered_summary["dest"],
        "rate": filtered_summary["rate"],
    }

    # 更新部门数据
    for item in array:
        total["total"] += item["total"]
        dept = item["department"]
        if dept not in deparment_sales_data:
            continue
        amount = format_float(deparment_sales_data[dept]["total"])
        item["total"] += amount
        item["dest"] = format_float(deparment_sales_data[dept]["dest"])
        item["rate"] = round(item["total"] / item["dest"], 4) if item["dest"] > 0 else 0

    total["rate"] = round(total["total"] / total["dest"], 4) if total["dest"] > 0 else 0

    # 排序并取前5
    array = sorted(
        array,
        key=lambda x: (x["rate"], x["total"]),
        reverse=True,
    )[:5]

    # 处理小组数据
    groups = [
        {
            "group": apply_group_alias(g, v["department"]),
            "department": v["department"],
            "total": v["total"],
            "dest": v["dest"],
            "rate": 0 if v["dest"] == 0 else round(v["total"] / v["dest"], 4),
            "champion_count": v["champion_count"],
        }
        for g, v in group_sales_data_map.items()
    ]
    groups = sorted(groups, key=lambda x: (x["rate"], x["total"]), reverse=True)

    return {
        "departments": array,
        "groups": groups,
        "summary": total,
    }


# API路由
@router.get(
    "/daily",
    summary="获取每日销售数据",
    description="获取当天的销售数据汇总，包括部门销售额、小组销售额等信息",
    response_model=ResponseModel[DailyResponseModel],
)
async def get_daily(request: Request) -> Dict[str, Any]:
    """获取每日数据"""
    if not await has_permission(request):
        return router.error("无权限", code=403)
    return router.success(await daily())


@router.get(
    "/monthy",
    summary="获取月度销售数据",
    description="获取当前月份的销售数据汇总，包括部门销售额、小组销售额等信息",
    response_model=ResponseModel[MonthlyResponseModel],
)
async def get_monthly(request: Request) -> Dict[str, Any]:
    """获取月度数据"""
    if not await has_permission(request):
        return router.error("无权限", code=403)
    now = datetime.now(tz=tz)
    if now.day == 1:
        return router.success(await get_monthly_summary_for_first_day(now))
    return router.success(await get_monthly_summary(now))


@tv_cache_redis()
async def get_monthly_v2_data(now: datetime) -> Dict[str, Any]:
    """获取月度数据V2版本的核心逻辑"""
    current_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    # 计算时间范围
    last_month = current_month - dateutil.relativedelta.relativedelta(months=1)
    two_months_ago = current_month - dateutil.relativedelta.relativedelta(months=2)

    # 计算周的时间范围
    current_week_start = now - timedelta(days=now.weekday())
    current_week_start = current_week_start.replace(
        hour=0, minute=0, second=0, microsecond=0
    )
    last_week_start = current_week_start - timedelta(weeks=1)
    two_weeks_ago_start = current_week_start - timedelta(weeks=2)

    # 1. 获取全公司上月、前月的业绩总计
    current_month_data = await internal.get_performance(current_month.strftime("%Y-%m"))
    last_month_data = await internal.get_performance(last_month.strftime("%Y-%m"))
    two_months_ago_data = await internal.get_performance(
        two_months_ago.strftime("%Y-%m")
    )

    # 2. 获取全公司上周、前周的业绩总计（从trade_detail集合获取）
    trade_detail_collection = db["gy_data"]["trade_detail"]

    # 当前周业绩
    current_week_sales = await trade_detail_collection.aggregate(
        [
            {
                "$match": {
                    "paytime": {
                        "$gte": current_week_start,
                        "$lt": current_week_start + timedelta(weeks=1),
                    }
                }
            },
            {"$group": {"_id": None, "total": {"$sum": "$payment"}}},
        ]
    ).to_list()
    current_week_total = current_week_sales[0]["total"] if current_week_sales else 0

    # 上周业绩
    last_week_sales = await trade_detail_collection.aggregate(
        [
            {
                "$match": {
                    "paytime": {"$gte": last_week_start, "$lt": current_week_start}
                }
            },
            {"$group": {"_id": None, "total": {"$sum": "$payment"}}},
        ]
    ).to_list()
    last_week_total = last_week_sales[0]["total"] if last_week_sales else 0

    # 前周业绩
    two_weeks_ago_sales = await trade_detail_collection.aggregate(
        [
            {
                "$match": {
                    "paytime": {"$gte": two_weeks_ago_start, "$lt": last_week_start}
                }
            },
            {"$group": {"_id": None, "total": {"$sum": "$payment"}}},
        ]
    ).to_list()
    two_weeks_ago_total = two_weeks_ago_sales[0]["total"] if two_weeks_ago_sales else 0

    # 3. 获取各部门上月、本月的业绩总计
    current_dept_data = {
        item["department"]: {
            "department": item["department"],
            "current_total": format_float(item["total"]),
            "last_total": 0,
            "increment": 0,
        }
        for item in current_month_data["data"]["mallDepTotal"]
        if item["department"] not in BLOCKED_DEPARTMENTS
    }

    # 补充上月部门数据
    for item in last_month_data["data"]["mallDepTotal"]:
        dept_name = item["department"]
        # 跳过被屏蔽的部门
        if dept_name in BLOCKED_DEPARTMENTS:
            continue
        if dept_name in current_dept_data:
            current_dept_data[dept_name]["last_total"] = format_float(item["total"])
            current_dept_data[dept_name]["increment"] = round(
                current_dept_data[dept_name]["current_total"]
                - current_dept_data[dept_name]["last_total"],
                2,
            )

    # 4. 获取各小组增量百分比、排名变化
    # 处理本月小组数据
    filtered_current_group_data = filter_blocked_groups(
        current_month_data["data"]["mallGroupTotal"]
    )
    merged_current_group_data = merge_innovation_department_groups(
        filtered_current_group_data
    )
    current_group_dict = {item["mallGroup"]: item for item in merged_current_group_data}

    # 处理上月小组数据
    filtered_last_group_data = filter_blocked_groups(
        last_month_data["data"]["mallGroupTotal"]
    )
    merged_last_group_data = merge_innovation_department_groups(
        filtered_last_group_data
    )
    last_group_dict = {item["mallGroup"]: item for item in merged_last_group_data}

    # 处理前月小组数据
    filtered_two_months_ago_group_data = filter_blocked_groups(
        two_months_ago_data["data"]["mallGroupTotal"]
    )
    merged_two_months_ago_group_data = merge_innovation_department_groups(
        filtered_two_months_ago_group_data
    )
    two_months_ago_group_dict = {
        item["mallGroup"]: item for item in merged_two_months_ago_group_data
    }

    # 获取所有小组的并集（本月 + 上月）
    all_group_names = set(current_group_dict.keys()) | set(last_group_dict.keys())

    # 构建完整的小组数据
    current_group_data = {}
    for group_name in all_group_names:
        current_item = current_group_dict.get(group_name)
        last_item = last_group_dict.get(group_name)

        # 优先使用本月的部门信息，如果本月没有则使用上月的
        if current_item:
            dept_list = current_item["department"]
            if isinstance(dept_list, list):
                # 过滤掉 None 值
                dept_list = [dept for dept in dept_list if dept is not None]
                department = ",".join(dept_list)
            else:
                department = dept_list or ""
            current_total = format_float(current_item["total"])
        else:
            # 本月没有数据，使用上月的部门信息
            dept_list = last_item["department"]
            if isinstance(dept_list, list):
                # 过滤掉 None 值
                dept_list = [dept for dept in dept_list if dept is not None]
                department = ",".join(dept_list)
            else:
                department = dept_list or ""
            current_total = 0.0

        current_group_data[group_name] = {
            "group": group_name,
            "department": department,
            "current_total": current_total,
            "last_total": format_float(last_item["total"]) if last_item else 0,
            "two_months_ago_total": format_float(
                two_months_ago_group_dict.get(group_name, {}).get("total", 0)
            ),
            "current_increment_rate": 0,
            "last_increment_rate": 0,
            "current_rank": 0,
            "last_rank": 0,
            "rank_change": 0,
        }

    # 计算小组增长率和排名
    for group_name, group_info in current_group_data.items():

        # 计算当前月增长倍数（相对于上月）
        # calculate_increment_multiplier 函数已经处理了新小组的情况，会返回 None
        group_info["current_increment_rate"] = calculate_increment_multiplier(
            group_info["current_total"], group_info["last_total"]
        )

        # 计算上月增长倍数（相对于前月）
        group_info["last_increment_rate"] = calculate_increment_multiplier(
            group_info["last_total"], group_info["two_months_ago_total"]
        )

    # 计算当前月排名
    current_groups_array = calculate_group_rankings(
        list(current_group_data.values()), "current_increment_rate"
    )
    for item in current_groups_array:
        item["current_rank"] = item["rank"]
        del item["rank"]

    # 计算上月排名
    last_groups_array = calculate_group_rankings(
        list(current_group_data.values()), "last_increment_rate"
    )
    last_rank_map = {item["group"]: item["rank"] for item in last_groups_array}

    # 计算排名变化
    for item in current_groups_array:
        item["last_rank"] = last_rank_map.get(item["group"], 0)
        item["rank_change"] = item["last_rank"] - item["current_rank"]
        # 应用小组别名映射
        item["group"] = apply_group_alias(item["group"], item["department"])
        # 清理不需要的字段
        del item["last_total"]
        del item["two_months_ago_total"]
        del item["last_increment_rate"]
        del item["current_total"]
        del item["last_rank"]
        item["increment_rate"] = item["current_increment_rate"]
        item["rank"] = item["current_rank"]
        del item["current_increment_rate"]
        del item["current_rank"]

    # 计算排除屏蔽部门后的总业绩
    filtered_summary = calculate_filtered_v2_summary(
        current_month_data, last_month_data, two_months_ago_data
    )

    # 构建返回数据
    summary_data = {
        "total": format_float(filtered_summary["total"]),
        "dest": format_float(filtered_summary["dest"]),
        "rate": filtered_summary["rate"],
        "last_month_total": format_float(filtered_summary["last_month_total"]),
        "two_months_ago_total": format_float(filtered_summary["two_months_ago_total"]),
        "month_increment_rate": filtered_summary["month_increment_rate"],
        "current_week_total": format_float(current_week_total),
        "last_week_total": format_float(last_week_total),
        "two_weeks_ago_total": format_float(two_weeks_ago_total),
        "week_increment_rate": calculate_increment_rate(
            current_week_total, last_week_total
        ),
    }

    return {
        "summary": summary_data,
        "departments": list(current_dept_data.values())[:5],
        "groups": current_groups_array[:10],
    }


@tv_cache_redis()
async def get_monthly_v2_data_for_first_day(now: datetime) -> Dict[str, Any]:
    """获取月度数据V2版本的核心逻辑（每月1号专用，返回上个月对比前个月的数据）"""
    current_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    # 计算时间范围
    last_month = current_month - dateutil.relativedelta.relativedelta(months=1)
    two_months_ago = current_month - dateutil.relativedelta.relativedelta(months=2)

    # 计算周的时间范围
    current_week_start = now - timedelta(days=now.weekday())
    current_week_start = current_week_start.replace(
        hour=0, minute=0, second=0, microsecond=0
    )
    last_week_start = current_week_start - timedelta(days=7)
    two_weeks_ago_start = current_week_start - timedelta(days=14)

    # 1. 获取当前月、上月、前月的数据
    current_month_data = await internal.get_performance(current_month.strftime("%Y-%m"))
    last_month_data = await internal.get_performance(last_month.strftime("%Y-%m"))
    two_months_ago_data = await internal.get_performance(
        two_months_ago.strftime("%Y-%m")
    )

    # 2. 获取全公司上周、前周的业绩总计（从trade_detail集合获取）
    trade_detail_collection = db["gy_data"]["trade_detail"]

    # 当前周业绩
    current_week_sales = await trade_detail_collection.aggregate(
        [
            {
                "$match": {
                    "paytime": {
                        "$gte": current_week_start,
                        "$lt": current_week_start + timedelta(weeks=1),
                    }
                }
            },
            {"$group": {"_id": None, "total": {"$sum": "$payment"}}},
        ]
    ).to_list()
    current_week_total = current_week_sales[0]["total"] if current_week_sales else 0

    # 上周业绩
    last_week_sales = await trade_detail_collection.aggregate(
        [
            {
                "$match": {
                    "paytime": {"$gte": last_week_start, "$lt": current_week_start}
                }
            },
            {"$group": {"_id": None, "total": {"$sum": "$payment"}}},
        ]
    ).to_list()
    last_week_total = last_week_sales[0]["total"] if last_week_sales else 0

    # 前周业绩
    two_weeks_ago_sales = await trade_detail_collection.aggregate(
        [
            {
                "$match": {
                    "paytime": {"$gte": two_weeks_ago_start, "$lt": last_week_start}
                }
            },
            {"$group": {"_id": None, "total": {"$sum": "$payment"}}},
        ]
    ).to_list()
    two_weeks_ago_total = two_weeks_ago_sales[0]["total"] if two_weeks_ago_sales else 0

    # 3. 获取各部门增量、排名变化
    # 处理本月部门数据
    current_dept_data = {}
    for item in current_month_data["data"]["mallDepTotal"]:
        dept_name = item["department"]
        # 跳过被屏蔽的部门
        if dept_name in BLOCKED_DEPARTMENTS:
            continue
        current_dept_data[dept_name] = {
            "department": dept_name,
            "current_total": format_float(item["total"]),
            "last_total": 0,
            "increment": 0,
        }

    # 补充上月部门数据
    for item in last_month_data["data"]["mallDepTotal"]:
        dept_name = item["department"]
        # 跳过被屏蔽的部门
        if dept_name in BLOCKED_DEPARTMENTS:
            continue
        if dept_name in current_dept_data:
            current_dept_data[dept_name]["last_total"] = format_float(item["total"])
            current_dept_data[dept_name]["increment"] = round(
                current_dept_data[dept_name]["current_total"]
                - current_dept_data[dept_name]["last_total"],
                2,
            )

    # 4. 获取各小组增量百分比、排名变化
    # 处理本月小组数据
    filtered_current_group_data = filter_blocked_groups(
        current_month_data["data"]["mallGroupTotal"]
    )
    merged_current_group_data = merge_innovation_department_groups(
        filtered_current_group_data
    )
    current_group_dict = {item["mallGroup"]: item for item in merged_current_group_data}

    # 处理上月小组数据
    filtered_last_group_data = filter_blocked_groups(
        last_month_data["data"]["mallGroupTotal"]
    )
    merged_last_group_data = merge_innovation_department_groups(
        filtered_last_group_data
    )
    last_group_dict = {item["mallGroup"]: item for item in merged_last_group_data}

    # 处理前月小组数据
    filtered_two_months_ago_group_data = filter_blocked_groups(
        two_months_ago_data["data"]["mallGroupTotal"]
    )
    merged_two_months_ago_group_data = merge_innovation_department_groups(
        filtered_two_months_ago_group_data
    )
    two_months_ago_group_dict = {
        item["mallGroup"]: item for item in merged_two_months_ago_group_data
    }

    # 构建小组数据字典
    current_group_data = {}
    for group_name, group_info in current_group_dict.items():
        # 过滤掉 None 值
        dept_list = group_info["department"]
        if isinstance(dept_list, list):
            dept_list = [dept for dept in dept_list if dept is not None]
            department = ",".join(dept_list)
        else:
            department = dept_list or ""

        current_group_data[group_name] = {
            "department": department,
            "group": group_name,
            "current_total": format_float(group_info["total"]),
            "last_total": 0,
            "two_months_ago_total": 0,
        }

    # 补充上月数据
    for group_name, group_info in last_group_dict.items():
        if group_name in current_group_data:
            current_group_data[group_name]["last_total"] = format_float(
                group_info["total"]
            )

    # 补充前月数据
    for group_name, group_info in two_months_ago_group_dict.items():
        if group_name in current_group_data:
            current_group_data[group_name]["two_months_ago_total"] = format_float(
                group_info["total"]
            )

    # 对于不在当前月的小组，也要包含在数据中（如果它们在上月或前月存在）
    all_group_names = (
        set(current_group_dict.keys())
        | set(last_group_dict.keys())
        | set(two_months_ago_group_dict.keys())
    )
    for group_name in all_group_names:
        if group_name not in current_group_data:
            # 获取部门信息
            department = ""
            if group_name in last_group_dict:
                dept_list = last_group_dict[group_name]["department"]
                if isinstance(dept_list, list):
                    dept_list = [dept for dept in dept_list if dept is not None]
                    department = ",".join(dept_list)
                else:
                    department = dept_list or ""
            elif group_name in two_months_ago_group_dict:
                dept_list = two_months_ago_group_dict[group_name]["department"]
                if isinstance(dept_list, list):
                    dept_list = [dept for dept in dept_list if dept is not None]
                    department = ",".join(dept_list)
                else:
                    department = dept_list or ""

            current_group_data[group_name] = {
                "department": department,
                "group": group_name,
                "current_total": 0,
                "last_total": format_float(
                    last_group_dict.get(group_name, {}).get("total", 0)
                ),
                "two_months_ago_total": format_float(
                    two_months_ago_group_dict.get(group_name, {}).get("total", 0)
                ),
            }

    # 计算小组增长率和排名（每月1号返回上个月对比前个月的数据）
    def calculate_increment_rate_with_none(
        current: float, previous: float
    ) -> float | None:
        """计算增长率，新小组返回None"""
        if previous > 0:
            return round((current - previous) / previous, 2)
        elif current == 0:
            return 0.0
        else:
            # 当previous为0但current大于0时（新的小组），返回None表示空的增量百分比
            return None

    # 我们需要获取三个月前的数据来计算前月的增量
    three_months_ago = current_month - dateutil.relativedelta.relativedelta(months=3)
    three_months_ago_data = await internal.get_performance(
        three_months_ago.strftime("%Y-%m")
    )

    # 处理三个月前小组数据
    filtered_three_months_ago_group_data = filter_blocked_groups(
        three_months_ago_data["data"]["mallGroupTotal"]
    )
    merged_three_months_ago_group_data = merge_innovation_department_groups(
        filtered_three_months_ago_group_data
    )
    three_months_ago_group_dict = {
        item["mallGroup"]: item for item in merged_three_months_ago_group_data
    }

    # 补充三个月前数据
    for group_name, group_info in current_group_data.items():
        group_info["three_months_ago_total"] = format_float(
            three_months_ago_group_dict.get(group_name, {}).get("total", 0)
        )

    for group_name, group_info in current_group_data.items():
        # 每月1号时，返回上月增长率（相对于前月）作为主要增量
        # 公式：(上月 - 前月) / 前月
        group_info["current_increment_rate"] = calculate_increment_rate_with_none(
            group_info["last_total"], group_info["two_months_ago_total"]
        )

        # 计算前月增长率（相对于三个月前）- 用于排名变化计算
        # 公式：(前月 - 三个月前) / 三个月前
        group_info["previous_increment_rate"] = calculate_increment_rate_with_none(
            group_info["two_months_ago_total"], group_info["three_months_ago_total"]
        )

    # 计算当前排名（基于上月对比前月的增量）
    current_groups_array = calculate_group_rankings(
        list(current_group_data.values()), "current_increment_rate"
    )
    for item in current_groups_array:
        item["current_rank"] = item["rank"]
        del item["rank"]

    # 计算前期排名（用于排名变化计算）
    previous_groups_array = calculate_group_rankings(
        list(current_group_data.values()), "previous_increment_rate"
    )
    previous_rank_map = {item["group"]: item["rank"] for item in previous_groups_array}

    # 计算排名变化并清理数据
    for item in current_groups_array:
        item["previous_rank"] = previous_rank_map.get(item["group"], 0)
        item["rank_change"] = item["previous_rank"] - item["current_rank"]
        # 应用小组别名映射
        item["group"] = apply_group_alias(item["group"], item["department"])
        # 设置最终字段
        item["increment_rate"] = item["current_increment_rate"]
        item["rank"] = item["current_rank"]
        # 清理不需要的字段
        del item["current_total"]
        del item["last_total"]
        del item["two_months_ago_total"]
        del item["three_months_ago_total"]
        del item["current_increment_rate"]
        del item["previous_increment_rate"]
        del item["current_rank"]
        del item["previous_rank"]

    # 计算排除屏蔽部门后的总业绩
    filtered_summary = calculate_filtered_v2_summary(
        current_month_data, last_month_data, two_months_ago_data
    )

    # 构建返回数据
    summary_data = {
        "total": format_float(filtered_summary["total"]),
        "dest": format_float(filtered_summary["dest"]),
        "rate": filtered_summary["rate"],
        "last_month_total": format_float(filtered_summary["last_month_total"]),
        "two_months_ago_total": format_float(filtered_summary["two_months_ago_total"]),
        "month_increment_rate": filtered_summary["month_increment_rate"],
        "current_week_total": format_float(current_week_total),
        "last_week_total": format_float(last_week_total),
        "two_weeks_ago_total": format_float(two_weeks_ago_total),
        "week_increment_rate": calculate_increment_rate(
            current_week_total, last_week_total
        ),
    }

    return {
        "summary": summary_data,
        "departments": list(current_dept_data.values())[:5],
        "groups": current_groups_array[:10],
    }


@router.get(
    "/monthly_v2",
    summary="获取月度销售数据V2",
    description="获取月度销售数据，包含上月对比、上周对比、部门对比和小组排名变化",
    response_model=ResponseModel[MonthlyV2ResponseModel],
)
async def get_monthly_v2(request: Request) -> Dict[str, Any]:
    """获取月度数据V2版本"""
    if not await has_permission(request):
        return router.error("无权限", code=403)

    try:
        now = datetime.now(tz=tz)
        # 每月1号时，返回上个月对比前个月的数据
        if now.day == 1:
            result = await get_monthly_v2_data_for_first_day(now)
        else:
            result = await get_monthly_v2_data(now)
        return router.success(result)
    except Exception as e:
        return router.error(f"获取数据失败: {str(e)}", code=500)


@router.get(
    "/check_user_permission",
    summary="检查用户权限",
    description="检查用户权限",
    response_model=ResponseModel[bool],
)
async def check_user_permission(request: Request):
    """检查用户权限"""
    return router.success(await has_permission(request))


async def has_permission(request: Request):
    """检查用户权限"""
    user = request.state._state.get("user", {})
    uid = user.get("loginId", "0")
    return await auth.checkPermission(uid, "BI:TV")


@router.get(
    "/test",
    summary="测试",
    description="测试",
)
async def test(request: Request):
    """测试"""
    if not await has_permission(request):
        return router.error("无权限", code=403)
    user = request.state._state.get("user", {})
    uid = user.get("loginId", "0")
    return router.success(
        {"uid": uid, "user": user, "permission": not await has_permission(request)}
    )


async def main():
    import time

    start_time = time.time()
    res = await get_monthly_summary(datetime(2025, 4, 9, tzinfo=tz))
    print(json.dumps(res, ensure_ascii=False, indent=4))
    res = await get_monthly_summary_for_first_day(datetime(2025, 4, 9, tzinfo=tz))
    print(json.dumps(res, ensure_ascii=False, indent=4))
    end_time = time.time()
    print(f"耗时: {end_time - start_time} 秒")


if __name__ == "__main__":
    asyncio.run(main())
