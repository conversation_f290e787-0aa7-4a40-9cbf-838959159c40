"""
小红书聚光平台 API 认证模块

提供访问令牌的获取、刷新、缓存等功能。
"""

import json
import logging
import requests
from datetime import datetime, timedelta
from typing import Tuple

from .types import TokenInfo, ApprovalAdvertisersList
from .constants import BASE_URL, ENDPOINTS, CACHE_CONFIG

# 这些模块需要从主项目导入
try:
    from app.core.database import db
    from app.utils.redis_utils import RedisClient
    from app.core.config import settings
    from app.core.logger import logger
except ImportError:
    # 如果在libs中单独使用，提供备用实现
    print("Warning: 无法导入主项目模块，使用默认配置")
    db = None
    RedisClient = None
    settings = None
    # 提供默认的logger实现
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)


async def refresh_access_token(refresh_token: str) -> dict | None:
    """
    刷新访问令牌

    Args:
        refresh_token: 刷新令牌

    Returns:
        dict | None: 刷新结果，包含新的访问令牌信息
    """
    if not settings:
        raise RuntimeError("配置未加载，无法刷新访问令牌")

    url = f"{BASE_URL}{ENDPOINTS['refresh_token']}"
    resp = requests.post(
        url,
        json={
            "app_id": settings.XHS_APP_ID,
            "secret": settings.XHS_APP_SECRET,
            "refresh_token": refresh_token,
        },
    )

    if resp.status_code == 200:
        return resp.json()
    else:
        logger.error(f"刷新访问令牌失败: {resp.text}")
        return None


async def _get_valid_access_token_from_db(
    now_ts: int,
) -> Tuple[str | None, int | None, ApprovalAdvertisersList | None]:
    """
    从数据库获取有效的 access_token，必要时自动刷新。

    Args:
        now_ts: 当前时间戳

    Returns:
        tuple: (access_token, expire_time, approval_advertisers) 或 (None, None, None)
    """
    if not db:
        raise RuntimeError("数据库连接未初始化")

    record = (
        await db.get_database("rpa_data")
        .get_collection("access_tokens")
        .find_one({"type": "xiaohongshu"})
    )

    if not record:
        logger.error("未找到小红书 access_token 记录")
        return None, None, None

    access_token = record.get("access_token")
    access_token_expires_time = record.get("expire_time")
    approval_advertisers = record.get("approval_advertisers", [])
    refresh_token = record.get("refresh_token")
    refresh_token_expires_time = record.get("refresh_token_expires_time")

    logger.info(
        f"从数据库获取到令牌记录 - "
        f"access_token存在: {bool(access_token)}, "
        f"expire_time: {access_token_expires_time} ({datetime.fromtimestamp(access_token_expires_time) if access_token_expires_time else 'None'}), "
        f"refresh_token存在: {bool(refresh_token)}, "
        f"refresh_token_expire_time: {refresh_token_expires_time} ({datetime.fromtimestamp(refresh_token_expires_time) if refresh_token_expires_time else 'None'})"
    )

    # 检查访问令牌是否需要刷新（提前600秒刷新）
    should_refresh = False
    if access_token_expires_time:
        refresh_threshold_ts = access_token_expires_time - 600  # 提前600秒刷新
        should_refresh = now_ts >= refresh_threshold_ts

        logger.info(
            f"检查令牌过期状态 - 当前时间: {datetime.fromtimestamp(now_ts)}, "
            f"令牌过期时间: {datetime.fromtimestamp(access_token_expires_time)}, "
            f"刷新阈值时间: {datetime.fromtimestamp(refresh_threshold_ts)}, "
            f"需要刷新: {should_refresh}"
        )
    if should_refresh:
        logger.info("令牌即将过期，开始刷新流程")
        # 检查刷新令牌是否过期
        if refresh_token_expires_time and refresh_token_expires_time < now_ts:
            logger.error("刷新令牌已过期，请重新登录获取新的令牌")
            return None, None, None

        if refresh_token:
            logger.info("正在调用刷新令牌API...")
            res = await refresh_access_token(refresh_token)

            logger.info(f"刷新令牌API响应: {res}")
            if not res or not res.get("success", False):
                logger.error(
                    f"刷新访问令牌失败, {res.get('msg', '未知错误') if res else '无返回'}"
                )
                return None, None, None
            res = res["data"]
            # 更新数据库记录
            record["access_token"] = res.get("access_token")
            record["expire_time"] = now_ts + res.get("access_token_expires_in", 0)
            record["refresh_token"] = res.get("refresh_token")
            record["refresh_token_expires_time"] = now_ts + res.get(
                "refresh_token_expires_in", 0
            )

            # 刷新时保留 approval_advertisers，如果API返回了新值则更新
            if "approval_advertisers" in res:
                record["approval_advertisers"] = res.get("approval_advertisers", [])

            record["created_at"] = now_ts

            await db.get_database("rpa_data").get_collection(
                "access_tokens"
            ).update_one({"_id": record["_id"]}, {"$set": record})

            access_token = record["access_token"]
            access_token_expires_time = record["expire_time"]
            approval_advertisers = record.get("approval_advertisers", [])
        else:
            logger.error("没有可用的刷新令牌")
            return None, None, None
    else:
        logger.info("令牌仍在有效期内，无需刷新")
    print(access_token)
    return access_token, access_token_expires_time, approval_advertisers


async def get_access_token() -> TokenInfo | None:
    """
    获取访问令牌（带 Redis 缓存和分布式锁，自动刷新和降级处理）

    Returns:
        TokenInfo: 包含 access_token 和 approval_advertisers 的字典，格式为:
                   {
                       "access_token": str,
                       "approval_advertisers": List[AdvertiserInfo]
                   }
                   失败时返回 None
    """
    cache_key = CACHE_CONFIG["token_cache_key"]
    lock_key = CACHE_CONFIG["token_lock_key"]
    now_ts = int(datetime.now().timestamp())

    logger.info(
        f"开始获取访问令牌 - 当前时间戳: {now_ts} ({datetime.fromtimestamp(now_ts)})"
    )

    # 1. 优先查缓存
    if RedisClient:
        try:
            cached_token_info = await RedisClient.get(cache_key)
            if cached_token_info:
                logger.info("从Redis缓存中获取到令牌信息")
                return cached_token_info
        except Exception as e:
            logger.error(f"Redis 连接失败: {e}")

    # 2. 分布式锁，防止并发刷新
    if RedisClient:
        try:
            async with await RedisClient.lock(
                lock_key, timeout=CACHE_CONFIG["lock_timeout"]
            ):
                # 再查一次缓存，防止并发期间被其他进程刷新
                cached_token_info = await RedisClient.get(cache_key)
                if cached_token_info:
                    return cached_token_info

                # 3. 查数据库并自动刷新
                access_token, expire_time, approval_advertisers = (
                    await _get_valid_access_token_from_db(now_ts)
                )

                if access_token:
                    # 构建完整的令牌信息
                    token_info = {
                        "access_token": access_token,
                        "approval_advertisers": approval_advertisers or [],
                    }

                    # 计算缓存过期时间，令牌剩余有效期-600秒，最小60秒
                    # if expire_time and expire_time > now_ts + 600:
                    #     cache_ex = max(
                    #         expire_time - now_ts - 600,
                    #         CACHE_CONFIG["min_cache_seconds"],
                    #     )
                    # else:
                    cache_ex = CACHE_CONFIG["min_cache_seconds"]

                    try:
                        await RedisClient.set(cache_key, token_info, ex=cache_ex)
                    except Exception as e:
                        logger.error(f"Redis 缓存失败: {e}")

                    return token_info

                return None

        except Exception as e:
            # 4. 锁异常降级，直接查数据库并自动刷新
            logger.error(f"Redis 分布式锁异常: {e}")

    # 降级处理：直接查询数据库
    access_token, _, approval_advertisers = await _get_valid_access_token_from_db(
        now_ts
    )

    if access_token:
        return {
            "access_token": access_token,
            "approval_advertisers": approval_advertisers or [],
        }

    return None


async def get_access_token_only() -> str | None:
    """
    获取访问令牌（仅返回 access_token 字符串，向后兼容）

    Returns:
        str: access_token 字符串，失败时返回 None
    """
    token_info = await get_access_token()
    return token_info["access_token"] if token_info else None


async def get_approval_advertisers() -> ApprovalAdvertisersList:
    """
    获取授权广告主列表

    Returns:
        ApprovalAdvertisersList: approval_advertisers 列表，失败时返回空列表
    """
    token_info = await get_access_token()
    return token_info["approval_advertisers"] if token_info else []
