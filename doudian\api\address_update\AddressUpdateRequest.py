# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.address_update.param.AddressUpdateParam import AddressUpdateParam


class AddressUpdateRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AddressUpdateParam()

	def getUrlPath(self, ):
		return "/address/update"

	def getParams(self, ):
		return self.params



