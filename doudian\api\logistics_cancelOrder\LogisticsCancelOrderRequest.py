# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_cancelOrder.param.LogisticsCancelOrderParam import LogisticsCancelOrderParam


class LogisticsCancelOrderRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsCancelOrderParam()

	def getUrlPath(self, ):
		return "/logistics/cancelOrder"

	def getParams(self, ):
		return self.params



