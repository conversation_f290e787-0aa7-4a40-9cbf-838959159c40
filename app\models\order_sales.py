from sqlalchemy import (
    Column,
    String,
    Integer,
    Numeric,
    BigInteger,
    Index,
)
from sqlalchemy.orm import Mapped
from .base import Base


class ProductOrderSalesItemShopSum(Base):
    """产品单品店铺订单销售统计表"""

    __tablename__ = "product_order_sales_item_shop_sum"

    id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    create_time: Mapped[int] = Column(
        BigInteger, nullable=False, default=0, comment="创建时间"
    )
    update_time: Mapped[int] = Column(
        BigInteger, nullable=False, default=0, comment="修改时间"
    )
    date_time: Mapped[int] = Column(
        BigInteger, nullable=False, default=0, comment="时间"
    )
    date_year: Mapped[str] = Column(
        String(4), nullable=False, default="", comment="年份"
    )
    date_month: Mapped[str] = Column(
        String(2), nullable=False, default="", comment="月份"
    )
    date_day: Mapped[str] = Column(
        String(2), nullable=False, default="", comment="日期"
    )
    platform_name: Mapped[str] = Column(String(255), default="", comment="平台名字")
    platform_code: Mapped[str] = Column(String(255), default="", comment="平台编码")
    item_code: Mapped[str] = Column(
        String(64), nullable=False, default="", comment="商品代码"
    )
    item_name: Mapped[str] = Column(
        String(128), nullable=False, default="", comment="产品名称"
    )
    shop_code: Mapped[str] = Column(
        String(16), nullable=False, default="0", comment="店铺代码"
    )
    shop_name: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="店铺名称"
    )
    order_number: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="订单量"
    )
    order_number_without_giveaway: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="订单量（不含赠品）"
    )
    sales_number: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="发货量"
    )
    sales_number_without_giveaway: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="发货量（不含赠品）"
    )
    post_cost: Mapped[float] = Column(
        Numeric(10, 2), nullable=False, default=0.00, comment="物流成本"
    )
    total_cost_price: Mapped[float] = Column(
        Numeric(10, 2), nullable=False, default=0.00, comment="商品成本"
    )
    after_amount: Mapped[float] = Column(
        Numeric(10, 2), nullable=False, default=0.00, comment="实收金额"
    )

    __table_args__ = (
        Index("index_year_item_group_shop", "date_year", "item_code", "shop_code"),
        Index("index_datetime", "date_time", "item_code", "shop_code"),
        Index("index_item", "item_code", "date_time", "shop_code"),
        Index("index_shop", "shop_code", "date_time", "item_code"),
        Index(
            "IX_product_order_sales_item_shop_sum_datetime_itemcode",
            "date_time",
            "item_code",
        ),
    )
