# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.address_listStrategyBindProducts.param.AddressListStrategyBindProductsParam import AddressListStrategyBindProductsParam


class AddressListStrategyBindProductsRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AddressListStrategyBindProductsParam()

	def getUrlPath(self, ):
		return "/address/listStrategyBindProducts"

	def getParams(self, ):
		return self.params



