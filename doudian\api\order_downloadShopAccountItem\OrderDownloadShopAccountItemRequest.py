# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_downloadShopAccountItem.param.OrderDownloadShopAccountItemParam import OrderDownloadShopAccountItemParam


class OrderDownloadShopAccountItemRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderDownloadShopAccountItemParam()

	def getUrlPath(self, ):
		return "/order/downloadShopAccountItem"

	def getParams(self, ):
		return self.params



