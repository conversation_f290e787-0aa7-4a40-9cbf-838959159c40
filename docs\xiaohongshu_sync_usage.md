# 小红书创意数据同步功能使用文档

## 概述

本功能实现了从小红书聚光平台 API 获取创意离线报表数据，并自动同步到本地数据库 `brand_xhs_jg_creativity_data_report` 表中。支持新增和更新操作，确保数据的完整性和一致性。

## 功能特性

- ✅ **自动获取访问令牌**：自动从数据库获取并刷新小红书 API 访问令牌
- ✅ **分页数据获取**：支持大量数据的分页获取，最大页面大小 500 条
- ✅ **数据去重**：基于创意 ID+笔记 ID+日期的唯一性约束，避免重复数据
- ✅ **增量更新**：已存在的记录会被更新，新记录会被插入
- ✅ **错误处理**：完善的错误处理和日志记录
- ✅ **事务支持**：使用数据库事务确保数据一致性
- ✅ **多广告主支持**：支持同时处理多个授权广告主的数据

## 数据库表结构

### 唯一性约束

表 `brand_xhs_jg_creativity_data_report` 使用以下字段组合作为唯一键：

- `creativity_id` (创意 ID)
- `note_id` (笔记 ID)
- `date_time` (日期时间戳)

这确保了每个创意每天只有一条记录。

### 主要字段

| 字段名          | 类型          | 说明              |
| --------------- | ------------- | ----------------- |
| `creativity_id` | String(32)    | 创意 ID           |
| `note_id`       | String(32)    | 笔记 ID           |
| `date_time`     | BigInteger    | 日期时间戳        |
| `fee`           | Numeric(11,2) | 消费金额          |
| `impression`    | String(32)    | 展现量            |
| `click`         | String(32)    | 点击量            |
| `ctr`           | String(32)    | 点击率            |
| `interaction`   | String(32)    | 互动量            |
| `origin_data`   | Text          | 原始 API 返回数据 |

## 使用方法

### 1. 基础同步功能

```python
from app.services.providers.xiaohongshu import sync_xiaohongshu_creativity_data

# 同步昨天的数据（默认）
result = await sync_xiaohongshu_creativity_data()

# 同步指定日期范围的数据
result = await sync_xiaohongshu_creativity_data(
    start_date="2024-01-01",
    end_date="2024-01-07"
)

# 同步指定广告主的数据
result = await sync_xiaohongshu_creativity_data(
    advertiser_ids=[123456, 789012]
)
```

### 2. 日期范围同步

```python
from app.services.providers.xiaohongshu import sync_xiaohongshu_creativity_data_for_date_range

# 同步最近7天的数据
result = await sync_xiaohongshu_creativity_data_for_date_range(days_back=7)

# 同步最近30天的数据
result = await sync_xiaohongshu_creativity_data_for_date_range(days_back=30)
```

### 3. 返回结果处理

```python
result = await sync_xiaohongshu_creativity_data()

if result["success"]:
    print(f"同步成功!")
    print(f"处理记录数: {result['total_processed']}")
    print(f"新增记录数: {result['total_inserted']}")
    print(f"更新记录数: {result['total_updated']}")
    print(f"广告主数量: {result['advertisers_count']}")

    # 检查是否有错误
    if result["errors"]:
        print(f"发生错误: {len(result['errors'])} 个")
        for error in result["errors"]:
            print(f"- {error}")
else:
    print(f"同步失败: {result['error']}")
```

## 返回结果结构

### 成功响应

```json
{
  "success": true,
  "total_processed": 150,
  "total_inserted": 100,
  "total_updated": 50,
  "errors": [],
  "date_range": "2024-01-01 到 2024-01-07",
  "advertisers_count": 2
}
```

### 失败响应

```json
{
  "success": false,
  "error": "获取访问令牌失败",
  "total_processed": 0,
  "total_inserted": 0,
  "total_updated": 0,
  "errors": ["具体错误信息"]
}
```

## 定时任务配置

### 1. 每日自动同步

建议配置定时任务每天自动同步前一天的数据：

```python
# 每天凌晨2点执行
import schedule
import asyncio

def job():
    asyncio.run(sync_xiaohongshu_creativity_data())

schedule.every().day.at("02:00").do(job)
```

### 2. 使用 Celery 异步任务

```python
from celery import Celery

app = Celery('xiaohongshu_sync')

@app.task
async def sync_xiaohongshu_data_task():
    result = await sync_xiaohongshu_creativity_data()
    return result

# 每天执行
from celery.schedules import crontab

app.conf.beat_schedule = {
    'sync-xiaohongshu-data': {
        'task': 'sync_xiaohongshu_data_task',
        'schedule': crontab(hour=2, minute=0),  # 每天凌晨2点
    },
}
```

## 测试和验证

### 运行测试脚本

```bash
python test_xiaohongshu_sync.py
```

测试脚本包含以下测试用例：

1. 同步昨天的数据
2. 同步指定日期范围的数据
3. 同步指定日期的数据
4. 重复同步测试（验证更新功能）

### 手动验证

```python
# 检查数据库中的记录
from app.core.oa_database import get_session, BrandXhsJgCreativityDataReport
from sqlalchemy import select, func

async with get_session() as session:
    # 查询总记录数
    stmt = select(func.count(BrandXhsJgCreativityDataReport.id))
    result = await session.execute(stmt)
    total_count = result.scalar()
    print(f"总记录数: {total_count}")

    # 查询最新的10条记录
    stmt = select(BrandXhsJgCreativityDataReport).order_by(
        BrandXhsJgCreativityDataReport.update_time.desc()
    ).limit(10)
    result = await session.execute(stmt)
    records = result.scalars().all()

    for record in records:
        print(f"创意ID: {record.creativity_id}, 笔记ID: {record.note_id}, "
              f"日期: {record.date_year}-{record.date_month}-{record.date_day}, "
              f"消费: {record.fee}")
```

## 注意事项

### 1. API 限制

- 小红书 API 有调用频率限制，建议不要过于频繁调用
- 离线数据最早在次日 10 点产出，建议在上午 10 点后执行同步

### 2. 数据完整性

- 每次同步会获取完整的日期范围数据
- 已存在的记录会被更新为最新数据
- 原始 API 数据会保存在 `origin_data` 字段中

### 3. 错误处理

- 网络错误会自动重试
- API 错误会记录在错误列表中
- 数据库错误会回滚整个事务

### 4. 性能优化

- 使用分页获取避免内存溢出
- 使用数据库事务确保一致性
- 批量处理提高效率

## 故障排除

### 常见问题

1. **访问令牌失效**

   - 检查数据库中的 `access_tokens` 表
   - 确认刷新令牌是否有效
   - 重新获取授权

2. **数据库连接错误**

   - 检查数据库配置
   - 确认数据库服务是否正常

3. **API 调用失败**
   - 检查网络连接
   - 确认 API 接口是否正常
   - 查看错误日志

### 日志查看

```python
from app.core.logger import logger

# 查看同步日志
logger.info("开始同步小红书创意数据")
```

## 扩展功能

### 1. 添加新字段

如需同步更多字段，可以修改 `_process_creativity_item` 函数中的数据映射：

```python
data = {
    # 现有字段...
    "new_field": safe_str(item.get("new_field")),
}
```

### 2. 自定义过滤条件

可以在 API 调用时添加过滤条件：

```python
from app.services.providers.xiaohongshu import FilterClause

# 只同步消费大于100元的创意
filter_clause = FilterClause(column="fee", operator=">", values=["100"])

params = GetCreativeOfflineReportParams(
    advertiser_id=advertiser_id,
    start_date=start_date,
    end_date=end_date,
    filters=[filter_clause]
)
```

### 3. 数据导出

```python
# 导出数据到CSV
import pandas as pd

async with get_session() as session:
    stmt = select(BrandXhsJgCreativityDataReport)
    result = await session.execute(stmt)
    records = result.scalars().all()

    data = [record.to_dict() for record in records]
    df = pd.DataFrame(data)
    df.to_csv("xiaohongshu_creativity_data.csv", index=False)
```

## 联系支持

如有问题或建议，请联系开发团队或查看相关文档。
