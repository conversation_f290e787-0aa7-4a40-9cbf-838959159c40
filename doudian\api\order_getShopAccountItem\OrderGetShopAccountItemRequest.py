# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_getShopAccountItem.param.OrderGetShopAccountItemParam import OrderGetShopAccountItemParam


class OrderGetShopAccountItemRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderGetShopAccountItemParam()

	def getUrlPath(self, ):
		return "/order/getShopAccountItem"

	def getParams(self, ):
		return self.params



