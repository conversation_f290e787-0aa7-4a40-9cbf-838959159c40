from decimal import Decimal
import time
from sqlalchemy.ext.asyncio import AsyncAttrs, create_async_engine
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import (
    Column,
    BigInteger,
    create_engine,
)
from app.core.config import settings
from sqlalchemy.ext.asyncio import async_sessionmaker
from sqlalchemy.orm import Mapped
from contextlib import asynccontextmanager
from sqlalchemy import event
from sqlalchemy.orm import sessionmaker
import asyncio

print(settings.OA)

# 配置异步引擎连接池参数
engine = create_async_engine(
    f"mysql+aiomysql://{settings.OA['USER']}:{settings.OA['PASSWORD']}@{settings.OA['HOST']}:{settings.OA['PORT']}/{settings.OA['DATABASE']}",
    # 连接池配置
    pool_size=10,  # 连接池大小
    max_overflow=20,  # 最大溢出连接数
    pool_pre_ping=True,  # 连接前检查连接有效性
    pool_recycle=3600,  # 连接回收时间（1小时）
    pool_timeout=30,  # 获取连接的超时时间
    # aiomysql 特定配置
    connect_args={
        "autocommit": False,
        "charset": "utf8mb4",
        "connect_timeout": 60,
        # 注意：aiomysql 不支持 read_timeout 和 write_timeout 参数
    },
)

# 配置同步引擎连接池参数
sync_engine = create_engine(
    f"mysql+pymysql://{settings.OA['USER']}:{settings.OA['PASSWORD']}@{settings.OA['HOST']}:{settings.OA['PORT']}/{settings.OA['DATABASE']}",
    # 连接池配置
    pool_size=5,  # 同步引擎连接池稍小
    max_overflow=10,  # 最大溢出连接数
    pool_pre_ping=True,  # 连接前检查连接有效性
    pool_recycle=3600,  # 连接回收时间（1小时）
    pool_timeout=30,  # 获取连接的超时时间
    # pymysql 特定配置
    connect_args={
        "autocommit": False,
        "charset": "utf8mb4",
        "connect_timeout": 60,
        "read_timeout": 30,
        "write_timeout": 30,
    },
)


def _to_dict(model):
    # 如果是 decimal 类型，则转换为 字符串
    result = {}
    for column in model.__table__.columns:
        if isinstance(getattr(model, column.name), Decimal):
            result[column.name] = str(getattr(model, column.name))
        else:
            result[column.name] = getattr(model, column.name)
    return result


class EmptyModel(AsyncAttrs, DeclarativeBase):
    def to_dict(self):
        return _to_dict(self)

    def __repr__(self):
        return f"<{self.__class__.__name__} {self.to_dict()}>"


class BaseModel(AsyncAttrs, DeclarativeBase):

    create_time: Mapped[int] = Column(BigInteger, default=int(time.time()))

    update_time: Mapped[int] = Column(BigInteger, default=int(time.time()))
    delete_time: Mapped[int] = Column(BigInteger)

    def to_dict(self):
        return _to_dict(self)

    def __repr__(self):
        return f"<{self.__class__.__name__} {self.to_dict()}>"


@event.listens_for(BaseModel, "before_insert", propagate=True)
def before_insert(mapper, connection, target):
    target.create_time = int(time.time())
    target.update_time = int(time.time())


@event.listens_for(BaseModel, "before_update", propagate=True)
def before_update(mapper, connection, target):
    target.update_time = int(time.time())


class Base(AsyncAttrs, DeclarativeBase):
    def to_dict(self):
        return _to_dict(self)


session_maker = async_sessionmaker(engine)
sync_session_maker = sessionmaker(sync_engine)


@asynccontextmanager
async def get_session(transaction: bool = False):
    """
    获取数据库会话的上下文管理器

    Args:
        transaction: 是否在事务中运行
    """
    session = None
    max_retries = 3

    for attempt in range(max_retries):
        try:
            session = session_maker()

            if not transaction:
                yield session
                return
            else:
                try:
                    yield session
                    # 如果没有异常，在事务模式下不会到达这里（由调用者控制提交）
                except Exception:
                    if session:
                        await session.rollback()
                    raise
                return

        except Exception as e:
            error_msg = str(e)
            is_connection_error = any(
                keyword in error_msg.lower()
                for keyword in [
                    "packet sequence number wrong",
                    "connection",
                    "timeout",
                    "lost connection",
                    "mysql server has gone away",
                    "can't connect",
                    "broken pipe",
                ]
            )

            if is_connection_error and attempt < max_retries - 1:
                # 连接错误且还有重试机会
                if session:
                    try:
                        await session.close()
                    except:
                        pass
                    session = None

                retry_delay = (attempt + 1) * 0.5  # 递增延迟
                await asyncio.sleep(retry_delay)
                continue
            else:
                raise
        finally:
            if session:
                await session.close()


def get_sync_session():
    return sync_session_maker()
