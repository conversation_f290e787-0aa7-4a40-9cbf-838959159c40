# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_isv_plead.param.ProductIsvPleadParam import ProductIsvPleadParam


class ProductIsvPleadRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductIsvPleadParam()

	def getUrlPath(self, ):
		return "/product/isv/plead"

	def getParams(self, ):
		return self.params



