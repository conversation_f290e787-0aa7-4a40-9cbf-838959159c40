"""
测试TV模块的屏蔽功能
"""
import pytest
from app.routers.bi.tv import filter_blocked_groups, BLOCKED_GROUPS, BLOCKED_DEPARTMENTS


class TestTVBlocking:
    """测试TV模块的屏蔽功能"""

    def test_filter_blocked_groups_by_group_name(self):
        """测试按小组名称屏蔽功能"""
        # 准备测试数据
        test_groups_data = [
            {
                "mallGroup": "其他分组",
                "department": ["电商一部"],
                "total": "1000.00",
                "dest": "2000.00"
            },
            {
                "mallGroup": "正常小组",
                "department": ["电商一部"],
                "total": "1500.00",
                "dest": "2500.00"
            },
            {
                "mallGroup": "另一个正常小组",
                "department": ["电商二部"],
                "total": "1200.00",
                "dest": "2200.00"
            }
        ]

        # 执行过滤
        filtered_data = filter_blocked_groups(test_groups_data)

        # 验证结果
        assert len(filtered_data) == 2
        group_names = [group["mallGroup"] for group in filtered_data]
        assert "其他分组" not in group_names
        assert "正常小组" in group_names
        assert "另一个正常小组" in group_names

    def test_filter_blocked_groups_by_department(self):
        """测试按部门屏蔽功能"""
        # 准备测试数据
        test_groups_data = [
            {
                "mallGroup": "供应链小组",
                "department": ["供应链部"],
                "total": "1000.00",
                "dest": "2000.00"
            },
            {
                "mallGroup": "正常小组",
                "department": ["电商一部"],
                "total": "1500.00",
                "dest": "2500.00"
            }
        ]

        # 执行过滤
        filtered_data = filter_blocked_groups(test_groups_data)

        # 验证结果
        assert len(filtered_data) == 1
        assert filtered_data[0]["mallGroup"] == "正常小组"

    def test_filter_blocked_groups_combined(self):
        """测试同时按小组名称和部门屏蔽功能"""
        # 准备测试数据
        test_groups_data = [
            {
                "mallGroup": "其他分组",
                "department": ["电商一部"],
                "total": "1000.00",
                "dest": "2000.00"
            },
            {
                "mallGroup": "供应链小组",
                "department": ["供应链部"],
                "total": "800.00",
                "dest": "1800.00"
            },
            {
                "mallGroup": "正常小组",
                "department": ["电商一部"],
                "total": "1500.00",
                "dest": "2500.00"
            }
        ]

        # 执行过滤
        filtered_data = filter_blocked_groups(test_groups_data)

        # 验证结果
        assert len(filtered_data) == 1
        assert filtered_data[0]["mallGroup"] == "正常小组"

    def test_filter_blocked_groups_with_string_department(self):
        """测试部门为字符串格式的情况"""
        # 准备测试数据
        test_groups_data = [
            {
                "mallGroup": "测试小组",
                "department": "供应链部,电商一部",
                "total": "1000.00",
                "dest": "2000.00"
            },
            {
                "mallGroup": "正常小组",
                "department": "电商一部",
                "total": "1500.00",
                "dest": "2500.00"
            }
        ]

        # 执行过滤
        filtered_data = filter_blocked_groups(test_groups_data)

        # 验证结果 - 包含供应链部的小组应该被过滤掉
        assert len(filtered_data) == 1
        assert filtered_data[0]["mallGroup"] == "正常小组"

    def test_blocked_groups_constant(self):
        """测试屏蔽小组常量是否正确设置"""
        assert "其他分组" in BLOCKED_GROUPS

    def test_blocked_departments_constant(self):
        """测试屏蔽部门常量是否正确设置"""
        assert "供应链部" in BLOCKED_DEPARTMENTS

    def test_filter_blocked_groups_empty_input(self):
        """测试空输入的情况"""
        filtered_data = filter_blocked_groups([])
        assert len(filtered_data) == 0

    def test_filter_blocked_groups_with_none_department(self):
        """测试部门为None的情况"""
        test_groups_data = [
            {
                "mallGroup": "测试小组",
                "department": None,
                "total": "1000.00",
                "dest": "2000.00"
            }
        ]

        # 执行过滤
        filtered_data = filter_blocked_groups(test_groups_data)

        # 验证结果 - 部门为None的小组应该被保留
        assert len(filtered_data) == 1
        assert filtered_data[0]["mallGroup"] == "测试小组"
