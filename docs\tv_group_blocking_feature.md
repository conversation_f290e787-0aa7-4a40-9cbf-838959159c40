# TV模块小组屏蔽功能

## 功能概述

在TV模块中新增了小组屏蔽功能，可以屏蔽指定的小组，使其不在各种统计和排名中显示。

## 功能特性

### 1. 屏蔽配置

在 `app/routers/bi/tv.py` 文件中，通过以下常量配置屏蔽的小组：

```python
# 需要屏蔽的小组列表
BLOCKED_GROUPS = ["其他分组"]
```

### 2. 屏蔽范围

小组屏蔽功能会影响以下API接口的数据：

- `/daily` - 每日销售数据
- `/monthy` - 月度销售数据  
- `/monthly_v2` - 月度销售数据V2版本

### 3. 屏蔽逻辑

屏蔽功能通过 `filter_blocked_groups()` 函数实现，该函数会：

1. **按小组名称屏蔽**：检查小组的 `mallGroup` 字段是否在 `BLOCKED_GROUPS` 列表中
2. **按部门屏蔽**：检查小组所属部门是否在 `BLOCKED_DEPARTMENTS` 列表中
3. **数据完整性**：正确处理部门字段为 `None`、字符串或列表的各种情况

### 4. 实现细节

#### 核心过滤函数

```python
def filter_blocked_groups(groups_data):
    """过滤属于屏蔽部门和屏蔽小组的数据"""
    filtered_groups = []
    for group in groups_data:
        # 检查小组名称是否在屏蔽列表中
        group_name = group.get("mallGroup", "")
        if group_name in BLOCKED_GROUPS:
            continue
            
        # 检查小组所属的部门是否包含被屏蔽的部门
        group_departments = group.get("department", [])
        if group_departments is None:
            group_departments = []
        elif isinstance(group_departments, str):
            group_departments = group_departments.split(",")

        # 如果小组的任何一个部门在屏蔽列表中，则过滤掉该小组
        if not any(
            dept and dept.strip() in BLOCKED_DEPARTMENTS for dept in group_departments
        ):
            filtered_groups.append(group)

    return filtered_groups
```

#### 应用范围

该过滤函数在以下场景中被调用：

1. **每日数据处理**：过滤小组销售数据
2. **月度数据处理**：过滤小组业绩统计
3. **排名计算**：过滤参与排名的小组
4. **实时销售统计**：过滤当日销售数据聚合

## 配置管理

### 添加新的屏蔽小组

要屏蔽新的小组，只需在 `BLOCKED_GROUPS` 列表中添加小组名称：

```python
BLOCKED_GROUPS = ["其他分组", "新屏蔽小组"]
```

### 移除屏蔽小组

从 `BLOCKED_GROUPS` 列表中移除对应的小组名称即可。

## 测试覆盖

功能包含完整的单元测试，测试文件：`tests/test_tv_blocking.py`

测试覆盖以下场景：
- 按小组名称屏蔽
- 按部门屏蔽
- 组合屏蔽（同时按小组和部门）
- 部门字段为字符串格式的处理
- 部门字段为None的处理
- 空输入处理
- 常量配置验证

## 注意事项

1. **数据一致性**：屏蔽功能会影响所有相关的统计数据，确保业务逻辑的一致性
2. **性能影响**：过滤操作在数据处理的早期阶段进行，对性能影响最小
3. **缓存更新**：由于使用了Redis缓存，配置更改后可能需要等待缓存过期或手动清除缓存
4. **权限控制**：屏蔽配置的修改需要代码部署，确保只有授权人员可以修改

## 兼容性

该功能与现有的部门屏蔽功能完全兼容，两种屏蔽机制可以同时生效。
