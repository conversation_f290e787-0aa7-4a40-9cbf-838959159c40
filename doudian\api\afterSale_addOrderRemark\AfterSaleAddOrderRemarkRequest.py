# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.afterSale_addOrderRemark.param.AfterSaleAddOrderRemarkParam import AfterSaleAddOrderRemarkParam


class AfterSaleAddOrderRemarkRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AfterSaleAddOrderRemarkParam()

	def getUrlPath(self, ):
		return "/afterSale/addOrderRemark"

	def getParams(self, ):
		return self.params



