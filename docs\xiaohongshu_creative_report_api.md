# 小红书创意离线报表 API 使用文档

## 概述

小红书创意离线报表 API 允许您获取广告创意的详细数据报表，包括展现、点击、消费、转化等各种指标。该 API 支持多种查询条件和数据细分维度。

## 重要说明

⚠️ **数据产出时间**: 离线核心数据（展点消）最早在次日 10 点产出，离线报表其他层级数据产出时间亦然。

## 接口信息

- **请求域名**: `https://adapi.xiaohongshu.com`
- **请求接口**: `/api/open/jg/data/report/offline/creative`
- **请求方式**: `POST`
- **数据格式**: `JSON`

## 快速开始

### 1. 基础查询

```python
import asyncio
from datetime import datetime, timedelta
from app.services.providers.xiaohongshu import (
    get_access_token,
    get_creative_offline_report,
    GetCreativeOfflineReportParams,
)

async def basic_query():
    # 获取访问令牌
    token_info = await get_access_token()
    access_token = token_info["access_token"]
    advertiser_id = token_info["approval_advertisers"][0]["advertiser_id"]

    # 查询最近7天的数据
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")

    params = GetCreativeOfflineReportParams(
        advertiser_id=advertiser_id,
        start_date=start_date,
        end_date=end_date,
        time_unit="DAY",  # 按天分组
        page_num=1,
        page_size=20
    )

    result = get_creative_offline_report(access_token, params)
    return result

# 运行查询
result = asyncio.run(basic_query())
```

### 2. 带过滤条件的查询

```python
from app.services.providers.xiaohongshu import FilterClause

async def filtered_query():
    token_info = await get_access_token()
    access_token = token_info["access_token"]
    advertiser_id = token_info["approval_advertisers"][0]["advertiser_id"]

    # 创建过滤条件：查询消费大于100元的创意
    filter_clause = FilterClause(
        column="fee",
        operator=">",
        values=["100"]
    )

    params = GetCreativeOfflineReportParams(
        advertiser_id=advertiser_id,
        start_date="2023-09-20",
        end_date="2023-09-21",
        filters=[filter_clause],
        sort_column="fee",  # 按消费排序
        sort="desc"  # 降序
    )

    result = get_creative_offline_report(access_token, params)
    return result
```

## 参数说明

### 必填参数

| 参数            | 类型 | 说明      | 示例         |
| --------------- | ---- | --------- | ------------ |
| `advertiser_id` | int  | 广告主 ID | 123456       |
| `start_date`    | str  | 开始时间  | "2023-09-20" |
| `end_date`      | str  | 结束时间  | "2023-09-21" |

### 可选参数

| 参数               | 类型               | 说明                                    | 默认值 |
| ------------------ | ------------------ | --------------------------------------- | ------ |
| `time_unit`        | str                | 时间维度：DAY/HOUR/SUMMARY              | "DAY"  |
| `marketing_target` | list[int]          | 营销目标过滤条件                        | None   |
| `bidding_strategy` | list[int]          | 出价方式过滤条件                        | None   |
| `optimize_target`  | list[int]          | 推广目标过滤条件                        | None   |
| `placement`        | list[int]          | 广告类型过滤条件                        | None   |
| `promotion_target` | list[int]          | 推广标的类型过滤条件                    | None   |
| `programmatic`     | list[int]          | 创意组合方式过滤条件                    | None   |
| `delivery_mode`    | list[int]          | 投放模式过滤条件                        | None   |
| `split_columns`    | list[str]          | 细分条件                                | None   |
| `sort_column`      | str                | 排序字段                                | None   |
| `sort`             | str                | 升降序：asc/desc                        | None   |
| `page_num`         | int                | 页数                                    | 1      |
| `page_size`        | int                | 页大小（最大 500）                      | 20     |
| `data_caliber`     | int                | 数据归因时间类型：0-点击时间/1-转化时间 | None   |
| `filters`          | list[FilterClause] | 过滤条件                                | None   |

## 过滤条件

### FilterClause 结构

```python
@dataclasses.dataclass
class FilterClause:
    column: str      # 筛选列
    operator: str    # 操作符
    values: list[str] # 值
```

### 支持的筛选列

- `creativityId`: 创意 ID
- `fee`: 消耗金额
- `impression`: 展示次数
- `click`: 点击量
- `interaction`: 笔记互动量

### 支持的操作符

- `>`: 大于
- `<`: 小于
- `in`: 等于

### 过滤条件示例

```python
# 查询消费大于100元的创意
filter1 = FilterClause(column="fee", operator=">", values=["100"])

# 查询点击量大于1000的创意
filter2 = FilterClause(column="click", operator=">", values=["1000"])

# 查询特定创意ID
filter3 = FilterClause(column="creativityId", operator="in", values=["123", "456"])

# 多条件过滤
params = GetCreativeOfflineReportParams(
    advertiser_id=advertiser_id,
    start_date="2023-09-20",
    end_date="2023-09-21",
    filters=[filter1, filter2]  # 同时满足两个条件
)
```

## 细分维度

使用 `split_columns` 参数可以按不同维度对数据进行分组：

### 支持的细分维度

- `marketingTarget`: 营销诉求
- `deliveryMode`: 投放模式
- `placement`: 广告类型
- `optimizeTarget`: 推广目标
- `biddingStrategy`: 出价方式
- `promotionTarget`: 推广标的类型
- `jumpType`: 创意跳转类型
- `countryName`: 国家
- `province`: 省份
- `city`: 城市
- `gender`: 性别
- `age`: 年龄
- `device`: 平台
- `targetDetail`: 精准定向
- `keyword`: 关键词
- `searchFeedType`: 搜索人群追投
- `itemId`: 商品
- `pageId`: 落地页
- `liveRedId`: 直播间笔记

### 细分查询示例

```python
# 按广告类型细分
params = GetCreativeOfflineReportParams(
    advertiser_id=advertiser_id,
    start_date="2023-09-20",
    end_date="2023-09-21",
    split_columns=["placement"]
)

# 按省份和城市细分
params = GetCreativeOfflineReportParams(
    advertiser_id=advertiser_id,
    start_date="2023-09-20",
    end_date="2023-09-21",
    split_columns=["province", "city"]
)
```

## 返回数据结构

### 响应格式

```json
{
    "code": 0,
    "success": true,
    "msg": "成功",
    "data": {
        "data_list": [...],
        "aggregation_data": {...}
    }
}
```

### 数据字段分类

#### 业务字段

- `campaign_id`: 计划 ID
- `campaign_name`: 计划名称
- `unit_id`: 单元 ID
- `unit_name`: 单元名称
- `creativity_id`: 创意 ID
- `creativity_name`: 创意名称
- `note_id`: 笔记 ID
- `time`: 时间

#### 基础指标

- `fee`: 消费（元）
- `impression`: 展现量
- `click`: 点击量
- `ctr`: 点击率
- `acp`: 平均点击成本
- `cpm`: 平均千次曝光

#### 笔记互动指标

- `like`: 点赞
- `comment`: 评论
- `collect`: 收藏
- `follow`: 关注
- `share`: 分享
- `interaction`: 互动量
- `cpi`: 平均互动成本

#### 电商转化指标

- `goods_visit`: 进店访问量
- `seller_visit`: 商品访客量
- `shopping_cart_add`: 商品加购量
- `goods_order`: 7 日下单订单量
- `rgmv`: 7 日下单金额
- `roi`: 7 日下单 ROI
- `success_goods_order`: 7 日支付订单量
- `purchase_order_gmv_7d`: 7 日支付金额
- `purchase_order_roi_7d`: 7 日支付 ROI

#### 销售线索指标

- `leads`: 表单提交
- `leads_cpl`: 表单成本
- `valid_leads`: 有效表单
- `phone_call_cnt`: 电话拨打
- `wechat_copy_cnt`: 微信复制

#### 私信营销指标

- `message_user`: 私信咨询人数
- `message_consult`: 私信咨询数
- `initiative_message`: 私信开口数

更多字段请参考 `CreativeReportData` 类型定义。

## 常见用例

### 1. 获取高 ROI 创意

```python
async def get_high_roi_creatives():
    # 查询ROI大于2的创意，按ROI降序排列
    filter_clause = FilterClause(column="roi", operator=">", values=["2"])

    params = GetCreativeOfflineReportParams(
        advertiser_id=advertiser_id,
        start_date=start_date,
        end_date=end_date,
        filters=[filter_clause],
        sort_column="roi",
        sort="desc",
        page_size=50
    )

    return get_creative_offline_report(access_token, params)
```

### 2. 分析不同广告类型的效果

```python
async def analyze_by_placement():
    params = GetCreativeOfflineReportParams(
        advertiser_id=advertiser_id,
        start_date=start_date,
        end_date=end_date,
        time_unit="SUMMARY",
        split_columns=["placement"],
        sort_column="fee",
        sort="desc"
    )

    return get_creative_offline_report(access_token, params)
```

### 3. 获取汇总数据

```python
async def get_summary_data():
    params = GetCreativeOfflineReportParams(
        advertiser_id=advertiser_id,
        start_date=start_date,
        end_date=end_date,
        time_unit="SUMMARY"  # 汇总模式
    )

    result = get_creative_offline_report(access_token, params)
    # 汇总数据在 result["data"]["aggregation_data"] 中
    return result
```

## 错误处理

```python
async def safe_query():
    try:
        result = get_creative_offline_report(access_token, params)

        if result.get("success"):
            data_list = result["data"]["data_list"]
            print(f"查询成功，返回 {len(data_list)} 条记录")
            return data_list
        else:
            print(f"查询失败: {result.get('msg', '未知错误')}")
            return None

    except Exception as e:
        print(f"请求异常: {e}")
        return None
```

## 注意事项

1. **数据延迟**: 离线数据最早在次日 10 点产出
2. **分页限制**: 单次查询最多返回 500 条记录
3. **时间范围**: 建议查询时间范围不要过长，避免数据量过大
4. **过滤条件**: 多个过滤条件之间是 AND 关系
5. **细分维度**: 商品、落地页、直播间笔记只能三选一进行细分

## 完整示例

参考 `examples/xiaohongshu_creative_report_example.py` 文件，其中包含了各种查询场景的完整示例代码。

## API 参考

更多详细的 API 参数和返回字段说明，请参考源码中的类型定义：

- `GetCreativeOfflineReportParams`: 查询参数
- `FilterClause`: 过滤条件
- `CreativeReportData`: 返回数据结构
