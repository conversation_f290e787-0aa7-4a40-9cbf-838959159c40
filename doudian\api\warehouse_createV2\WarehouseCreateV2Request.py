# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.warehouse_createV2.param.WarehouseCreateV2Param import WarehouseCreateV2Param


class WarehouseCreateV2Request(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = WarehouseCreateV2Param()

	def getUrlPath(self, ):
		return "/warehouse/createV2"

	def getParams(self, ):
		return self.params



