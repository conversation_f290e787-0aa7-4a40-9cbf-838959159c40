# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_mGetPlayInfo.param.MaterialMGetPlayInfoParam import MaterialMGetPlayInfoParam


class MaterialMGetPlayInfoRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialMGetPlayInfoParam()

	def getUrlPath(self, ):
		return "/material/mGetPlayInfo"

	def getParams(self, ):
		return self.params



