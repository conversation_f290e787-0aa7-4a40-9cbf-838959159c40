from datetime import datetime, timedelta, timezone
import json
from sqlalchemy import delete, select
from app.core.base_router import BaseRouter
from app.core.oa_database import (
    PddDailyReport,
    PddHourlyReport,
    PddJmsj,
    PddMall,
    PddTgsj,
    PddTgspDetails,
)
from app.schemas.response import ResponseModel
from app.core.database import db
from app.core.oa_database import get_session
from fastapi import HTTPException, Body
from pymongo import ReplaceOne
from pydantic import BaseModel, Field
import app.utils.tools as utils

router = BaseRouter()


@router.post(
    "/goods",
    summary="拼多多商品采集",
    description=(
        "批量采集拼多多商品数据。\n"
        "- 支持upsert操作\n"
        "- 如果商品已存在则更新，不存在则插入\n"
        "- 使用goodsId和日期作为唯一标识"
    ),
    response_model=ResponseModel,
    response_description="返回更新和插入的记录数量",
)
async def pdd_goods(
    data: list[dict] = Body(..., description="商品数据列表，每个商品包含goodsId等信息")
):
    goods_collection = db.rpa_data.pdd_goods

    if not data:
        return router.error(message="请求数据为空")
    # 当天0点
    date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    # 设置日期
    for item in data:
        item["date"] = date
    # 构建批量操作列表
    operations = [
        ReplaceOne({"goodsId": item.get("goodsId"), "date": date}, item, upsert=True)
        for item in data
    ]

    # 执行批量写入操作
    if operations:
        result = await goods_collection.bulk_write(operations)

    return router.success(
        data={
            "updated": result.modified_count,
            "upserted": result.upserted_count,
        }
    )


@router.post(
    "/activity",
    summary="拼多多活动采集",
    description=(
        "采集拼多多活动数据。\n"
        "- 支持单个或批量活动数据的upsert操作\n"
        "- 使用activity_goods_id作为唯一标识\n"
        "- 自动处理单条数据转换为列表"
    ),
    response_model=ResponseModel,
    response_description="返回更新和插入的记录数量",
)
async def pdd_activity(
    data: list[dict] = Body(
        ..., description="活动数据列表，每个活动包含activity_goods_id等信息"
    )
):
    activity_collection = db.rpa_data.pdd_activity

    if not data:
        return router.error(message="请求数据为空")
    if not isinstance(data, list):
        data = [data]

    operations = [
        ReplaceOne(
            {"activity_goods_id": item.get("activity_goods_id")}, item, upsert=True
        )
        for item in data
    ]

    if operations:
        result = await activity_collection.bulk_write(operations)

    return router.success(
        data={
            "updated": result.modified_count,
            "upserted": result.upserted_count,
        }
    )


@router.post(
    "/goods_dsr",
    summary="拼多多商品领航员数据采集",
    description=(
        "采集拼多多商品的领航员数据。\n"
        "- 包含商品的各项指标数据\n"
        "- 按日期进行存储\n"
        "- 使用goods_id和日期作为唯一标识"
    ),
    response_model=ResponseModel,
    response_description="返回更新和插入的记录数量",
)
async def pdd_goods_dsr(
    data: list[dict] = Body(
        ..., description="商品领航员数据列表，每条数据包含goods_id等信息"
    )
):
    goods_dsr_collection = db.rpa_data.pdd_goods_dsr
    if not data:
        return router.error(message="请求数据为空")
    # 当天0点
    date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    # 设置日期
    for item in data:
        item["date"] = date
    # 构建批量操作列表
    operations = [
        ReplaceOne({"goods_id": item.get("goods_id"), "date": date}, item, upsert=True)
        for item in data
    ]
    if operations:
        result = await goods_dsr_collection.bulk_write(operations)

    return router.success(
        data={
            "updated": result.modified_count,
            "upserted": result.upserted_count,
        }
    )


@router.post(
    "/tgsp_details",
    summary="拼多多推广商品明细数据采集",
    description="采集拼多多推广商品明细数据。",
    response_model=ResponseModel,
    response_description="返回操作成功状态",
)
async def pdd_tgsp_details(
    data: list[dict] = Body(
        ..., description="推广商品明细数据列表，每条数据包含推广商品明细信息"
    )
):
    if not data:
        return router.error(message="请求数据为空")
    try:
        async with get_session() as session:
            items_processed = 0
            for item_data in data:
                # 先检查是否存在相同date、mallId和goodsId的数据
                date = datetime.strptime(item_data.get("date"), "%Y-%m-%d").replace(
                    hour=0,
                    minute=0,
                    second=0,
                    microsecond=0,
                    tzinfo=timezone(timedelta(hours=8)),
                )

                # 查询现有记录
                query = select(PddTgspDetails).filter(
                    PddTgspDetails.date == date,
                    PddTgspDetails.mallId == item_data.get("mallId"),
                    PddTgspDetails.goodsId == item_data.get("goodsId"),
                    PddTgspDetails.adId == item_data.get("adId"),
                )
                result = await session.execute(query)
                existing_item = result.scalars().first()

                if existing_item:
                    # 如果记录存在，更新现有记录
                    item = existing_item
                else:
                    # 否则创建新记录
                    item = PddTgspDetails()

                item.pid = 0
                item.mallId = item_data.get("mallId")
                item.date = date
                item.type = item_data.get("type")
                item.goodsId = item_data.get("goodsId")
                item.goodsName = item_data.get("goodsName")
                item.adId = item_data.get("adId")
                item.adName = item_data.get("adName")
                item.thumbUrl = item_data.get("thumbUrl")
                item.adStatus = int(item_data.get("adStatus", 0))
                item.adGroupId = int(item_data.get("adGroupId", 0))
                item.adGroupName = item_data.get("adGroupName")
                item.todaySpend = utils.safe_get_float(item_data, "todaySpend")
                item.targetRoi = utils.safe_get_float(item_data, "targetRoi")
                item.planId = item_data.get("planId")
                item.beginTime = item_data.get("beginTime")
                item.endTime = item_data.get("endTime")
                item.spend = utils.safe_get_float(item_data, "spend")
                item.gmv = utils.safe_get_float(item_data, "gmv")
                item.roi = utils.safe_get_float(item_data, "roi")
                item.orderNum = int(item_data.get("orderNum", 0))
                item.costPerOrder = utils.safe_get_float(item_data, "costPerOrder")
                item.avgPayAmount = utils.safe_get_float(item_data, "avgPayAmount")
                item.globalTakeRate = utils.safe_get_float(item_data, "globalTakeRate")
                item.impression = int(item_data.get("impression", 0))
                item.click = int(item_data.get("click", 0))
                item.ctr = utils.safe_get_float(item_data, "ctr")
                item.cvr = utils.safe_get_float(item_data, "cvr")
                item.cpc = utils.safe_get_float(item_data, "cpc")
                item.cpm = utils.safe_get_float(item_data, "cpm")
                item.multiGoalMallFavNum = int(item_data.get("multiGoalMallFavNum", 0))
                item.multiGoalGoodsFavNum = int(
                    item_data.get("multiGoalGoodsFavNum", 0)
                )
                item.multiGoalInquiryNum = int(item_data.get("multiGoalInquiryNum", 0))
                item.orderSpend = utils.safe_get_float(item_data, "orderSpend")
                item.mallFavSpend = utils.safe_get_float(item_data, "mallFavSpend")
                item.goodsFavSpend = utils.safe_get_float(item_data, "goodsFavSpend")
                item.inquirySpend = utils.safe_get_float(item_data, "inquirySpend")
                item.inquiryNum = int(item_data.get("inquiryNum", 0))
                item.goodsFavNum = int(item_data.get("goodsFavNum", 0))
                item.costPerGoodsFav = utils.safe_get_float(
                    item_data, "costPerGoodsFav"
                )
                item.quickRefundGmv = utils.safe_get_float(item_data, "quickRefundGmv")
                item.netGmv = utils.safe_get_float(item_data, "netGmv")
                item.directGmv = utils.safe_get_float(item_data, "directGmv")
                item.indirectGmv = utils.safe_get_float(item_data, "indirectGmv")
                item.globalGmv = utils.safe_get_float(item_data, "globalGmv")
                item.orderSpendRoi = utils.safe_get_float(item_data, "orderSpendRoi")
                item.orderSpendNetRoi = utils.safe_get_float(
                    item_data, "orderSpendNetRoi"
                )
                item.quickRefundOrderNum = int(item_data.get("quickRefundOrderNum", 0))
                item.netOrderNum = int(item_data.get("netOrderNum", 0))
                item.directOrderNum = int(item_data.get("directOrderNum", 0))
                item.indirectOrderNum = int(item_data.get("indirectOrderNum", 0))
                item.avgDirectPayAmount = utils.safe_get_float(
                    item_data, "avgDirectPayAmount"
                )
                item.avgIndirectPayAmount = utils.safe_get_float(
                    item_data, "avgIndirectPayAmount"
                )
                item.modifyTime = datetime.now(tz=timezone(timedelta(hours=8)))
                session.add(item)
                items_processed += 1
            await session.commit()
            return router.success(data={"processed": items_processed})
    except Exception as e:
        await session.rollback()
        return router.error(message=str(e))


class DailyReportRequest(BaseModel):
    mallId: str = Field(..., description="店铺ID，用于唯一标识一个店铺")
    type: str = Field(..., description="数据类型，用于区分不同的数据来源或类别")
    date: datetime = Field(..., description="数据日期，精确到天")
    data: dict = Field(..., description="具体的数据内容，包含spend、gmv等指标")

    class Config:
        json_schema_extra = {
            "example": {
                "mallId": "12345",
                "type": "广告",
                "date": "2024-03-20T00:00:00",
                "data": {"spend": 1000, "gmv": 5000, "ctr": 0.02, "cvr": 0.03},
            }
        }


@router.post(
    "/daily_report",
    summary="拼多多每日数据采集",
    description=(
        "采集拼多多店铺的每日数据报表。\n"
        "- 支持自动处理数据单位：\n"
        "  * 金额类字段（spend、orderSpend、gmv等）自动除以1000\n"
        "  * 比率类字段（ctr、cvr等）自动乘以100\n"
        "- 会先删除同一天的历史数据，然后插入新数据\n"
        "- 使用店铺ID、类型和日期作为唯一标识"
    ),
    response_model=ResponseModel,
    response_description="返回操作成功状态",
)
async def pdd_daily_report(data: DailyReportRequest = Body(...)):
    try:
        async with get_session() as session:
            date = data.date.replace(hour=0, minute=0, second=0, microsecond=0)
            delete_query = delete(PddDailyReport).filter(
                PddDailyReport.date == date,
                PddDailyReport.mallId == data.mallId,
                PddDailyReport.type == data.type,
            )

            await session.execute(delete_query)
            item = PddDailyReport()
            itemKeys = item.to_dict().keys()
            divide_1000_keys = [
                "spend",
                "orderSpend",
                "gmv",
                "costPerOrder",
                "avgPayAmount",
            ]
            multiply_100_keys = ["ctr", "cvr", "globalTakeRate"]
            for key in itemKeys:
                if key in data.data:
                    value = data.data.get(key, 0)
                    if value is None and key in divide_1000_keys + multiply_100_keys:
                        value = 0
                    if key in divide_1000_keys:
                        setattr(item, key, value / 1000)
                    elif key in multiply_100_keys:
                        setattr(item, key, value * 100)
                    else:
                        setattr(item, key, value)
            item.mallId = data.mallId
            item.type = data.type
            item.date = date
            item.modifyTime = datetime.now()

            session.add(item)

            delete_query = delete(PddTgsj).filter(
                PddTgsj.date == date,
                PddTgsj.mallId == data.mallId,
                PddTgsj.type == data.type,
                PddTgsj.ajaxType == "queryDailyReport",
            )
            await session.execute(delete_query)
            mall_query = select(PddMall).filter(PddMall.mallId == data.mallId)
            mall_result = await session.execute(mall_query)
            mall = mall_result.scalars().first()
            tgsj_item = PddTgsj()
            tgsj_item.mallId = data.mallId
            if mall:
                tgsj_item.mallName = mall.mallName
            else:
                tgsj_item.mallName = "未知"
            tgsj_item.type = data.type
            tgsj_item.ajaxType = "queryDailyReport"
            tgsj_item.date = date
            tgsj_item.content = json.dumps(
                {
                    "errorCode": 1000,
                    "result": {
                        "sumReport": data.data,
                        "dailyReportList": [],
                        "externalFieldValues": [],
                        "total": 0,
                    },
                    "success": True,
                }
            )
            tgsj_item.modifyTime = datetime.now()
            session.add(tgsj_item)
            await session.commit()
    except Exception as e:
        await session.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    return router.success()


class HourlyReportRequest(BaseModel):
    mallId: str = Field(..., description="店铺ID，用于唯一标识一个店铺")
    type: str = Field(..., description="数据类型，用于区分不同的数据来源或类别")
    date: datetime = Field(..., description="数据日期，精确到天")
    data: list[dict] = Field(..., description="具体的小时维度数据内容")

    class Config:
        json_schema_extra = {
            "example": {
                "mallId": "12345",
                "type": "广告",
                "date": "2024-03-20T00:00:00",
                "data": [
                    {"hour": 10, "impression": 1000, "click": 100, "spend": 500},
                    {"hour": 11, "impression": 1000, "click": 100, "spend": 500},
                ],
            }
        }


@router.post(
    "/hourly_report",
    summary="拼多多小时数据采集",
    description=(
        "采集拼多多店铺的小时维度数据报表。\n"
        "- 按照以下字段作为唯一标识：\n"
        "  * 店铺ID\n"
        "  * 数据类型\n"
        "  * 日期\n"
        "- 会先删除同一天的历史数据，然后插入新数据"
    ),
    response_model=ResponseModel,
    response_description="返回操作成功状态",
)
async def pdd_hourly_report(data: HourlyReportRequest = Body(...)):
    try:
        async with get_session() as session:
            date = data.date.replace(hour=0, minute=0, second=0, microsecond=0)
            delete_query = delete(PddHourlyReport).filter(
                PddHourlyReport.date == date,
                PddHourlyReport.mallId == data.mallId,
                PddHourlyReport.type == data.type,
            )
            await session.execute(delete_query)
            items = []
            for data_item in data.data:
                item = PddHourlyReport()
                itemKeys = item.to_dict().keys()
                for key in itemKeys:
                    if key in data_item:
                        setattr(item, key, data_item.get(key, None))
                item.mallId = data.mallId
                item.type = data.type
                item.date = date
                item.modifyTime = datetime.now()
                items.append(item)
            session.add_all(items)
            await session.commit()
    except Exception as e:
        await session.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    return router.success()


class JmsjRequest(BaseModel):
    mallId: str = Field(..., description="店铺ID，用于唯一标识一个店铺")
    mallName: str = Field(..., description="店铺名称")
    type: str = Field(..., description="数据类型，用于区分不同的数据来源或类别")
    pageUrl: str = Field(..., description="页面网址")
    ttfUrl: str = Field(..., description="ttf网址")
    ttfName: str = Field(..., description="ttf文件名")
    ajaxType: str = Field(..., description="AJAX类型")
    ajax: str = Field(..., description="AJAX")
    date: datetime = Field(..., description="数据日期，精确到天")
    content: str = Field(..., description="AJAX返回结果")
    content2: str = Field(None, description="AJAX返回结果（未解密）")


@router.post(
    "/jmsj",
    summary="拼多多加密数据采集",
    description="采集拼多多店铺的加密数据报表。",
    response_model=ResponseModel,
    response_description="返回操作成功状态",
)
async def pdd_jmsj(data: JmsjRequest = Body(...)):
    """
    拼多多加密数据采集
    """
    try:
        date = data.date.replace(hour=0, minute=0, second=0, microsecond=0)
        async with get_session() as session:
            item_query = select(PddJmsj).filter(
                PddJmsj.date == date,
                PddJmsj.mallId == data.mallId,
                PddJmsj.type == data.type,
                PddJmsj.ajaxType == data.ajaxType,
            )
            query_result = await session.execute(item_query)
            item = query_result.scalars().first()
            if not item:
                item = PddJmsj()
                session.add(item)
            item.mallId = data.mallId
            item.mallName = data.mallName
            item.type = data.type
            item.pageUrl = data.pageUrl
            item.ttfUrl = data.ttfUrl
            item.ttfName = data.ttfName
            item.ajaxType = data.ajaxType
            item.ajax = data.ajax
            item.date = date
            item.content = data.content
            item.content2 = data.content2
            item.modifyTime = datetime.now()
            await session.commit()
    except Exception as e:
        await session.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    return router.success()


class GetPddJmsjRequest(BaseModel):
    mallId: str = Field(..., description="店铺ID，用于唯一标识一个店铺")
    type: str = Field(..., description="数据类型，用于区分不同的数据来源或类别")
    ajaxType: str = Field(..., description="AJAX类型")
    date: datetime = Field(..., description="数据日期，精确到天")


@router.post(
    "/get_pdd_jmsj",
    summary="获取拼多多加密数据",
    description="获取拼多多店铺的加密数据报表。",
    response_model=ResponseModel,
    response_description="返回操作成功状态",
)
async def get_pdd_jmsj(data: GetPddJmsjRequest = Body(...)):
    """
    获取拼多多加密数据
    """
    try:
        date = data.date.replace(hour=0, minute=0, second=0, microsecond=0)
        async with get_session() as session:
            item_query = select(PddJmsj).filter(
                PddJmsj.date == date,
                PddJmsj.mallId == data.mallId,
                PddJmsj.type == data.type,
                PddJmsj.ajaxType == data.ajaxType,
            )
            query_result = await session.execute(item_query)
            item = query_result.scalars().first()
            if not item:
                return router.error(message="数据不存在")
            dic = item.to_dict()
            return router.success(data=dic)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
