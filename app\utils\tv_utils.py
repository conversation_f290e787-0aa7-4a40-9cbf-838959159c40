from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List
from functools import wraps
from .redis_utils import RedisClient

# 常量定义
tz = timezone(timedelta(hours=8))


def tv_cache_redis(ttl: int = 120):
    """TV模块专用的Redis缓存装饰器，使用redis_utils中的实现"""

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 在函数调用时动态生成缓存键
            now = datetime.now(tz=tz)
            cache_key = f"DATAPILOT:TV:{func.__name__}:{now.strftime('%Y-%m')}"

            # 尝试从缓存获取
            cached_result = await RedisClient.get(cache_key)
            if cached_result is not None:
                return cached_result

            # 调用原始函数
            result = await func(*args, **kwargs)

            # 将结果存入缓存
            await RedisClient.set(cache_key, result, ex=ttl)

            return result

        return wrapper

    return decorator


def calculate_increment_rate(current: float, previous: float) -> float:
    """计算增长率"""
    return round((current - previous) / previous, 2) if previous > 0 else 0


def calculate_increment_multiplier(current: float, previous: float) -> float | None:
    """计算增长倍数（1 + 增长率）"""
    if previous > 0:
        return round(current / previous, 2)
    elif current == 0:
        return 1.0
    else:
        # 当previous为0但current大于0时（新的小组），返回None表示空的增量百分比
        return None


def format_float(value: Any) -> float:
    """格式化浮点数"""
    return float(value) if value else 0


def get_today_start() -> datetime:
    """获取当天0点时间"""
    return datetime.now().replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=tz)


def calculate_group_rankings(
    groups_data: List[Dict[str, Any]], sort_key: str = "increment_rate"
) -> List[Dict[str, Any]]:
    """计算小组排名"""
    # 将小组分为三组：有正增量百分比的、有零或负增量百分比的、新小组（增量百分比为None）
    groups_with_positive_rate = [
        group
        for group in groups_data
        if group[sort_key] is not None and group[sort_key] > 0
    ]
    groups_with_zero_or_negative_rate = [
        group
        for group in groups_data
        if group[sort_key] is not None and group[sort_key] <= 0
    ]
    new_groups = [group for group in groups_data if group[sort_key] is None]

    # 对有正增量百分比的小组按增量百分比排序
    sorted_groups_with_positive_rate = sorted(
        groups_with_positive_rate, key=lambda x: x[sort_key], reverse=True
    )

    # 对有零或负增量百分比的小组按当前销售额排序（如果有current_total字段的话）
    if groups_with_zero_or_negative_rate:
        if "current_total" in groups_with_zero_or_negative_rate[0]:
            sorted_groups_with_zero_or_negative_rate = sorted(
                groups_with_zero_or_negative_rate,
                key=lambda x: x.get("current_total", 0),
                reverse=True,
            )
        else:
            # 如果没有current_total字段，按增量百分比排序（从高到低）
            sorted_groups_with_zero_or_negative_rate = sorted(
                groups_with_zero_or_negative_rate,
                key=lambda x: x[sort_key],
                reverse=True,
            )
    else:
        sorted_groups_with_zero_or_negative_rate = []

    # 对新小组按当前销售额排序（如果有current_total字段的话）
    if new_groups and "current_total" in new_groups[0]:
        sorted_new_groups = sorted(
            new_groups, key=lambda x: x.get("current_total", 0), reverse=True
        )
    else:
        sorted_new_groups = new_groups

    # 合并排序结果：有正增量百分比的在前，有零或负增量百分比的在中间，新小组在后
    sorted_groups = (
        sorted_groups_with_positive_rate
        + sorted_groups_with_zero_or_negative_rate
        + sorted_new_groups
    )

    # 分配排名
    for i, item in enumerate(sorted_groups):
        item["rank"] = i + 1
    return sorted_groups


def process_group_champion_counts(
    group_data: Dict[str, Dict[str, Any]], date_range: List[str]
) -> Dict[str, int]:
    """计算小组冠军次数"""
    group_daily_champion = {}
    for date in date_range:
        daily_groups = {}
        for group, data in group_data.items():
            if date not in data["raw_data"]:
                continue
            total = format_float(data["raw_data"][date])
            daily_groups[group] = {
                "total": total,
                "rate": 0 if data["dest"] == 0 else round(total / data["dest"], 4),
            }

        if daily_groups:
            champion = max(
                daily_groups.items(), key=lambda x: (x[1]["rate"], x[1]["total"])
            )
            group_name = champion[0]
            group_daily_champion[group_name] = (
                group_daily_champion.get(group_name, 0) + 1
            )

    return group_daily_champion
