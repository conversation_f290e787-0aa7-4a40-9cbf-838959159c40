BASE_URL = "https://gateway.kfch.cn"
import asyncio
import aiohttp


async def checkPermission(user_id: str, permission: str) -> bool:
    """检查用户权限"""
    url = f"{BASE_URL}/api/permission/user/check/{user_id}?permission={permission}"
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            res = await response.json()
            return res.get("data", False)


async def test():
    print(await checkPermission("148", "BI:TV"))


if __name__ == "__main__":
    asyncio.run(test())
