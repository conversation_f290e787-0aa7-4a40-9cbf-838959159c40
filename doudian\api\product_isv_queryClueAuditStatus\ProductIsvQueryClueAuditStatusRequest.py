# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_isv_queryClueAuditStatus.param.ProductIsvQueryClueAuditStatusParam import ProductIsvQueryClueAuditStatusParam


class ProductIsvQueryClueAuditStatusRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductIsvQueryClueAuditStatusParam()

	def getUrlPath(self, ):
		return "/product/isv/queryClueAuditStatus"

	def getParams(self, ):
		return self.params



