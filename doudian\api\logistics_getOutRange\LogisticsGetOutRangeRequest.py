# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_getOutRange.param.LogisticsGetOutRangeParam import LogisticsGetOutRangeParam


class LogisticsGetOutRangeRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsGetOutRangeParam()

	def getUrlPath(self, ):
		return "/logistics/getOutRange"

	def getParams(self, ):
		return self.params



