# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.iop_waybillReturn.param.IopWaybillReturnParam import IopWaybillReturnParam


class IopWaybillReturnRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = IopWaybillReturnParam()

	def getUrlPath(self, ):
		return "/iop/waybillReturn"

	def getParams(self, ):
		return self.params



