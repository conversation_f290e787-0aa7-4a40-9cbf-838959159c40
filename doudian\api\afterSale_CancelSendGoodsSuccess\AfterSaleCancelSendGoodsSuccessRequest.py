# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.afterSale_CancelSendGoodsSuccess.param.AfterSaleCancelSendGoodsSuccessParam import AfterSaleCancelSendGoodsSuccessParam


class AfterSaleCancelSendGoodsSuccessRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AfterSaleCancelSendGoodsSuccessParam()

	def getUrlPath(self, ):
		return "/afterSale/CancelSendGoodsSuccess"

	def getParams(self, ):
		return self.params



