FROM ghcr.io/astral-sh/uv:python3.13-alpine AS base

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV FORCE_COLOR=1

# 设置 pip 源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple

# 只复制依赖文件
COPY pyproject.toml .   

# 安装Python依赖
RUN uv pip install . --system

# 最终镜像
FROM base

WORKDIR /app

# 设置环境变量
ENV APP_ENV=production
ENV TZ=Asia/Shanghai

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# 复制应用代码
COPY . /app/

# 暴露端口
EXPOSE 8000

# 启动命令
# CMD ["fastapi", "run", "--workers", "8", "--host", "0.0.0.0", "--port", "8000"] 
CMD [ "python", "run.py" ]
# CMD [ "uv", "run", "run.py" ]