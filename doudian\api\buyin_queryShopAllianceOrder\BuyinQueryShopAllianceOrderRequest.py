# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.buyin_queryShopAllianceOrder.param.BuyinQueryShopAllianceOrderParam import BuyinQueryShopAllianceOrderParam


class BuyinQueryShopAllianceOrderRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = BuyinQueryShopAllianceOrderParam()

	def getUrlPath(self, ):
		return "/buyin/queryShopAllianceOrder"

	def getParams(self, ):
		return self.params



