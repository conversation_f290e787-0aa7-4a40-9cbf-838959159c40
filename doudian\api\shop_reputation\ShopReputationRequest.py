# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.shop_reputation.param.ShopReputationParam import ShopReputationParam


class ShopReputationRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ShopReputationParam()

	def getUrlPath(self, ):
		return "/shop/reputation"

	def getParams(self, ):
		return self.params



