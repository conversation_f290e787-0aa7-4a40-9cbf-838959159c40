# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.afterSale_returnGoodsToWareHouseSuccess.param.AfterSaleReturnGoodsToWareHouseSuccessParam import AfterSaleReturnGoodsToWareHouseSuccessParam


class AfterSaleReturnGoodsToWareHouseSuccessRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AfterSaleReturnGoodsToWareHouseSuccessParam()

	def getUrlPath(self, ):
		return "/afterSale/returnGoodsToWareHouseSuccess"

	def getParams(self, ):
		return self.params



