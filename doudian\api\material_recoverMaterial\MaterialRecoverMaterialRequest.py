# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_recoverMaterial.param.MaterialRecoverMaterialParam import MaterialRecoverMaterialParam


class MaterialRecoverMaterialRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialRecoverMaterialParam()

	def getUrlPath(self, ):
		return "/material/recoverMaterial"

	def getParams(self, ):
		return self.params



