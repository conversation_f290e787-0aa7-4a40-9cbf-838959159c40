# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.buyin_queryShopSelfAuthors.param.BuyinQueryShopSelfAuthorsParam import BuyinQueryShopSelfAuthorsParam


class BuyinQueryShopSelfAuthorsRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = BuyinQueryShopSelfAuthorsParam()

	def getUrlPath(self, ):
		return "/buyin/queryShopSelfAuthors"

	def getParams(self, ):
		return self.params



