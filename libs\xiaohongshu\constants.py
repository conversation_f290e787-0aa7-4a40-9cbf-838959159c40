"""
小红书聚光平台 API 常量定义
"""

# 小红书聚光平台API基础URL
BASE_URL = "https://adapi.xiaohongshu.com"

# API端点
ENDPOINTS = {
    # 认证相关
    "refresh_token": "/api/open/oauth2/refresh_token",
    # 广告计划
    "campaign_list": "/api/open/jg/campaign/list",
    # 创意相关
    "creativity_search": "/api/open/jg/creativity/search",
    # 报表相关
    "creative_offline_report": "/api/open/jg/data/report/offline/creative",
    "note_offline_report": "/api/open/jg/data/report/offline/note",
    # 子账号
    "sub_account_list": "/api/open/jg/account/sub/page",
}

# 时间维度选项
TIME_UNITS = {
    "DAY": "DAY",  # 分天
    "HOUR": "HOUR",  # 分时
    "SUMMARY": "SUMMARY",  # 汇总
}

# 排序选项
SORT_OPTIONS = {
    "ASC": "asc",  # 升序
    "DESC": "desc",  # 降序
}

# 过滤操作符
FILTER_OPERATORS = {
    "GREATER_THAN": ">",  # 大于
    "LESS_THAN": "<",  # 小于
    "EQUALS": "in",  # 等于
}

# 营销目标映射
MARKETING_TARGETS = {
    3: "商品销量",
    4: "产品种草",
    8: "直播推广",
    9: "客资收集",
    10: "抢占关键词",
    13: "种草直达",
    14: "直播预热",
    15: "店铺拉新",
    16: "应用推广",
}

# 广告类型映射
PLACEMENT_TYPES = {
    1: "信息流",
    2: "搜索推广",
    4: "全站智投",
    7: "视频内流",
}

# 默认分页配置
DEFAULT_PAGE_CONFIG = {
    "page_num": 1,
    "page_size": 20,
    "max_page_size": 500,
}

# 缓存配置
CACHE_CONFIG = {
    "token_cache_key": "DATAPILOT:XIAOHONGSHU:TOKEN_INFO",
    "token_lock_key": "DATAPILOT:XIAOHONGSHU:TOKEN_INFO:LOCK",
    "default_expire_seconds": 600,  # 10分钟内过期判断
    "min_cache_seconds": 60,  # 最小缓存时间
    "lock_timeout": 10,  # 分布式锁超时时间
}
