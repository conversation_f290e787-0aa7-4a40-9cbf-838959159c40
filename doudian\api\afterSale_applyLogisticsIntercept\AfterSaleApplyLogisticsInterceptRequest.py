# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.afterSale_applyLogisticsIntercept.param.AfterSaleApplyLogisticsInterceptParam import AfterSaleApplyLogisticsInterceptParam


class AfterSaleApplyLogisticsInterceptRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AfterSaleApplyLogisticsInterceptParam()

	def getUrlPath(self, ):
		return "/afterSale/applyLogisticsIntercept"

	def getParams(self, ):
		return self.params



