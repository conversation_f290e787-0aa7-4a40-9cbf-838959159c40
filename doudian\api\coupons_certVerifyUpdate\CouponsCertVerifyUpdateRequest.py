# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.coupons_certVerifyUpdate.param.CouponsCertVerifyUpdateParam import CouponsCertVerifyUpdateParam


class CouponsCertVerifyUpdateRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = CouponsCertVerifyUpdateParam()

	def getUrlPath(self, ):
		return "/coupons/certVerifyUpdate"

	def getParams(self, ):
		return self.params



