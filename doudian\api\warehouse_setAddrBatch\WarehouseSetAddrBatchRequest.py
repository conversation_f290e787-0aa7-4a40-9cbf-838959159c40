# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.warehouse_setAddrBatch.param.WarehouseSetAddrBatchParam import WarehouseSetAddrBatchParam


class WarehouseSetAddrBatchRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = WarehouseSetAddrBatchParam()

	def getUrlPath(self, ):
		return "/warehouse/setAddrBatch"

	def getParams(self, ):
		return self.params



