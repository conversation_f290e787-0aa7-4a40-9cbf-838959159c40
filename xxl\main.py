import configparser
from datetime import datetime, timedelta
import time
import json

from pyxxl import ExecutorConfig, PyxxlRunner
from pyxxl.ctx import g
from app.services.providers import business_annual_planning, doudian, product_batch
from app.services.update_note_cost_flow import update_note_cost_flow_from_report

# 读取配置文件
config = configparser.ConfigParser()
config.read("./xxl/config.ini")
config = ExecutorConfig(
    xxl_admin_baseurl=config["xxl-job"]["xxl_admin_baseurl"],
    executor_app_name=config["xxl-job"]["executor_app_name"],
    # executor_url=config["xxl-job"]["executor_host"],
    access_token=config["xxl-job"]["access_token"],
    task_timeout=60 * 20,
    graceful_timeout=60 * 10,
)

app = PyxxlRunner(config)


@app.register(name="定时统计批号数据")
async def generate_batch_data():
    result = await product_batch.generate()
    return json.dumps(
        {
            "更新": result["update_count"],
            "新增": result["insert_count"],
            "未变": result["unchange_count"],
            "总数": result["total_count"],
        },
        ensure_ascii=False,
    )


@app.register(name="分配批号数据")
async def allocate_batch_data():
    t = time.time()
    g.logger.info("开始分配批号数据")
    product_batch.log_func = g.logger.info
    await product_batch.allocate_to_delivery()
    time_cost1 = format_time(time.time() - t)
    g.logger.info(f"分配批号数据成功, 耗时: {time_cost1}")
    t = time.time()
    # 重新统计并分配负数批号数据
    await re_statistic_and_allocate_negative_batch_number()
    time_cost2 = format_time(time.time() - t)
    g.logger.info(f"重新统计并分配负数批号数据成功, 耗时: {time_cost2}")

    return f"分配批号数据成功, 耗时: {time_cost1}, 重新统计并分配负数批号数据成功, 耗时: {time_cost2}"


@app.register(name="重新统计并分配负数批号数据")
async def re_statistic_and_allocate_negative_batch_number():
    t = time.time()
    g.logger.info("开始重新统计并分配负数批号数据")
    await product_batch.re_statistic_and_allocate_negative_batch_number()
    time_cost = format_time(time.time() - t)
    g.logger.info(f"重新统计并分配负数批号数据成功, 耗时: {time_cost}")
    return f"重新统计并分配负数批号数据成功, 耗时: {time_cost}"


@app.register(name="更新业务年度规划（每月1号）")
def update_business_annual_planning():
    t = time.time()
    g.logger.info("开始更新业务年度规划")

    business_annual_planning.update_business_annual_planning()
    time_cost = format_time(time.time() - t)
    g.logger.info(f"更新业务年度规划成功, 耗时: {time_cost}")
    return f"更新业务年度规划成功, 耗时: {time_cost}"


@app.register(name="抖店每日数据统计")
async def doudian_daily_statistics():
    t = time.time()
    info("开始统计抖店每日数据")
    await doudian.executeStatisticsDailyData()
    # 昨天
    start_date = datetime.now() - timedelta(days=1)
    end_date = datetime.now()
    await doudian.executeStatisticsData(
        type="weekly",
        start_date=start_date,
        end_date=end_date,
    )
    await doudian.executeStatisticsData(
        type="monthly",
        start_date=start_date,
        end_date=end_date,
    )
    time_cost = format_time(time.time() - t)
    info(f"统计抖店每日数据成功, 耗时: {time_cost}")
    return f"统计抖店每日数据成功, 耗时: {time_cost}"


@app.register(name="小红书每日数据同步和汇总")
async def xiaohongshu_daily_sync_and_summary():
    """
    小红书每日数据同步和汇总任务
    1. 同步创意基础信息
    2. 同步昨日的创意数据
    3. 生成汇总数据
    """
    t = time.time()
    g.logger.info("开始执行小红书每日数据同步和汇总")

    try:
        # 导入小红书相关模块
        from app.services.providers.xiaohongshu import (
            sync_xiaohongshu_creativity_info,
            sync_xiaohongshu_creativity_data,
            generate_xiaohongshu_overall_data_report,
        )

        # 第一步：同步创意基础信息
        g.logger.info("第一步：开始同步创意基础信息")
        info_start_time = time.time()

        info_result = await sync_xiaohongshu_creativity_info()

        info_time_cost = format_time(time.time() - info_start_time)

        if info_result.get("success"):
            g.logger.info(f"创意基础信息同步成功, 耗时: {info_time_cost}")
            g.logger.info(
                f"基础信息统计 - 处理: {info_result.get('total_processed', 0)}, "
                f"新增: {info_result.get('total_inserted', 0)}, "
                f"更新: {info_result.get('total_updated', 0)}"
            )
        else:
            g.logger.warning(
                f"创意基础信息同步失败: {info_result.get('error', '未知错误')}"
            )
            g.logger.warning("继续执行创意数据同步...")

        # 第二步：同步昨日的创意数据
        g.logger.info("第二步：开始同步昨日的创意数据")
        sync_start_time = time.time()

        sync_result = await sync_xiaohongshu_creativity_data()

        sync_time_cost = format_time(time.time() - sync_start_time)

        if sync_result.get("success"):
            g.logger.info(f"创意数据同步成功, 耗时: {sync_time_cost}")
            g.logger.info(
                f"数据同步统计 - 处理: {sync_result.get('total_processed', 0)}, "
                f"新增: {sync_result.get('total_inserted', 0)}, "
                f"更新: {sync_result.get('total_updated', 0)}"
            )

            # 第三步：生成汇总数据
            g.logger.info("第三步：开始生成汇总数据")
            summary_start_time = time.time()

            summary_result = await generate_xiaohongshu_overall_data_report(
                force_regenerate=True  # 强制重新生成，确保数据最新
            )

            summary_time_cost = format_time(time.time() - summary_start_time)

            if summary_result.get("success"):
                g.logger.info(f"汇总数据生成成功, 耗时: {summary_time_cost}")
                g.logger.info(
                    f"汇总统计 - 处理: {summary_result.get('total_processed', 0)}, "
                    f"新增: {summary_result.get('total_inserted', 0)}, "
                    f"更新: {summary_result.get('total_updated', 0)}"
                )

                total_time_cost = format_time(time.time() - t)
                g.logger.info(
                    f"小红书每日数据同步和汇总完成, 总耗时: {total_time_cost}"
                )

                # 构建返回信息
                info_status = "成功" if info_result.get("success") else "失败"
                return (
                    f"小红书每日数据同步和汇总完成! "
                    f"基础信息同步: {info_status}({info_time_cost}), "
                    f"创意数据同步: 成功({sync_time_cost}), "
                    f"汇总数据生成: 成功({summary_time_cost}), "
                    f"总耗时: {total_time_cost}"
                )
            else:
                error_msg = (
                    f"汇总数据生成失败: {summary_result.get('error', '未知错误')}"
                )
                g.logger.error(error_msg)
                return f"任务部分失败 - 创意数据同步成功但汇总失败: {error_msg}"
        else:
            error_msg = f"创意数据同步失败: {sync_result.get('error', '未知错误')}"
            g.logger.error(error_msg)
            return f"任务失败 - {error_msg}"

    except Exception as e:
        error_msg = f"小红书数据同步和汇总过程中发生异常: {str(e)}"
        g.logger.error(error_msg)
        import traceback

        g.logger.error(traceback.format_exc())
        return error_msg


@app.register(name="更新笔记流量推广费")
async def update_xiaohongshu_note_cost_flow():
    """
    更新小红书笔记流量推广费任务
    通过小红书笔记离线报表API获取消费金额数据，并更新数据库中对应笔记的流量推广费字段
    """
    t = time.time()
    g.logger.info("开始更新小红书笔记流量推广费")

    try:
        # 获取昨天的日期作为结束日期
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

        # 调用更新服务，从2020-01-01开始到昨天的数据
        result = await update_note_cost_flow_from_report(
            start_date="2020-01-01",
            end_date=yesterday,
            time_unit="SUMMARY",  # 使用汇总模式获取总计数据
        )

        time_cost = format_time(time.time() - t)

        # 计算成功率
        unique_notes = result.get("unique_notes", 0)
        success_rate = (
            (result["success_count"] / unique_notes * 100) if unique_notes > 0 else 0
        )

        # 构建格式化的返回字符串
        status = (
            "成功"
            if result["success_count"] > 0 or result["error_count"] == 0
            else "部分成功"
        )

        return_msg = f"笔记流量推广费更新{status}，耗时: {time_cost} | "
        return_msg += f"📊 数据统计: 成功更新 {result['success_count']} 条，记录不存在 {result.get('not_found_count', 0)} 条，异常错误 {result.get('exception_error_count', 0)} 条 | "
        return_msg += (
            f"📈 成功率: {success_rate:.1f}%，总失败数: {result['error_count']} 条 | "
        )
        return_msg += f"🔢 API数据: 获取 {result['total_notes']} 条记录，涉及 {unique_notes} 个唯一笔记 | "
        return_msg += f"📅 时间范围: 2020-01-01 至 {yesterday}"

        # 如果有错误详情，添加错误分析
        error_details = result.get("error_details", {})
        if error_details.get("total_errors", 0) > 0:
            error_types_cn = {
                "record_not_found": "记录不存在",
                "database_error": "数据库错误",
                "connection_error": "连接错误",
                "timeout_error": "超时错误",
                "validation_error": "验证错误",
                "unknown_error": "未知错误",
            }

            return_msg += " | 🔍 错误分析: "
            error_parts = []
            for error_type, count in error_details.get("error_types", {}).items():
                error_type_cn = error_types_cn.get(error_type, error_type)
                error_parts.append(f"{error_type_cn} {count} 条")
            return_msg += "，".join(error_parts)

        # 记录详细日志
        if result["success_count"] > 0:
            g.logger.info(
                f"笔记流量推广费更新成功 - 耗时: {time_cost}, "
                f"成功: {result['success_count']} 条, "
                f"记录不存在: {result.get('not_found_count', 0)} 条, "
                f"异常错误: {result.get('exception_error_count', 0)} 条, "
                f"成功率: {success_rate:.1f}%, "
                f"API获取: {result['total_notes']} 条, "
                f"唯一笔记: {unique_notes} 个"
            )
        else:
            g.logger.warning(
                f"笔记流量推广费更新完成但无数据更新 - 耗时: {time_cost}, "
                f"总失败: {result['error_count']} 条 (记录不存在: {result.get('not_found_count', 0)}, "
                f"异常错误: {result.get('exception_error_count', 0)}), "
                f"API获取: {result['total_notes']} 条"
            )

        return return_msg

    except Exception as e:
        time_cost = format_time(time.time() - t)
        error_msg = f"更新笔记流量推广费时发生异常: {str(e)}"
        g.logger.error(error_msg)
        import traceback

        g.logger.error(traceback.format_exc())

        # 异常情况返回格式化字符串
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
        return f"笔记流量推广费更新失败，耗时: {time_cost} | ❌ 错误信息: {str(e)} | 📅 时间范围: 2020-01-01 至 {yesterday}"


def format_time(t):
    # 将秒数转换为时分秒，如果某个单位为0，则不显示，秒保留 2 位小数，分和小时不保留小数
    m, s = divmod(t, 60)
    h, m = divmod(m, 60)
    if h == 0:
        if m == 0:
            return f"{s:.2f}秒"
        return f"{m}分{s:.2f}秒"
    return f"{h}小时{m}分{s:.2f}秒"


def info(msg):
    if __name__ == "__main__":
        print(msg)
    else:
        g.logger.info(msg)


def error(msg):
    if g.logger:
        g.logger.error(msg)
    else:
        print(msg)


def warning(msg):
    if g.logger:
        g.logger.warning(msg)
    else:
        print(msg)


def register():
    while True:
        try:
            app.run_executor()
        except Exception as e:
            g.logger.error(f"注册失败: {e}")
            time.sleep(3)


def main():
    import asyncio

    asyncio.run(doudian_daily_statistics())


if __name__ == "__main__":
    main()
