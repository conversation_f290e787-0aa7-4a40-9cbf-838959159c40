# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_uploadVideoAsync.param.MaterialUploadVideoAsyncParam import MaterialUploadVideoAsyncParam


class MaterialUploadVideoAsyncRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialUploadVideoAsyncParam()

	def getUrlPath(self, ):
		return "/material/uploadVideoAsync"

	def getParams(self, ):
		return self.params



