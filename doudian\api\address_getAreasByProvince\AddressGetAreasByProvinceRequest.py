# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.address_getAreasByProvince.param.AddressGetAreasByProvinceParam import AddressGetAreasByProvinceParam


class AddressGetAreasByProvinceRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AddressGetAreasByProvinceParam()

	def getUrlPath(self, ):
		return "/address/getAreasByProvince"

	def getParams(self, ):
		return self.params



