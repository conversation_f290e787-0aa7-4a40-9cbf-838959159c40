# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.address_areaList.param.AddressAreaListParam import AddressAreaListParam


class AddressAreaListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AddressAreaListParam()

	def getUrlPath(self, ):
		return "/address/areaList"

	def getParams(self, ):
		return self.params



