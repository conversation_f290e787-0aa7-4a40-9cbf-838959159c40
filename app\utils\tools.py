import time


def retry(max_retries=3, delay=1):
    """
    重试装饰器

    Args:
        max_retries: 最大重试次数
        delay: 重试延迟时间(秒)
    """

    def decorator(func):
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    retries += 1
                    if retries == max_retries:
                        raise e
                    time.sleep(delay)
            return None

        return wrapper

    return decorator


def safe_get_float(obj: dict, key: str, default=0.0):
    """安全地从字典中获取浮点数"""
    value = obj.get(key)
    if value is None or value == "":
        return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return default
