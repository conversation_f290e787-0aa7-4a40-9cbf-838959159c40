import asyncio
from datetime import datetime, timedelta
import json
import math
from app.core.logger import logger
from sqlalchemy import text

# 从libs中导入API功能
from libs.xiaohongshu import (
    # 认证相关
    get_access_token,
    # API调用
    get_campaign_list,
    get_creativity_search,
    get_creative_offline_report,
    # 类型定义
    GetCampaignListParams,
    GetCreativitySearchParams,
    GetCreativeOfflineReportParams,
)

"""
小红书聚光平台业务服务模块

本模块专注于业务逻辑实现，包括：

业务功能：
1. 创意数据同步到数据库
2. 创意基础信息同步
3. 汇总数据生成和管理
4. 数据同步任务调度

技术特性：
- 异步数据库操作
- 分页数据处理
- 错误处理和重试机制
- 数据一致性保证

API功能请使用：
- from libs.xiaohongshu import get_note_offline_report, GetNoteOfflineReportParams
- 详细使用方法请参考 libs/xiaohongshu/README.md
"""

# ==================== 数据库连接监控工具 ====================


async def check_database_connection():
    """
    检查数据库连接状态

    Returns:
        dict: 连接状态信息
    """
    from app.core.oa_database import get_session, engine

    try:
        async with get_session() as session:
            # 执行简单查询测试连接
            result = await session.execute(text("SELECT 1 as test"))
            test_result = result.scalar()

            # 获取连接池状态（注意不同版本的SQLAlchemy可能有不同的属性）
            pool = engine.pool
            pool_status = {}

            try:
                # 尝试获取连接池状态信息
                pool_status = {
                    "pool_size": getattr(pool, "size", lambda: "N/A")(),
                    "checked_in": getattr(pool, "checkedin", lambda: "N/A")(),
                    "checked_out": getattr(pool, "checkedout", lambda: "N/A")(),
                    "overflow": getattr(pool, "overflow", lambda: "N/A")(),
                }
                # 尝试获取无效连接数，如果属性不存在就跳过
                if hasattr(pool, "invalidated"):
                    pool_status["invalidated"] = pool.invalidated()
                elif hasattr(pool, "invalid"):
                    pool_status["invalid"] = pool.invalid()
                else:
                    pool_status["invalidated"] = "N/A"

            except Exception as pool_error:
                logger.warning(f"获取连接池状态时出错: {pool_error}")
                pool_status = {
                    "pool_size": "Error",
                    "checked_in": "Error",
                    "checked_out": "Error",
                    "overflow": "Error",
                    "invalidated": "Error",
                }

            return {
                "success": True,
                "test_query": test_result == 1,
                "pool_status": pool_status,
                "timestamp": datetime.now().isoformat(),
            }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat(),
        }


async def log_database_stats():
    """记录数据库连接池统计信息"""
    stats = await check_database_connection()
    if stats["success"]:
        pool_info = stats["pool_status"]
        # 获取无效连接数，优先使用 invalidated，然后是 invalid
        invalid_count = pool_info.get("invalidated") or pool_info.get("invalid", "N/A")

        logger.info(
            f"数据库连接池状态 - 大小: {pool_info['pool_size']}, "
            f"已签入: {pool_info['checked_in']}, "
            f"已签出: {pool_info['checked_out']}, "
            f"溢出: {pool_info['overflow']}, "
            f"无效: {invalid_count}"
        )
    else:
        logger.error(f"数据库连接检查失败: {stats['error']}")


# ==================== 辅助工具函数 ====================


async def retry_api_call(
    func, *args, max_retries: int = 3, delay: float = 1.0, **kwargs
):
    """
    带重试机制的API调用包装器

    Args:
        func: 要调用的函数
        *args: 函数参数
        max_retries: 最大重试次数
        delay: 重试间隔时间（秒）
        **kwargs: 函数关键字参数

    Returns:
        函数执行结果
    """
    last_exception = None

    for attempt in range(max_retries + 1):
        try:
            if attempt > 0:
                # 添加延迟，避免频繁请求
                await asyncio.sleep(delay * attempt)
                logger.info(f"重试API调用，第 {attempt} 次重试")

            result = func(*args, **kwargs)
            return result

        except Exception as e:
            last_exception = e
            error_msg = str(e)

            # 判断是否为网络相关错误，值得重试
            if any(
                keyword in error_msg.lower()
                for keyword in [
                    "ssl",
                    "connection",
                    "timeout",
                    "max retries",
                    "eof",
                    "network",
                ]
            ):
                if attempt < max_retries:
                    logger.warning(f"API调用失败，将重试: {error_msg}")
                    continue
                else:
                    logger.error(f"API调用重试 {max_retries} 次后仍然失败: {error_msg}")
            else:
                # 非网络错误，不重试
                logger.error(f"API调用失败（不重试）: {error_msg}")
                break

    # 所有重试都失败了，抛出最后一个异常
    raise last_exception


def safe_get_api_data(
    result: dict, data_key: str, context: str = ""
) -> tuple[bool, any, str]:
    """
    安全地从API响应中获取数据

    Args:
        result: API响应结果
        data_key: 要获取的数据字段名
        context: 上下文信息，用于错误日志

    Returns:
        tuple: (成功标志, 数据, 错误信息)
    """
    if not result.get("success"):
        error_msg = f"API调用失败: {result.get('msg', '未知错误')}"
        return False, None, error_msg

    if "data" not in result:
        error_msg = f"API响应中没有data字段"
        return False, None, error_msg

    data = result["data"]
    if not isinstance(data, dict):
        error_msg = f"API响应data字段不是字典类型: {type(data)}"
        return False, None, error_msg

    if data_key not in data:
        available_keys = list(data.keys()) if isinstance(data, dict) else "N/A"
        error_msg = f"API响应data中没有{data_key}字段，可用字段: {available_keys}"
        return False, None, error_msg

    return True, data[data_key], ""


# ==================== 创意数据同步业务逻辑 ====================


async def sync_xiaohongshu_creativity_data(
    start_date: str = None, end_date: str = None, advertiser_ids: list[int] = None
) -> dict:
    """
    同步小红书创意数据到数据库

    Args:
        start_date: 开始日期，格式 YYYY-MM-DD，默认为昨天
        end_date: 结束日期，格式 YYYY-MM-DD，默认为昨天
        advertiser_ids: 广告主ID列表，默认为所有授权的广告主

    Returns:
        dict: 同步结果统计
    """
    from app.core.oa_database import get_session

    # 设置默认日期为昨天
    if not start_date or not end_date:
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
        start_date = start_date or yesterday
        end_date = end_date or yesterday

    logger.info(f"开始同步小红书创意数据，日期范围: {start_date} 到 {end_date}")

    # 获取访问令牌和授权广告主
    token_info = await get_access_token()
    if not token_info:
        logger.error("获取小红书访问令牌失败")
        return {"success": False, "error": "获取访问令牌失败"}

    access_token = token_info["access_token"]
    approval_advertisers = token_info["approval_advertisers"]

    if not approval_advertisers:
        logger.error("没有授权的广告主")
        return {"success": False, "error": "没有授权的广告主"}

    # 过滤广告主ID
    if advertiser_ids:
        approval_advertisers = [
            adv
            for adv in approval_advertisers
            if adv["advertiser_id"] in advertiser_ids
        ]

    total_processed = 0
    total_inserted = 0
    total_updated = 0
    errors = []

    async with get_session(transaction=True) as session:
        try:
            for advertiser in approval_advertisers:
                advertiser_id = advertiser["advertiser_id"]
                advertiser_name = advertiser["advertiser_name"]

                logger.info(f"处理广告主: {advertiser_name} (ID: {advertiser_id})")

                # 首先获取第一页数据来确定总数
                page_num = 1
                page_size = 500  # 最大页面大小
                total_pages = None  # 总页数，第一页获取后计算

                while True:
                    try:
                        # 构建查询参数
                        params = GetCreativeOfflineReportParams(
                            advertiser_id=advertiser_id,
                            start_date=start_date,
                            end_date=end_date,
                            time_unit="DAY",  # 按天获取数据
                            page_num=page_num,
                            page_size=page_size,
                        )

                        # 使用重试机制调用API
                        result = await retry_api_call(
                            get_creative_offline_report,
                            access_token,
                            params,
                            max_retries=3,
                            delay=1.0,
                        )

                        if not result.get("success"):
                            error_msg = f"API调用失败: {result.get('msg', '未知错误')}"
                            logger.error(f"广告主 {advertiser_name}: {error_msg}")
                            errors.append(f"{advertiser_name}: {error_msg}")
                            break

                        # 安全地访问数据结构
                        if "data" not in result:
                            error_msg = f"API响应中没有data字段"
                            logger.error(f"广告主 {advertiser_name}: {error_msg}")
                            errors.append(f"{advertiser_name}: {error_msg}")
                            break

                        data = result["data"]
                        if not isinstance(data, dict):
                            error_msg = f"API响应data字段不是字典类型: {type(data)}"
                            logger.error(f"广告主 {advertiser_name}: {error_msg}")
                            errors.append(f"{advertiser_name}: {error_msg}")
                            break

                        if "data_list" not in data:
                            error_msg = f"API响应data中没有data_list字段，可用字段: {list(data.keys())}"
                            logger.error(f"广告主 {advertiser_name}: {error_msg}")
                            errors.append(f"{advertiser_name}: {error_msg}")
                            break

                        data_list = data["data_list"]

                        # 第一页时计算总页数
                        if page_num == 1 and total_pages is None:
                            total_count = data.get("total_count", 0)
                            if total_count > 0:
                                total_pages = math.ceil(total_count / page_size)
                                logger.info(
                                    f"广告主 {advertiser_name} 创意数据总数: {total_count}，总页数: {total_pages}"
                                )
                            else:
                                logger.info(f"广告主 {advertiser_name} 无创意数据")
                                break

                        if not data_list:
                            break

                        # 处理每条数据
                        for item in data_list:
                            try:
                                processed, inserted, updated = (
                                    await _process_creativity_item(
                                        session, item, advertiser_id
                                    )
                                )
                                total_processed += processed
                                total_inserted += inserted
                                total_updated += updated

                            except Exception as e:
                                error_msg = f"处理创意数据失败: {str(e)}"
                                logger.error(
                                    f"创意ID {item.get('creativity_id', 'N/A')}: {error_msg}"
                                )
                                errors.append(
                                    f"创意ID {item.get('creativity_id', 'N/A')}: {error_msg}"
                                )

                        # 检查是否还有更多数据
                        if total_pages is not None and page_num >= total_pages:
                            # 根据总页数判断是否结束
                            break
                        elif len(data_list) < page_size:
                            # 兜底逻辑：当前页数据不足页面大小，说明是最后一页
                            break

                        page_num += 1

                    except Exception as e:
                        error_msg = f"获取第 {page_num} 页数据失败: {str(e)}"
                        logger.error(f"广告主 {advertiser_name}: {error_msg}")
                        errors.append(
                            f"{advertiser_name} 第 {page_num} 页: {error_msg}"
                        )
                        break

            # 提交事务
            await session.commit()

            result = {
                "success": True,
                "total_processed": total_processed,
                "total_inserted": total_inserted,
                "total_updated": total_updated,
                "errors": errors,
                "date_range": f"{start_date} 到 {end_date}",
                "advertisers_count": len(approval_advertisers),
            }

            logger.info(
                f"同步完成: 处理 {total_processed} 条，新增 {total_inserted} 条，更新 {total_updated} 条"
            )
            return result

        except Exception as e:
            await session.rollback()
            logger.error(f"同步过程中发生异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "total_processed": total_processed,
                "total_inserted": total_inserted,
                "total_updated": total_updated,
                "errors": errors,
            }


async def _process_creativity_item(
    session, item: dict, advertiser_id: int
) -> tuple[int, int, int]:
    """
    处理单个创意数据项

    Args:
        session: 数据库会话
        item: 创意数据项
        advertiser_id: 广告主ID

    Returns:
        tuple: (processed_count, inserted_count, updated_count)
    """
    from app.core.oa_database import BrandXhsJgCreativityDataReport
    from sqlalchemy import select, and_

    creativity_id = str(item.get("creativity_id", ""))
    note_id = str(item.get("note_id", ""))
    time_str = item.get("time", "")

    if not creativity_id or not note_id or not time_str:
        logger.warning(
            f"跳过无效数据: creativity_id={creativity_id}, note_id={note_id}, time={time_str}"
        )
        return 0, 0, 0

    # 解析时间
    try:
        date_obj = datetime.strptime(time_str, "%Y-%m-%d")
        date_time = int(date_obj.timestamp())
        date_year = date_obj.strftime("%Y")
        date_month = date_obj.strftime("%m")
        date_day = date_obj.strftime("%d")
    except ValueError:
        logger.warning(f"时间格式错误: {time_str}")
        return 0, 0, 0

    # 准备数据
    def safe_float(value, default=0.0):
        """安全转换为浮点数"""
        if value is None or value == "":
            return default
        try:
            return float(value)
        except (ValueError, TypeError):
            return default

    def safe_str(value, default=""):
        """安全转换为字符串"""
        if value is None:
            return default
        return str(value)

    def safe_int(value, default=0):
        """安全转换为整数"""
        if value is None or value == "":
            return default
        try:
            return int(value)
        except (ValueError, TypeError):
            return default

    # 构建数据字典
    data = {
        "api": True,
        "creativity_id": creativity_id,
        "note_id": note_id,
        "date_time": date_time,
        "date_year": date_year,
        "date_month": date_month,
        "date_day": date_day,
        "fee": safe_float(item.get("fee")),
        "impression": safe_int(item.get("impression")),
        "click": safe_int(item.get("click")),
        "ctr": safe_str(item.get("ctr")),
        "acp": safe_float(item.get("acp")),
        "cpm": safe_float(item.get("cpm")),
        "like": safe_int(item.get("like")),
        "comment": safe_int(item.get("comment")),
        "collect": safe_int(item.get("collect")),
        "follow": safe_int(item.get("follow")),
        "share": safe_int(item.get("share")),
        "interaction": safe_int(item.get("interaction")),
        "cpi": safe_float(item.get("cpi")),
        "action_button_click": safe_int(item.get("action_button_click")),
        "action_button_ctr": safe_str(item.get("action_button_ctr")),
        "screenshot": safe_int(item.get("screenshot")),
        "pic_save": safe_int(item.get("pic_save")),
        "reserve_pv": safe_int(item.get("reserve_pv")),
        "search_cmt_click": safe_int(item.get("search_cmt_click")),
        "search_cmt_click_cvr": safe_str(item.get("search_cmt_click_cvr")),
        "search_cmt_after_read_avg": safe_float(item.get("search_cmt_after_read_avg")),
        "search_cmt_after_read": safe_int(item.get("search_cmt_after_read")),
        "delete_time": 0,  # 设置默认删除时间为0，表示未删除
    }

    # 数据库操作重试机制
    max_retries = 2
    for attempt in range(max_retries):
        try:
            # 检查是否已存在记录（基于创意ID、笔记ID和日期的唯一性）
            stmt = select(BrandXhsJgCreativityDataReport).where(
                and_(
                    BrandXhsJgCreativityDataReport.creativity_id == creativity_id,
                    BrandXhsJgCreativityDataReport.note_id == note_id,
                    BrandXhsJgCreativityDataReport.date_time == date_time,
                )
            )

            existing_record = await session.execute(stmt)
            existing_record = existing_record.scalar_one_or_none()

            if existing_record:
                # 更新现有记录
                for key, value in data.items():
                    if key not in [
                        "create_time",
                        "delete_time",
                    ]:  # 不更新创建时间和删除时间
                        setattr(existing_record, key, value)

                return 1, 0, 1
            else:
                # 插入新记录
                new_record = BrandXhsJgCreativityDataReport(**data)
                session.add(new_record)

                return 1, 1, 0

        except Exception as e:
            error_msg = str(e)
            is_connection_error = any(
                keyword in error_msg.lower()
                for keyword in [
                    "packet sequence number wrong",
                    "connection",
                    "lost connection",
                    "mysql server has gone away",
                ]
            )

            if is_connection_error and attempt < max_retries - 1:
                # 连接错误且还有重试机会
                await asyncio.sleep(0.5)  # 短暂延迟
                continue
            else:
                # 非连接错误或重试次数用完，重新抛出异常
                raise


async def sync_xiaohongshu_creativity_data_for_date_range(
    days_back: int = 7, advertiser_ids: list[int] = None
) -> dict:
    """
    同步指定天数范围内的小红书创意数据

    Args:
        days_back: 往前同步的天数，默认7天
        advertiser_ids: 广告主ID列表，默认为所有授权的广告主

    Returns:
        dict: 同步结果统计
    """
    end_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")  # 昨天
    start_date = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")

    return await sync_xiaohongshu_creativity_data(
        start_date=start_date, end_date=end_date, advertiser_ids=advertiser_ids
    )


# ==================== 创意基础信息同步业务逻辑 ====================


async def sync_xiaohongshu_creativity_info(
    advertiser_ids: list[int] = None,
    max_concurrent_advertisers: int = 3,  # 大幅降低并发数以减少数据库压力
    max_concurrent_campaigns: int = 5,  # 大幅降低并发数以减少数据库压力
) -> dict:
    """
    同步小红书创意基础信息到数据库

    Args:
        advertiser_ids: 广告主ID列表，默认为所有授权的广告主
        max_concurrent_advertisers: 最大并发广告主数量
        max_concurrent_campaigns: 最大并发计划数量

    Returns:
        dict: 同步结果统计
    """

    logger.info("开始同步小红书创意基础信息")

    # 记录开始时的数据库连接状态
    await log_database_stats()

    # 获取访问令牌和授权广告主
    token_info = await get_access_token()
    if not token_info:
        logger.error("获取小红书访问令牌失败")
        return {"success": False, "error": "获取访问令牌失败"}

    access_token = token_info["access_token"]
    approval_advertisers = token_info["approval_advertisers"]

    if not approval_advertisers:
        logger.error("没有授权的广告主")
        return {"success": False, "error": "没有授权的广告主"}

    # 过滤广告主ID
    if advertiser_ids:
        approval_advertisers = [
            adv
            for adv in approval_advertisers
            if adv["advertiser_id"] in advertiser_ids
        ]

    # 创建信号量控制并发数量
    advertiser_semaphore = asyncio.Semaphore(max_concurrent_advertisers)
    campaign_semaphore = asyncio.Semaphore(max_concurrent_campaigns)

    try:
        # 并发处理所有广告主，每个广告主使用独立的数据库会话
        tasks = []
        for advertiser in approval_advertisers:
            task = _process_advertiser_concurrent(
                access_token, advertiser, advertiser_semaphore, campaign_semaphore
            )
            tasks.append(task)

        # 等待所有广告主处理完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 汇总结果
        total_processed = 0
        total_inserted = 0
        total_updated = 0
        errors = []

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                advertiser_name = approval_advertisers[i]["advertiser_name"]
                error_msg = f"处理广告主 {advertiser_name} 发生异常: {str(result)}"
                logger.error(error_msg)
                errors.append(error_msg)
            elif isinstance(result, dict):
                total_processed += result.get("processed", 0)
                total_inserted += result.get("inserted", 0)
                total_updated += result.get("updated", 0)
                errors.extend(result.get("errors", []))

        result = {
            "success": True,
            "total_processed": total_processed,
            "total_inserted": total_inserted,
            "total_updated": total_updated,
            "errors": errors,
            "advertisers_count": len(approval_advertisers),
        }

        logger.info(
            f"创意信息同步完成: 处理 {total_processed} 条，"
            f"新增 {total_inserted} 条，更新 {total_updated} 条"
        )

        # 记录结束时的数据库连接状态
        await log_database_stats()

        return result

    except Exception as e:
        logger.error(f"同步过程中发生异常: {str(e)}")
        # 记录异常时的数据库连接状态
        await log_database_stats()
        return {
            "success": False,
            "error": str(e),
            "total_processed": 0,
            "total_inserted": 0,
            "total_updated": 0,
            "errors": [],
        }


async def _process_advertiser_concurrent(
    access_token: str,
    advertiser: dict,
    advertiser_semaphore: asyncio.Semaphore,
    campaign_semaphore: asyncio.Semaphore,
):
    """
    并发处理单个广告主的创意信息同步

    Args:
        access_token: 访问令牌
        advertiser: 广告主信息
        advertiser_semaphore: 广告主并发控制信号量
        campaign_semaphore: 计划并发控制信号量
    """
    async with advertiser_semaphore:
        advertiser_id = advertiser["advertiser_id"]
        advertiser_name = advertiser["advertiser_name"]

        try:
            # 获取广告计划列表
            campaigns = await _get_campaigns_for_advertiser_concurrent(
                access_token, advertiser_id
            )

            if not campaigns:
                return {
                    "processed": 0,
                    "inserted": 0,
                    "updated": 0,
                    "errors": [],
                }

            # 并发处理所有计划
            campaign_tasks = []
            for campaign in campaigns:
                task = _process_campaign_concurrent(
                    access_token, advertiser_id, campaign, campaign_semaphore
                )
                campaign_tasks.append(task)

            # 等待所有计划处理完成
            campaign_results = await asyncio.gather(
                *campaign_tasks, return_exceptions=True
            )

            # 汇总结果
            processed = 0
            inserted = 0
            updated = 0
            errors = []

            for result in campaign_results:
                if isinstance(result, Exception):
                    error_msg = f"处理计划 {campaign.get('campaign_name', 'N/A')} 失败: {str(result)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                elif isinstance(result, dict):
                    processed += result.get("processed", 0)
                    inserted += result.get("inserted", 0)
                    updated += result.get("updated", 0)
                    errors.extend(result.get("errors", []))

            return {
                "processed": processed,
                "inserted": inserted,
                "updated": updated,
                "errors": errors,
            }

        except Exception as e:
            error_msg = f"处理广告主 {advertiser_name} 失败: {str(e)}"
            logger.error(error_msg)
            return {
                "processed": 0,
                "inserted": 0,
                "updated": 0,
                "errors": [error_msg],
            }


async def _process_campaign_concurrent(
    access_token: str,
    advertiser_id: int,
    campaign: dict,
    campaign_semaphore: asyncio.Semaphore,
):
    """
    并发处理单个计划的创意信息

    Args:
        access_token: 访问令牌
        advertiser_id: 广告主ID
        campaign: 计划信息
        campaign_semaphore: 计划并发控制信号量
    """
    async with campaign_semaphore:
        campaign_id = campaign["campaign_id"]
        campaign_name = campaign["campaign_name"]

        try:
            # 获取该计划下的创意列表
            creativities = await _get_creativities_for_campaign_concurrent(
                access_token, advertiser_id, campaign_id
            )

            if not creativities:
                return {
                    "processed": 0,
                    "inserted": 0,
                    "updated": 0,
                    "errors": [],
                }

            # 批量处理创意信息
            processed, inserted, updated = await _process_creativities_batch(
                access_token, creativities, campaign, advertiser_id
            )

            return {
                "processed": processed,
                "inserted": inserted,
                "updated": updated,
                "errors": [],
            }

        except Exception as e:
            error_msg = f"处理计划 {campaign_name} 失败: {str(e)}"
            logger.error(error_msg)
            return {
                "processed": 0,
                "inserted": 0,
                "updated": 0,
                "errors": [error_msg],
            }


async def _process_creativities_batch(
    access_token: str, creativities: list, campaign: dict, advertiser_id: int
) -> tuple[int, int, int]:
    """
    批量处理创意信息，使用独立的数据库会话

    Args:
        access_token: 访问令牌
        creativities: 创意列表
        campaign: 计划信息
        advertiser_id: 广告主ID

    Returns:
        tuple: (processed_count, inserted_count, updated_count)
    """
    from app.core.oa_database import get_session

    processed = 0
    inserted = 0
    updated = 0

    # 数据库操作重试机制
    max_db_retries = 3
    for db_attempt in range(max_db_retries):
        try:
            # 使用独立的数据库会话
            async with get_session(transaction=True) as session:
                try:
                    for creativity in creativities:
                        try:
                            processed_count, inserted_count, updated_count = (
                                await _process_creativity_info_item(
                                    session, creativity, campaign, advertiser_id
                                )
                            )
                            processed += processed_count
                            inserted += inserted_count
                            updated += updated_count

                        except Exception as e:
                            error_msg = f"处理创意信息失败: {str(e)}"
                            creativity_id = creativity.get("creativity_id", "N/A")
                            logger.error(f"创意ID {creativity_id}: {error_msg}")

                    # 提交事务
                    await session.commit()
                    # 成功完成，退出重试循环
                    break

                except Exception as e:
                    await session.rollback()
                    raise e

        except Exception as e:
            error_msg = str(e)
            is_connection_error = any(
                keyword in error_msg.lower()
                for keyword in [
                    "packet sequence number wrong",
                    "connection",
                    "timeout",
                    "lost connection",
                    "mysql server has gone away",
                    "can't connect",
                    "broken pipe",
                ]
            )

            if is_connection_error and db_attempt < max_db_retries - 1:
                # 连接错误且还有重试机会
                retry_delay = (db_attempt + 1) * 2  # 递增延迟
                logger.warning(
                    f"数据库连接错误，第 {db_attempt + 1} 次重试 (延迟 {retry_delay}s): {error_msg}"
                )
                await asyncio.sleep(retry_delay)
                continue
            else:
                # 非连接错误或重试次数用完
                logger.error(f"批量处理创意信息失败: {error_msg}")
                raise

    return processed, inserted, updated


async def _process_creativity_info_item(
    session, creativity: dict, campaign: dict, advertiser_id: int
) -> tuple[int, int, int]:
    """
    处理单个创意信息项

    Args:
        session: 数据库会话
        creativity: 创意数据
        campaign: 计划数据
        advertiser_id: 广告主ID

    Returns:
        tuple: (processed_count, inserted_count, updated_count)
    """
    from app.core.oa_database import BrandXhsJgCreativity
    from sqlalchemy import select

    creativity_id = str(creativity.get("creativity_id", ""))

    if not creativity_id:
        logger.warning("跳过无效创意数据: creativity_id为空")
        return 0, 0, 0

    # 准备数据
    def safe_str(value, default=""):
        """安全转换为字符串"""
        if value is None:
            return default
        return str(value)

    def safe_int(value, default=0):
        """安全转换为整数"""
        if value is None:
            return default
        try:
            return int(value)
        except (ValueError, TypeError):
            return default

    # 构建数据字典
    current_time = int(datetime.now().timestamp())

    # 构建抓取数据包（用于后续数据抓取的参数）
    catch_data = {
        "advertiser_id": advertiser_id,
        "campaign_id": creativity.get("campaign_id"),
        "unit_id": creativity.get("unit_id"),
        "creativity_id": creativity_id,
        "note_id": creativity.get("note_id"),
    }

    data = {
        "catch_time": current_time,
        "last_catch_error_time": 0,
        "catch_data": json.dumps(catch_data, ensure_ascii=False),
        "campaign_id": safe_str(creativity.get("campaign_id")),
        "campaign_name": safe_str(campaign.get("campaign_name")),
        "marketing_target": safe_int(campaign.get("marketing_target")),
        "placement": safe_int(campaign.get("placement")),
        "unit_id": safe_str(creativity.get("unit_id")),
        "unit_name": safe_str(
            creativity.get("unit_name", "")
        ),  # API可能不返回unit_name
        "creativity_id": creativity_id,
        "creativity_name": safe_str(creativity.get("creativity_name")),
        "note_id": safe_str(creativity.get("note_id")),
        "note_title": safe_str(
            creativity.get("note_title", "")
        ),  # 可能需要从其他接口获取
        "brand_resource_id": 0,  # 默认值，可能需要后续关联
        "delete_time": 0,  # 设置默认删除时间为0，表示未删除
    }

    # 检查是否已存在记录（基于创意ID的唯一性）
    stmt = select(BrandXhsJgCreativity).where(
        BrandXhsJgCreativity.creativity_id == creativity_id
    )

    existing_record = await session.execute(stmt)
    existing_record = existing_record.scalar_one_or_none()

    if existing_record:
        # 更新现有记录
        for key, value in data.items():
            if key not in ["create_time", "delete_time"]:  # 不更新创建时间和删除时间
                setattr(existing_record, key, value)

        return 1, 0, 1
    else:
        # 插入新记录
        new_record = BrandXhsJgCreativity(**data)
        session.add(new_record)

        return 1, 1, 0


async def _get_campaigns_for_advertiser_concurrent(
    access_token: str, advertiser_id: int
) -> list:
    """
    并发获取指定广告主的所有广告计划

    Args:
        access_token: 访问令牌
        advertiser_id: 广告主ID

    Returns:
        list: 广告计划列表
    """
    campaigns = []
    page_size = 20

    # 首先获取第一页来确定总页数
    first_page_campaigns, page_info = await _fetch_campaigns_page(
        access_token, advertiser_id, 1, page_size
    )
    if not first_page_campaigns:
        return campaigns

    campaigns.extend(first_page_campaigns)

    # 从分页信息获取总数
    total_count = page_info.get("total_count", 0)
    if total_count <= page_size:
        # 总数不超过一页大小，直接返回
        return campaigns

    # 计算总页数
    total_pages = math.ceil(total_count / page_size)
    logger.info(
        f"广告主 {advertiser_id} 计划总数: {total_count}，总页数: {total_pages}"
    )

    # 如果只有一页，直接返回
    if total_pages <= 1:
        return campaigns

    # 并发获取剩余页面
    page_tasks = []

    for page_index in range(2, total_pages + 1):
        task = _fetch_campaigns_page(access_token, advertiser_id, page_index, page_size)
        page_tasks.append(task)

        # 每添加一个任务后延迟，避免同时发起太多请求
        if page_index % 2 == 0:  # 每2个请求延迟一次，减少频率
            await asyncio.sleep(1.5)  # 增加延迟时间

    # 获取所有页面结果
    page_results = await asyncio.gather(*page_tasks, return_exceptions=True)

    for result in page_results:
        if isinstance(result, tuple) and len(result) == 2:
            page_campaigns, _ = result
            if page_campaigns:
                campaigns.extend(page_campaigns)
        elif isinstance(result, Exception):
            logger.error(f"获取计划页面时发生异常: {str(result)}")

    logger.info(f"广告主 {advertiser_id} 实际获取计划数: {len(campaigns)}")
    return campaigns


async def _fetch_campaigns_page(
    access_token: str, advertiser_id: int, page_index: int, page_size: int
) -> tuple[list, dict]:
    """
    获取指定页的广告计划数据（带重试机制）

    Args:
        access_token: 访问令牌
        advertiser_id: 广告主ID
        page_index: 页码
        page_size: 页面大小

    Returns:
        tuple: (该页的广告计划列表, 分页信息)
    """
    try:
        params = GetCampaignListParams(
            advertiser_id=advertiser_id,
            status=6,  # 所有未删除状态
            page={"page_index": page_index, "page_size": page_size},
        )

        # 使用重试机制调用API
        result = await retry_api_call(
            get_campaign_list, access_token, params, max_retries=3, delay=1.0
        )

        if not result.get("success"):
            error_msg = f"API调用失败: {result.get('msg', '未知错误')}"
            logger.error(f"获取计划列表第 {page_index} 页失败: {error_msg}")
            return [], {}

        data = result.get("data", {})
        campaign_list = data.get("base_campaign_dtos", [])
        page_info = data.get("page", {})

        return campaign_list or [], page_info

    except Exception as e:
        logger.error(f"获取计划列表第 {page_index} 页异常: {str(e)}")
        return [], {}


async def _get_creativities_for_campaign_concurrent(
    access_token: str, advertiser_id: int, campaign_id: int
) -> list:
    """
    并发获取指定计划下的所有创意

    Args:
        access_token: 访问令牌
        advertiser_id: 广告主ID
        campaign_id: 计划ID

    Returns:
        list: 创意列表
    """
    creativities = []
    page_size = 20

    # 首先获取第一页来确定总页数
    first_page_creativities, page_info = await _fetch_creativities_page(
        access_token, advertiser_id, campaign_id, 1, page_size
    )
    if not first_page_creativities:
        return creativities

    creativities.extend(first_page_creativities)

    # 从分页信息获取总数
    total_count = page_info.get("total_count", 0)
    if total_count <= page_size:
        # 总数不超过一页大小，直接返回
        return creativities

    # 计算总页数
    total_pages = math.ceil(total_count / page_size)
    logger.info(f"计划 {campaign_id} 创意总数: {total_count}，总页数: {total_pages}")

    # 如果只有一页，直接返回
    if total_pages <= 1:
        return creativities

    # 并发获取剩余页面
    page_tasks = []

    for page_index in range(2, total_pages + 1):
        task = _fetch_creativities_page(
            access_token, advertiser_id, campaign_id, page_index, page_size
        )
        page_tasks.append(task)

        # 每添加一个任务后延迟，避免同时发起太多请求
        if page_index % 2 == 0:  # 每2个请求延迟一次，减少频率
            await asyncio.sleep(1.5)  # 增加延迟时间

    # 获取所有页面结果
    page_results = await asyncio.gather(*page_tasks, return_exceptions=True)

    for result in page_results:
        if isinstance(result, tuple) and len(result) == 2:
            page_creativities, _ = result
            if page_creativities:
                creativities.extend(page_creativities)
        elif isinstance(result, Exception):
            logger.error(f"获取创意页面时发生异常: {str(result)}")

    logger.info(f"计划 {campaign_id} 实际获取创意数: {len(creativities)}")
    return creativities


async def _fetch_creativities_page(
    access_token: str,
    advertiser_id: int,
    campaign_id: int,
    page_index: int,
    page_size: int,
) -> tuple[list, dict]:
    """
    获取指定页的创意数据（带重试机制）

    Args:
        access_token: 访问令牌
        advertiser_id: 广告主ID
        campaign_id: 计划ID
        page_index: 页码
        page_size: 页面大小

    Returns:
        tuple: (该页的创意列表, 分页信息)
    """
    try:
        params = GetCreativitySearchParams(
            advertiser_id=advertiser_id,
            campaign_id=campaign_id,
            status=2,  # 所有未删除状态
            page={"page_index": page_index, "page_size": page_size},
        )

        # 使用重试机制调用API
        result = await retry_api_call(
            get_creativity_search, access_token, params, max_retries=3, delay=1.0
        )

        if not result.get("success"):
            error_msg = f"API调用失败: {result.get('msg', '未知错误')}"
            logger.error(f"获取创意列表第 {page_index} 页失败: {error_msg}")
            return [], {}

        data = result.get("data", {})
        creativity_list = data.get("creativity_dtos", [])
        page_info = data.get("page", {})

        return creativity_list or [], page_info

    except Exception as e:
        logger.error(f"获取创意列表第 {page_index} 页异常: {str(e)}")
        return [], {}


# ==================== 小红书汇总数据生成业务逻辑 ====================


async def generate_xiaohongshu_overall_data_report(
    start_date: str = None, end_date: str = None, force_regenerate: bool = False
) -> dict:
    """
    从创意报表数据生成汇总报表数据

    Args:
        start_date: 开始日期，格式 YYYY-MM-DD，默认为昨天
        end_date: 结束日期，格式 YYYY-MM-DD，默认为昨天
        force_regenerate: 是否强制重新生成已存在的数据

    Returns:
        dict: 生成结果统计
    """
    from app.core.oa_database import (
        get_session,
        BrandXhsJgCreativityDataReport,
        BrandXhsJgOverallDataReport,
    )
    from sqlalchemy import select, func, and_

    # 设置默认日期为昨天
    if not start_date or not end_date:
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
        start_date = start_date or yesterday
        end_date = end_date or yesterday

    logger.info(f"开始生成小红书汇总数据，日期范围: {start_date} 到 {end_date}")

    total_processed = 0
    total_inserted = 0
    total_updated = 0
    errors = []

    async with get_session(transaction=True) as session:
        try:
            # 解析日期范围
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")

            # 按日期循环处理
            current_date = start_date_obj
            while current_date <= end_date_obj:
                date_str = current_date.strftime("%Y-%m-%d")
                date_timestamp = int(current_date.timestamp())

                try:
                    processed, inserted, updated = await _process_overall_data_for_date(
                        session, date_timestamp, date_str, force_regenerate
                    )
                    total_processed += processed
                    total_inserted += inserted
                    total_updated += updated

                except Exception as e:
                    error_msg = f"处理日期 {date_str} 失败: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)

                current_date += timedelta(days=1)

            # 提交事务
            await session.commit()

            result = {
                "success": True,
                "total_processed": total_processed,
                "total_inserted": total_inserted,
                "total_updated": total_updated,
                "errors": errors,
                "date_range": f"{start_date} 到 {end_date}",
            }

            logger.info(
                f"汇总数据生成完成: 处理 {total_processed} 条，新增 {total_inserted} 条，更新 {total_updated} 条"
            )
            return result

        except Exception as e:
            await session.rollback()
            logger.error(f"生成汇总数据过程中发生异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "total_processed": total_processed,
                "total_inserted": total_inserted,
                "total_updated": total_updated,
                "errors": errors,
            }


async def _process_overall_data_for_date(
    session, date_timestamp: int, date_str: str, force_regenerate: bool = False
) -> tuple[int, int, int]:
    """
    处理单个日期的汇总数据

    Args:
        session: 数据库会话
        date_timestamp: 日期时间戳
        date_str: 日期字符串 YYYY-MM-DD
        force_regenerate: 是否强制重新生成

    Returns:
        tuple: (processed_count, inserted_count, updated_count)
    """
    from app.core.oa_database import (
        BrandXhsJgCreativityDataReport,
        BrandXhsJgOverallDataReport,
    )
    from sqlalchemy import select, func

    # 检查是否已存在汇总数据
    existing_stmt = select(BrandXhsJgOverallDataReport).where(
        BrandXhsJgOverallDataReport.date_time == date_timestamp
    )
    existing_record = await session.execute(existing_stmt)
    existing_record = existing_record.scalar_one_or_none()

    if existing_record and not force_regenerate:
        return 0, 0, 0

    # 查询该日期的创意数据进行汇总
    creativity_stmt = select(
        func.sum(BrandXhsJgCreativityDataReport.fee).label("total_fee"),
        func.sum(BrandXhsJgCreativityDataReport.impression).label("total_impression"),
        func.sum(BrandXhsJgCreativityDataReport.click).label("total_click"),
        func.count(BrandXhsJgCreativityDataReport.id).label("record_count"),
    ).where(BrandXhsJgCreativityDataReport.date_time == date_timestamp)

    result = await session.execute(creativity_stmt)
    aggregation = result.first()

    if not aggregation or aggregation.record_count == 0:
        return 0, 0, 0

    # 计算汇总指标
    total_fee = float(aggregation.total_fee or 0)
    total_impression = int(aggregation.total_impression or 0)
    total_click = int(aggregation.total_click or 0)

    # 计算点击率
    ctr = (total_click / total_impression * 100) if total_impression > 0 else 0

    # 计算平均点击价格
    acp = (total_fee / total_click) if total_click > 0 else 0

    # 计算环比（与前一天对比）
    previous_date_timestamp = date_timestamp - 86400  # 前一天
    previous_stmt = select(BrandXhsJgOverallDataReport).where(
        BrandXhsJgOverallDataReport.date_time == previous_date_timestamp
    )
    previous_result = await session.execute(previous_stmt)
    previous_record = previous_result.scalar_one_or_none()

    # 计算环比
    def calculate_chain_ratio(current_value, previous_value):
        """计算环比"""
        if previous_value == 0:
            return "100.00%" if current_value > 0 else "0.00%"
        ratio = ((current_value - previous_value) / previous_value) * 100
        return f"{ratio:+.2f}%"

    fee_chain_ratio = "0.00%"
    impression_chain_ratio = "0.00%"
    click_chain_ratio = "0.00%"
    ctr_chain_ratio = "0.00%"
    acp_chain_ratio = "0.00%"

    if previous_record:
        try:
            prev_fee = float(previous_record.fee if previous_record.fee else "0")
            prev_impression = int(
                previous_record.impression if previous_record.impression else "0"
            )
            prev_click = int(previous_record.click if previous_record.click else "0")
            prev_ctr = float(
                previous_record.ctr.replace("%", "") if previous_record.ctr else "0"
            )
            prev_acp = float(previous_record.acp if previous_record.acp else "0")

            fee_chain_ratio = calculate_chain_ratio(total_fee, prev_fee)
            impression_chain_ratio = calculate_chain_ratio(
                total_impression, prev_impression
            )
            click_chain_ratio = calculate_chain_ratio(total_click, prev_click)
            ctr_chain_ratio = calculate_chain_ratio(ctr, prev_ctr)
            acp_chain_ratio = calculate_chain_ratio(acp, prev_acp)
        except (ValueError, AttributeError) as e:
            logger.warning(f"计算环比时出错: {e}")

    # 解析日期
    date_obj = datetime.fromtimestamp(date_timestamp)
    date_year = date_obj.strftime("%Y")
    date_month = date_obj.strftime("%m")
    date_day = date_obj.strftime("%d")

    # 构建汇总数据
    overall_data = {
        "api": True,
        "origin_data": json.dumps(
            {
                "source": "aggregated_from_creativity_data",
                "date": date_str,
                "total_records": aggregation.record_count,
                "aggregation_time": datetime.now().isoformat(),
            },
            ensure_ascii=False,
        ),
        "date_time": date_timestamp,
        "date_year": date_year,
        "date_month": date_month,
        "date_day": date_day,
        "fee": str(total_fee),
        "fee_chain_ratio": fee_chain_ratio,
        "impression": str(total_impression),
        "impression_chain_ratio": impression_chain_ratio,
        "click": str(total_click),
        "click_chain_ratio": click_chain_ratio,
        "ctr": f"{ctr:.2f}%",
        "ctr_chain_ratio": ctr_chain_ratio,
        "acp": f"{acp:.2f}",
        "acp_chain_ratio": acp_chain_ratio,
        "delete_time": 0,
    }

    if existing_record:
        # 更新现有记录
        for key, value in overall_data.items():
            if key not in ["create_time", "delete_time"]:
                setattr(existing_record, key, value)

        return 1, 0, 1
    else:
        # 插入新记录
        new_record = BrandXhsJgOverallDataReport(**overall_data)
        session.add(new_record)

        return 1, 1, 0


async def generate_xiaohongshu_overall_data_for_date_range(
    days_back: int = 7, force_regenerate: bool = False
) -> dict:
    """
    生成指定天数范围内的小红书汇总数据

    Args:
        days_back: 往前生成的天数，默认7天
        force_regenerate: 是否强制重新生成已存在的数据

    Returns:
        dict: 生成结果统计
    """
    end_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")  # 昨天
    start_date = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")

    return await generate_xiaohongshu_overall_data_report(
        start_date=start_date, end_date=end_date, force_regenerate=force_regenerate
    )


async def sync_and_generate_xiaohongshu_data(
    start_date: str = None,
    end_date: str = None,
    advertiser_ids: list[int] = None,
    generate_overall: bool = True,
) -> dict:
    """
    同步创意数据并生成汇总数据的一站式函数

    Args:
        start_date: 开始日期，格式 YYYY-MM-DD，默认为昨天
        end_date: 结束日期，格式 YYYY-MM-DD，默认为昨天
        advertiser_ids: 广告主ID列表，默认为所有授权的广告主
        generate_overall: 是否生成汇总数据

    Returns:
        dict: 同步和生成结果统计
    """
    logger.info("开始执行小红书数据同步和汇总生成")

    # 1. 同步创意数据
    sync_result = await sync_xiaohongshu_creativity_data(
        start_date=start_date, end_date=end_date, advertiser_ids=advertiser_ids
    )

    if not sync_result.get("success"):
        logger.error("创意数据同步失败，停止后续操作")
        return {
            "success": False,
            "sync_result": sync_result,
            "overall_result": None,
            "error": "创意数据同步失败",
        }

    overall_result = None
    if generate_overall:
        # 2. 生成汇总数据
        overall_result = await generate_xiaohongshu_overall_data_report(
            start_date=start_date, end_date=end_date, force_regenerate=True
        )

        if not overall_result.get("success"):
            logger.warning("汇总数据生成失败，但创意数据同步成功")

    result = {
        "success": True,
        "sync_result": sync_result,
        "overall_result": overall_result,
        "summary": {
            "creativity_processed": sync_result.get("total_processed", 0),
            "creativity_inserted": sync_result.get("total_inserted", 0),
            "creativity_updated": sync_result.get("total_updated", 0),
            "overall_processed": (
                overall_result.get("total_processed", 0) if overall_result else 0
            ),
            "overall_inserted": (
                overall_result.get("total_inserted", 0) if overall_result else 0
            ),
            "overall_updated": (
                overall_result.get("total_updated", 0) if overall_result else 0
            ),
        },
    }

    logger.info(f"小红书数据同步和汇总生成完成: {result['summary']}")
    return result


# ==================== 测试和示例函数 ====================


async def main():
    """
    主函数，用于测试功能
    """
    # await sync_xiaohongshu_creativity_info()
    start_date = "2025-06-01"
    end_date = "2025-06-30"

    await sync_xiaohongshu_creativity_data(
        start_date=start_date, end_date=end_date, advertiser_ids=None
    )
    await generate_xiaohongshu_overall_data_report(
        start_date, end_date, force_regenerate=True
    )


if __name__ == "__main__":
    asyncio.run(main())
