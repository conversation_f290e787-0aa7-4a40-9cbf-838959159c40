# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_offlineChannelProduct.param.ProductOfflineChannelProductParam import ProductOfflineChannelProductParam


class ProductOfflineChannelProductRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductOfflineChannelProductParam()

	def getUrlPath(self, ):
		return "/product/offlineChannelProduct"

	def getParams(self, ):
		return self.params



