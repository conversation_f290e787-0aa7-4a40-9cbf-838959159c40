# 小红书笔记流量推广费更新功能使用文档

## 概述

本功能通过调用小红书聚光平台的笔记离线报表 API，获取笔记的消费金额数据，并自动更新到 `BrandXhsNote` 表中对应笔记的 `cost_flow`（流量推广费）字段。

## 功能特性

- ✅ **自动获取 API 数据**：自动调用小红书笔记离线报表 API 获取消费金额
- ✅ **支持时间范围查询**：支持指定开始和结束日期进行查询
- ✅ **多广告主支持**：自动处理所有授权广告主的数据
- ✅ **分页数据获取**：支持大量数据的分页获取，最大页面大小 500 条
- ✅ **数据汇总**：按笔记 ID 汇总所有时间段的消费金额
- ✅ **批量更新**：使用数据库事务批量更新笔记数据
- ✅ **错误处理**：完善的错误处理和日志记录
- ✅ **精确计算**：使用 Decimal 类型确保金额计算精度

## 主要函数

### `update_note_cost_flow_from_report()`

主要的更新函数，实现完整的数据获取和更新流程。

#### 参数说明

| 参数         | 类型 | 默认值         | 说明                                       |
| ------------ | ---- | -------------- | ------------------------------------------ |
| `start_date` | str  | `"2020-01-01"` | 开始日期，格式：`YYYY-MM-DD`               |
| `end_date`   | str  | `None`         | 结束日期，格式：`YYYY-MM-DD`，默认为昨天   |
| `time_unit`  | str  | `"SUMMARY"`    | 时间维度：`DAY`（分天）、`SUMMARY`（汇总） |

#### 返回值

```python
{
    "success_count": int,     # 成功更新的笔记数量
    "error_count": int,       # 处理失败的笔记数量
    "total_notes": int,       # 从API获取到的记录总数
    "unique_notes": int       # 涉及的唯一笔记数量
}
```

## 使用方法

### 1. 基础使用

```python
import asyncio
from app.services.update_note_cost_flow import update_note_cost_flow_from_report

async def basic_update():
    """基础更新示例"""
    # 更新从2020-01-01到昨天的所有数据
    result = await update_note_cost_flow_from_report()
    print(f"更新结果：{result}")

# 运行
asyncio.run(basic_update())
```

### 2. 指定时间范围

```python
async def range_update():
    """指定时间范围更新"""
    result = await update_note_cost_flow_from_report(
        start_date="2024-01-01",
        end_date="2024-01-31",
        time_unit="SUMMARY"
    )
    print(f"更新2024年1月数据：{result}")
```

### 3. 每日更新任务

```python
from datetime import datetime, timedelta

async def daily_update():
    """每日更新任务"""
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

    result = await update_note_cost_flow_from_report(
        start_date=yesterday,
        end_date=yesterday,
        time_unit="DAY"
    )
    print(f"更新昨天数据：{result}")
```

### 4. 近期数据更新

```python
async def recent_update():
    """更新最近30天数据"""
    end_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")

    result = await update_note_cost_flow_from_report(
        start_date=start_date,
        end_date=end_date,
        time_unit="SUMMARY"
    )
    print(f"更新最近30天数据：{result}")
```

## 运行示例

### 方式 1：直接运行服务脚本

```bash
# 进入项目目录
cd /path/to/your/project

# 运行更新服务
python app/services/update_note_cost_flow.py
```

### 方式 2：运行示例脚本

```bash
# 运行示例脚本（提供交互式选择）
python examples/update_note_cost_flow_example.py
```

### 方式 3：集成到现有代码

```python
from app.services.update_note_cost_flow import update_note_cost_flow_from_report

# 在你的业务代码中调用
async def your_business_function():
    # 其他业务逻辑...

    # 更新笔记流量推广费
    result = await update_note_cost_flow_from_report(
        start_date="2020-01-01",
        end_date=None  # 默认到昨天
    )

    if result["success_count"] > 0:
        print(f"成功更新了 {result['success_count']} 条笔记")

    # 继续其他业务逻辑...
```

## 数据处理流程

### 1. 数据获取

1. **获取访问令牌**：自动从小红书 API 获取访问令牌
2. **遍历广告主**：处理所有授权的广告主账户
3. **分页查询**：使用分页方式获取大量数据，避免内存溢出
4. **数据解析**：解析 API 返回的 JSON 数据，提取笔记 ID 和消费金额

### 2. 数据汇总

1. **按笔记 ID 分组**：将同一笔记在不同时间的消费金额进行汇总
2. **金额累加**：计算每个笔记的总消费金额
3. **数据验证**：验证数据格式和数值有效性

### 3. 数据库更新

1. **批量更新**：使用 SQLAlchemy 的批量更新功能
2. **事务保护**：整个更新过程在数据库事务中执行
3. **错误处理**：遇到错误时自动回滚，保证数据一致性

## 配置要求

### 1. 数据库配置

确保 `config/default.ini` 中的数据库配置正确：

```ini
[database]
OA_URL = mysql+aiomysql://username:password@host:port/database
```

### 2. 小红书 API 配置

确保配置文件中包含小红书 API 凭据：

```ini
[xiaohongshu]
APP_ID = your_app_id
APP_SECRET = your_app_secret
```

### 3. 访问令牌

确保数据库中有有效的小红书 API 访问令牌。如果没有，请先通过授权流程获取令牌。

## 注意事项

### 1. API 限制

- **数据延迟**：离线数据最早在次日 10 点产出，建议在上午 10 点后执行
- **调用频率**：避免过于频繁调用 API，建议每日执行一次
- **分页限制**：单次查询最多返回 500 条记录

### 2. 时间范围

- **推荐范围**：建议不要查询过长的时间范围，避免数据量过大
- **历史数据**：首次运行可能需要较长时间处理历史数据
- **增量更新**：日常维护建议使用增量更新模式

### 3. 数据一致性

- **重复执行**：重复执行相同时间范围的更新是安全的，会覆盖原有数据
- **事务保护**：使用数据库事务确保数据一致性
- **错误回滚**：遇到错误时会自动回滚所有更改

### 4. 性能优化

- **分页获取**：自动使用分页避免内存溢出
- **批量更新**：使用批量更新提高数据库性能
- **索引利用**：更新时使用 `note_id` 索引提高查询效率

## 监控和日志

### 1. 日志级别

- **INFO**：关键流程信息和结果统计
- **DEBUG**：详细的处理信息（每个笔记的更新情况）
- **WARNING**：非致命错误和异常情况
- **ERROR**：严重错误和异常

### 2. 关键日志

```
# 开始处理
INFO: 开始更新笔记流量推广费，查询时间范围：2020-01-01 到 2024-12-20

# API调用
INFO: 正在获取小红书API访问令牌...
INFO: 成功获取访问令牌，找到 2 个授权广告主
INFO: 正在处理广告主：品牌名称 (ID: 12345)

# 数据获取
INFO: 第 1 页：获取到 500 条记录，总记录数：1250
INFO: 从API获取完成，共获得 1250 条记录，涉及 156 个唯一笔记

# 数据库更新
INFO: 数据库更新完成，成功更新 156 条记录
INFO: 更新完成：{'success_count': 156, 'error_count': 0, 'total_notes': 1250, 'unique_notes': 156}
```

## 故障排除

### 1. 常见问题

**问题 1：访问令牌失效**

```
ERROR: 获取访问令牌失败
```

**解决方案**：

- 检查小红书 API 配置
- 重新进行授权流程
- 确认刷新令牌是否有效

**问题 2：没有授权广告主**

```
ERROR: 没有授权的广告主
```

**解决方案**：

- 确认已完成小红书广告主授权
- 检查访问令牌的授权范围

**问题 3：数据库连接失败**

```
ERROR: 数据库操作发生错误
```

**解决方案**：

- 检查数据库配置和连接
- 确认数据库服务是否正常
- 检查网络连接

### 2. 调试方法

1. **启用调试日志**：设置日志级别为 DEBUG 查看详细信息
2. **小范围测试**：先用较小的时间范围测试功能
3. **检查网络**：确认可以正常访问小红书 API
4. **验证数据**：手动检查更新后的数据是否正确

## 维护建议

### 1. 定期任务

建议设置定期任务（如使用 cron 或任务调度器）每日自动更新：

```bash
# 每天上午11点执行更新（确保数据已产出）
0 11 * * * /path/to/python /path/to/your/project/app/services/update_note_cost_flow.py
```

### 2. 监控告警

建议设置监控告警：

- 更新失败时发送通知
- 更新数量异常时发送告警
- API 调用失败时发送通知

### 3. 数据备份

建议在大批量更新前备份相关数据：

```sql
-- 备份BrandXhsNote表
CREATE TABLE brand_xhs_note_backup AS SELECT * FROM brand_xhs_note;
```

## 扩展功能

### 1. 添加过滤条件

可以扩展功能支持更多过滤条件：

```python
from libs.xiaohongshu import FilterClause

# 只处理消费金额大于100元的笔记
filter_clause = FilterClause(column="fee", operator=">", values=["100"])

params = GetNoteOfflineReportParams(
    # ... 其他参数
    filters=[filter_clause]
)
```

### 2. 增加其他字段

可以扩展功能同时更新其他字段：

```python
# 在更新时同时更新其他相关字段
stmt = (
    update(BrandXhsNote)
    .where(BrandXhsNote.note_id == note_id)
    .values(
        cost_flow=cost_flow,
        # 可以添加其他字段
        # last_update_time=int(time.time())
    )
)
```

### 3. 数据导出

可以添加数据导出功能：

```python
import pandas as pd

async def export_updated_data():
    """导出更新后的数据"""
    async with get_session() as session:
        result = await session.execute(
            select(BrandXhsNote).where(BrandXhsNote.cost_flow > 0)
        )
        notes = result.scalars().all()

        # 转换为DataFrame并导出
        df = pd.DataFrame([note.to_dict() for note in notes])
        df.to_csv("updated_notes.csv", index=False)
```
