[project]
name = "KOPHENIX_DATAPILOT"
version = "0.1.0"
description = "数据管理平台"
requires-python = ">=3.13"
dependencies = [
    "aiofiles==24.1.0",
    "aiohappyeyeballs==2.4.4",
    "aiohttp==3.11.10",
    "aiomysql==0.2.0",
    "aiosignal==1.3.1",
    "alibabacloud-dysmsapi20170525==3.1.2",
    "annotated-types==0.7.0",
    "anyio==4.7.0",
    "attrs==24.2.0",
    "certifi==2024.8.30",
    "click==8.1.7",
    "colorama==0.4.6",
    "coverage==7.6.9",
    "dnspython==2.7.0",
    "email_validator==2.2.0",
    "fastapi==0.115.6",
    "fastapi-cli==0.0.6",
    "frozenlist==1.5.0",
    "greenlet==3.1.1",
    "h11==0.14.0",
    "httpcore==1.0.7",
    "httptools==0.6.4",
    "httpx==0.28.0",
    "idna==3.10",
    "iniconfig==2.0.0",
    "Jinja2==3.1.4",
    "markdown-it-py==3.0.0",
    "MarkupSafe==3.0.2",
    "mdurl==0.1.2",
    "motor==3.6.0",
    "multidict==6.1.0",
    "packaging==24.2",
    "pluggy==1.5.0",
    "propcache==0.2.1",
    "pydantic==2.10.3",
    "pydantic-settings==2.6.1",
    "pydantic_core==2.27.1",
    "Pygments==2.18.0",
    "pyjwt>=2.10.1",
    "pymongo==4.9.2",
    "pymysql==1.1.1",
    "pytest==8.3.4",
    "pytest-asyncio==0.24.0",
    "pytest-cov==6.0.0",
    "python-dateutil>=2.9.0.post0",
    "python-dotenv==1.0.1",
    "python-multipart==0.0.19",
    "pyxxl>=0.4.0",
    "PyYAML==6.0.2",
    "redis>=5.2.1",
    "requests>=2.32.3",
    "rich==13.9.4",
    "rich-toolkit==0.12.0",
    "shellingham==1.5.4",
    "sniffio==1.3.1",
    "SQLAlchemy==2.0.36",
    "starlette==0.41.3",
    "typer==0.15.1",
    "typing_extensions==4.12.2",
    "uvicorn==0.32.1",
    "watchfiles==1.0.0",
    "websockets==14.1",
    "yarl==1.18.3",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.metadata]
allow-direct-references = true

[tool.pytest.ini_options]
addopts = "-v --cov=app --cov-report=term-missing"
testpaths = ["tests"]
python_files = ["test_*.py"]
asyncio_mode = "auto"

[tool.ruff]
line-length = 120

[tool.hatch.build.targets.wheel]
packages = ["app"] 
