# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_registerPackageRoute.param.LogisticsRegisterPackageRouteParam import LogisticsRegisterPackageRouteParam


class LogisticsRegisterPackageRouteRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsRegisterPackageRouteParam()

	def getUrlPath(self, ):
		return "/logistics/registerPackageRoute"

	def getParams(self, ):
		return self.params



