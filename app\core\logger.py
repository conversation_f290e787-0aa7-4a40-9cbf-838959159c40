import logging
from fastapi import logger as fastapi_logger
from rich.logging import RichHandler
from rich.console import Console

# 创建控制台对象
console = Console()

# 获取logger
logger = logging.getLogger("console")

# 设置logger级别为DEBUG
logger.setLevel(logging.DEBUG)

# 使用Rich的处理器替换默认处理器
rich_handler = RichHandler(
    console=console,
    show_time=True,
    show_level=True,
    show_path=True,
    markup=True,
    rich_tracebacks=True,
    tracebacks_show_locals=True,
    log_time_format="[%X]",
)
rich_handler.setLevel(logging.DEBUG)

# 清除现有的处理器（如果有的话）
logger.handlers.clear()
logger.addHandler(rich_handler)

# 防止日志重复输出
logger.propagate = False


def get_logger():
    """获取logger实例"""
    return logger


# 保留便捷的日志记录方法（可选使用）
def info(msg, *args, **kwargs):
    """记录INFO级别日志 - 蓝色"""
    logger.info(f"[blue]{msg}[/blue]", *args, **kwargs)


def error(msg, *args, **kwargs):
    """记录ERROR级别日志 - 红色"""
    logger.error(f"[red]{msg}[/red]", *args, **kwargs)


def warning(msg, *args, **kwargs):
    """记录WARNING级别日志 - 黄色"""
    logger.warning(f"[yellow]{msg}[/yellow]", *args, **kwargs)


def debug(msg, *args, **kwargs):
    """记录DEBUG级别日志 - 绿色"""
    logger.debug(f"[green]{msg}[/green]", *args, **kwargs)


def success(msg, *args, **kwargs):
    """记录成功消息 - 亮绿色"""
    logger.info(f"[bright_green]✓ {msg}[/bright_green]", *args, **kwargs)


def failure(msg, *args, **kwargs):
    """记录失败消息 - 亮红色"""
    logger.error(f"[bright_red]✗ {msg}[/bright_red]", *args, **kwargs)
