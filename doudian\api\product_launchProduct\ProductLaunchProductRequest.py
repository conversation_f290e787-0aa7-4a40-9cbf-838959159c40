# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_launchProduct.param.ProductLaunchProductParam import ProductLaunchProductParam


class ProductLaunchProductRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductLaunchProductParam()

	def getUrlPath(self, ):
		return "/product/launchProduct"

	def getParams(self, ):
		return self.params



