from sqlalchemy import Column, Integer, String, Date
from sqlalchemy.orm import Mapped
from datetime import datetime
from .base import BaseModel


class ProductionBatch(BaseModel):
    __tablename__ = "production_batch"
    id = Column(Integer, primary_key=True)
    batch_no = Column(String)
    item_code = Column(String)
    item_name = Column(String)
    qty = Column(Integer)
    # 已分配数量
    allocated_qty = Column(Integer, default=0)


class ProductionBatchAllocateRecord(BaseModel):
    """
    生产批次分配记录
    """

    __tablename__ = "production_batch_allocate_record"
    id: Mapped[int] = Column(Integer, primary_key=True)
    batch_no: Mapped[str] = Column(String)
    shop_code: Mapped[str] = Column(String)
    shop_name: Mapped[str] = Column(String)
    item_code: Mapped[str] = Column(String)
    item_name: Mapped[str] = Column(String)
    platform_code: Mapped[str] = Column(String)
    platform_name: Mapped[str] = Column(String)
    allocated_qty: Mapped[int] = Column(Integer)
    order_sales_item_shop_sum_id: Mapped[int] = Column(Integer)
    date: Mapped[datetime] = Column(Date)
