# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_queryPackageRoute.param.LogisticsQueryPackageRouteParam import LogisticsQueryPackageRouteParam


class LogisticsQueryPackageRouteRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsQueryPackageRouteParam()

	def getUrlPath(self, ):
		return "/logistics/queryPackageRoute"

	def getParams(self, ):
		return self.params



