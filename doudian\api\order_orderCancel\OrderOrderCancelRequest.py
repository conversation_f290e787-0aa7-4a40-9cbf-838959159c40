# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_orderCancel.param.OrderOrderCancelParam import OrderOrderCancelParam


class OrderOrderCancelRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderOrderCancelParam()

	def getUrlPath(self, ):
		return "/order/orderCancel"

	def getParams(self, ):
		return self.params



