# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.openCloud_ddpAddShopTask.param.OpenCloudDdpAddShopTaskParam import OpenCloudDdpAddShopTaskParam


class OpenCloudDdpAddShopTaskRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OpenCloudDdpAddShopTaskParam()

	def getUrlPath(self, ):
		return "/openCloud/ddpAddShopTask"

	def getParams(self, ):
		return self.params



