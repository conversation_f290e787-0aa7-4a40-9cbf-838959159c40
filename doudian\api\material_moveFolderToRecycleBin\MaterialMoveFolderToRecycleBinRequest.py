# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_moveFolderToRecycleBin.param.MaterialMoveFolderToRecycleBinParam import MaterialMoveFolderToRecycleBinParam


class MaterialMoveFolderToRecycleBinRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialMoveFolderToRecycleBinParam()

	def getUrlPath(self, ):
		return "/material/moveFolderToRecycleBin"

	def getParams(self, ):
		return self.params



