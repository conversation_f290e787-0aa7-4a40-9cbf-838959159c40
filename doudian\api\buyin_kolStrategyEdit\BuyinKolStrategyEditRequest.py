# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.buyin_kolStrategyEdit.param.BuyinKolStrategyEditParam import BuyinKolStrategyEditParam


class BuyinKolStrategyEditRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = BuyinKolStrategyEditParam()

	def getUrlPath(self, ):
		return "/buyin/kolStrategyEdit"

	def getParams(self, ):
		return self.params



