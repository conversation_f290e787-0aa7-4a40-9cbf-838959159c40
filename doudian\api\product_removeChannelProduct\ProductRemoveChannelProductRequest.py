# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_removeChannelProduct.param.ProductRemoveChannelProductParam import ProductRemoveChannelProductParam


class ProductRemoveChannelProductRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductRemoveChannelProductParam()

	def getUrlPath(self, ):
		return "/product/removeChannelProduct"

	def getParams(self, ):
		return self.params



