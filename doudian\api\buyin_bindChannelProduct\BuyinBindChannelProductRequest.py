# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.buyin_bindChannelProduct.param.BuyinBindChannelProductParam import BuyinBindChannelProductParam


class BuyinBindChannelProductRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = BuyinBindChannelProductParam()

	def getUrlPath(self, ):
		return "/buyin/bindChannelProduct"

	def getParams(self, ):
		return self.params



