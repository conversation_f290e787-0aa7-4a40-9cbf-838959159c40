# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_logisticsAddSinglePack.param.OrderLogisticsAddSinglePackParam import OrderLogisticsAddSinglePackParam


class OrderLogisticsAddSinglePackRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderLogisticsAddSinglePackParam()

	def getUrlPath(self, ):
		return "/order/logisticsAddSinglePack"

	def getParams(self, ):
		return self.params



