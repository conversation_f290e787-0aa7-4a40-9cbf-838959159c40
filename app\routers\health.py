from typing import Dict, Any
from app.core.base_router import BaseRouter
from app.schemas.response import ResponseModel

router = BaseRouter()


@router.get(
    "/health",
    response_model=ResponseModel[Dict[str, Any]],
    summary="健康检查",
    description="检查API服务是否正常运行",
)
async def health_check():
    """
    健康检查接口

    返回:
        - status: 服务状态
    """
    return {"code": 200, "msg": "success", "data": {"status": "healthy"}}


@router.get(
    "/",
    response_model=ResponseModel[Dict[str, Any]],
    summary="欢迎页面",
    description="返回欢迎信息",
)
async def root():
    """
    欢迎页面接口

    返回:
        - message: 欢迎信息
    """
    return {
        "code": 200,
        "msg": "success",
        "data": {"message": "欢迎使用 FastAPI MongoDB 应用"},
    }
