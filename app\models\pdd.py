from sqlalchemy import (
    Column,
    String,
    DateTime,
    Integer,
    Text,
    Numeric,
    Boolean,
    Index,
)
from sqlalchemy.orm import Mapped
from datetime import datetime
from .base import Base


class PddMall(Base):
    __tablename__ = "pdd_mall"

    id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True, comment="序号"
    )
    mallId: Mapped[str] = Column(String(40), nullable=False, comment="店铺Id")
    mallName: Mapped[str] = Column(String(200), nullable=False, comment="店铺名称")
    abbrName: Mapped[str] = Column(String(200), nullable=True, comment="店铺简称")
    gyName: Mapped[str] = Column(String(200), nullable=True, comment="管易店铺名")
    gyCode: Mapped[str] = Column(String(200), nullable=True, comment="管易店铺代码")
    nameMapType: Mapped[int] = Column(Integer, nullable=True, comment="名称对齐类型")
    aclUser: Mapped[str] = Column(String(200), nullable=True, comment="店铺Logo")
    aclUserIds: Mapped[str] = Column(String(4000), nullable=True, comment="权限用户Id")
    aclDingIds: Mapped[str] = Column(
        String(4000), nullable=True, comment="权限用户钉钉Id"
    )
    mallLogo: Mapped[str] = Column(String(200), nullable=True, comment="店铺Logo")
    mallStatus: Mapped[int] = Column(Integer, nullable=True, comment="店铺状态")
    mallCompany: Mapped[str] = Column(String(20), nullable=True, comment="店铺主体")
    mallCompanyOrdr: Mapped[str] = Column(
        String(10), nullable=True, comment="店铺主体排序"
    )
    mallPlatform: Mapped[str] = Column(String(20), nullable=True, comment="店铺平台")
    mallPlatformOrdr: Mapped[str] = Column(
        String(10), nullable=True, comment="店铺平台排序"
    )
    mallType: Mapped[str] = Column(String(20), nullable=True, comment="店铺类型")
    mallGroup: Mapped[str] = Column(String(20), nullable=True, comment="店铺分组")
    mallGroupOrdr: Mapped[str] = Column(
        String(10), nullable=True, comment="店铺分组排序"
    )
    createTime: Mapped[datetime] = Column(DateTime, nullable=True, comment="创建时间")
    lastLoginTime: Mapped[datetime] = Column(
        DateTime, nullable=True, comment="最后登录时间"
    )
    lastCrawlTime: Mapped[datetime] = Column(
        DateTime, nullable=True, comment="最后爬取时间"
    )
    appOnline: Mapped[bool] = Column(
        Boolean, nullable=False, default=False, comment="APP状态"
    )
    appLock: Mapped[bool] = Column(
        Boolean, nullable=False, default=False, comment="APP锁止"
    )
    appForceExit: Mapped[bool] = Column(
        Boolean, nullable=False, default=False, comment="APP退出"
    )
    loginStatus: Mapped[int] = Column(Integer, nullable=True, comment="登录状态")
    loginGroup: Mapped[int] = Column(
        Integer, nullable=False, default=1, comment="登录分组"
    )
    loginUser: Mapped[str] = Column(
        String(20), nullable=True, comment="拼多多子账号归属"
    )
    apiToken: Mapped[str] = Column(String(200), nullable=True, comment="API TOKEN")
    apiTokenExpiresAt: Mapped[int] = Column(
        Integer, nullable=True, comment="API TOKEN 过期时间"
    )
    apiRefreshToken: Mapped[str] = Column(
        String(200), nullable=True, comment="API Refresh TOKEN"
    )
    apiRefreshTokenExpiresAt: Mapped[int] = Column(
        Integer, nullable=True, comment="API Refresh TOKEN 过期时间"
    )
    loginUsernameId: Mapped[str] = Column(
        String(200), nullable=True, comment="账号登录用户"
    )
    loginPasswordId: Mapped[str] = Column(
        String(200), nullable=True, comment="账号登录密码"
    )
    caiwuDept: Mapped[str] = Column(String(40), nullable=True, comment="财务部门")
    caiwuDeptOrdr: Mapped[str] = Column(
        String(10), nullable=True, comment="财务部门排序"
    )
    caiwuOrdr: Mapped[str] = Column(String(20), nullable=True, comment="财务排序")


class PddDailyReport(Base):
    """拼多多每日报表"""

    __tablename__ = "pdd_daily_report"

    id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True, comment="序号"
    )
    mallId: Mapped[str] = Column(String(20), nullable=False, comment="店铺Id")
    date: Mapped[datetime] = Column(DateTime, nullable=True, comment="日期")
    type: Mapped[str] = Column(String(20), nullable=False, comment="类型")
    spend: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="总花费(元)")
    orderSpend: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="成交花费(元)"
    )
    gmv: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="交易额(元)")
    roi: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="实际投产比")
    orderNum: Mapped[int] = Column(Integer, nullable=True, comment="成交笔数")
    costPerOrder: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="每笔成交花费(元)"
    )
    avgPayAmount: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="每笔成交金额(元)"
    )
    globalTakeRate: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="全站推广费比"
    )
    impression: Mapped[int] = Column(Integer, nullable=True, comment="曝光量")
    click: Mapped[int] = Column(Integer, nullable=True, comment="点击量")
    ctr: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="点击率")
    cvr: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="点击转化率")
    cpc: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="平均点击花费")
    mallFavNum: Mapped[int] = Column(Integer, nullable=True, comment="店铺关注量")
    goodsFavNum: Mapped[int] = Column(Integer, nullable=True, comment="商品收藏量")
    inquiryNum: Mapped[int] = Column(Integer, nullable=True, comment="询单量")
    modifyTime: Mapped[datetime] = Column(DateTime, nullable=True, comment="修改时间")

    __table_args__ = (Index("idx_pdd_daily_report_1", "mallId", "date", "type"),)


class PddHourlyReport(Base):
    """拼多多每小时报表"""

    __tablename__ = "pdd_hourly_report"

    id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True, comment="序号"
    )
    mallId: Mapped[str] = Column(String(20), nullable=False, comment="店铺Id")
    date: Mapped[datetime] = Column(DateTime, nullable=True, comment="日期")
    hour: Mapped[int] = Column(Integer, nullable=True, comment="小时")
    type: Mapped[str] = Column(String(20), nullable=False, comment="类型")
    spend: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="总花费(元)")
    orderSpend: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="成交花费(元)"
    )
    gmv: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="交易额(元)")
    roi: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="实际投产比")
    orderNum: Mapped[int] = Column(Integer, nullable=True, comment="成交笔数")
    costPerOrder: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="每笔成交花费(元)"
    )
    avgPayAmount: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="每笔成交金额(元)"
    )
    globalTakeRate: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="全站推广费比"
    )
    impression: Mapped[int] = Column(Integer, nullable=True, comment="曝光量")
    click: Mapped[int] = Column(Integer, nullable=True, comment="点击量")
    ctr: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="点击率")
    cvr: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="点击转化率")
    cpc: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="平均点击花费")
    mallFavNum: Mapped[int] = Column(Integer, nullable=True, comment="店铺关注量")
    goodsFavNum: Mapped[int] = Column(Integer, nullable=True, comment="商品收藏量")
    inquiryNum: Mapped[int] = Column(Integer, nullable=True, comment="询单量")
    modifyTime: Mapped[datetime] = Column(DateTime, nullable=True, comment="修改时间")

    __table_args__ = (
        Index("idx_pdd_hourly_report_1", "mallId", "date", "hour", "type"),
    )


class PddTgsj(Base):
    """拼多多推广数据采集表"""

    __tablename__ = "pdd_tgsj"

    id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True, comment="序号"
    )
    mallId: Mapped[str] = Column(String(20), nullable=False, comment="店铺Id")
    mallName: Mapped[str] = Column(String(200), nullable=False, comment="店铺名称")
    type: Mapped[str] = Column(String(20), nullable=True, comment="类型")
    pageUrl: Mapped[str] = Column(String(400), nullable=True, comment="页面网址")
    ajaxType: Mapped[str] = Column(String(40), nullable=True, comment="AJAX类型")
    ajax: Mapped[str] = Column(String(400), nullable=True, comment="AJAX")
    date: Mapped[datetime] = Column(DateTime, nullable=True, comment="日期")
    content: Mapped[str] = Column(
        Text(length=4294967295), nullable=True, comment="AJAX返回结果"
    )
    modifyTime: Mapped[datetime] = Column(DateTime, nullable=True, comment="操作时间")

    __table_args__ = (
        Index("idx_pdd_tgsj_1", "mallId", "type", "ajaxType", "date"),
        Index("idx_pdd_tgsj_2", "date"),
        Index("idx_pdd_tgsj_3", "type"),
        {
            "mysql_engine": "InnoDB",
            "mysql_charset": "utf8",
            "mysql_row_format": "DYNAMIC",
        },
    )


class PddJmsj(Base):
    """拼多多加密数据采集表"""

    __tablename__ = "pdd_jmsj"

    id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True, comment="序号"
    )
    mallId: Mapped[str] = Column(String(20), nullable=False, comment="店铺Id")
    mallName: Mapped[str] = Column(String(200), nullable=False, comment="店铺名称")
    type: Mapped[str] = Column(String(20), nullable=True, comment="类型")
    pageUrl: Mapped[str] = Column(String(400), nullable=True, comment="页面网址")
    ttfUrl: Mapped[str] = Column(String(200), nullable=True, comment="ttf网址")
    ttfName: Mapped[str] = Column(String(200), nullable=True, comment="ttf文件名")
    ajaxType: Mapped[str] = Column(String(40), nullable=True, comment="AJAX类型")
    ajax: Mapped[str] = Column(String(400), nullable=True, comment="AJAX")
    date: Mapped[datetime] = Column(DateTime, nullable=True, comment="日期")
    content: Mapped[str] = Column(
        Text(length=4294967295), nullable=True, comment="AJAX返回结果"
    )
    content2: Mapped[str] = Column(
        Text(length=4294967295), nullable=True, comment="AJAX返回结果（未解密）"
    )
    modifyTime: Mapped[datetime] = Column(DateTime, nullable=True, comment="操作时间")

    __table_args__ = (
        Index("idx_pdd_jmsj_1", "mallId", "type", "ajaxType", "date"),
        {
            "mysql_engine": "InnoDB",
            "mysql_charset": "utf8mb4",
            "mysql_collate": "utf8mb4_unicode_ci",
        },
    )


class PddTgspDetails(Base):
    """拼多多推广商品明细表"""

    __tablename__ = "pdd_tgsp_details"

    id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True, comment="序号"
    )
    pid: Mapped[int] = Column(Integer, nullable=True, comment="上级数据Id")
    mallId: Mapped[str] = Column(String(20), nullable=False, comment="店铺Id")
    date: Mapped[datetime] = Column(DateTime, nullable=True, comment="日期")
    type: Mapped[str] = Column(String(20), nullable=False, comment="类型")
    goodsId: Mapped[str] = Column(String(20), nullable=False, comment="商品Id")
    goodsName: Mapped[str] = Column(String(200), nullable=True, comment="商品名称")
    adId: Mapped[str] = Column(String(20), nullable=True)
    adName: Mapped[str] = Column(String(200), nullable=True)
    thumbUrl: Mapped[str] = Column(String(200), nullable=True)
    adStatus: Mapped[int] = Column(Integer, nullable=True)
    adGroupId: Mapped[int] = Column(Integer, nullable=True)
    adGroupName: Mapped[str] = Column(String(40), nullable=True)
    todaySpend: Mapped[float] = Column(Numeric(8, 2), nullable=True)
    targetRoi: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="目标投产比"
    )
    planId: Mapped[str] = Column(String(20), nullable=True)
    beginTime: Mapped[datetime] = Column(DateTime, nullable=True)
    endTime: Mapped[datetime] = Column(DateTime, nullable=True)
    spend: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="花费(元)")
    gmv: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="交易额(元)")
    roi: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="实际投产比")
    orderNum: Mapped[int] = Column(Integer, nullable=True, comment="成交笔数")
    costPerOrder: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="每笔成交花费(元)"
    )
    avgPayAmount: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="每笔成交金额(元)"
    )
    globalTakeRate: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="全站推广费比"
    )
    impression: Mapped[int] = Column(Integer, nullable=True, comment="曝光量")
    click: Mapped[int] = Column(Integer, nullable=True, comment="点击量")
    ctr: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="点击率")
    cvr: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="点击转化率")
    cpc: Mapped[float] = Column(Numeric(8, 2), nullable=True, comment="平均点击花费")
    cpm: Mapped[float] = Column(Numeric(8, 2), nullable=True)
    multiGoalMallFavNum: Mapped[int] = Column(
        Integer, nullable=True, comment="店铺关注量"
    )
    multiGoalGoodsFavNum: Mapped[int] = Column(
        Integer, nullable=True, comment="商品收藏量"
    )
    multiGoalInquiryNum: Mapped[int] = Column(Integer, nullable=True, comment="询单量")
    orderSpend: Mapped[float] = Column(Numeric(8, 2), nullable=True)
    mallFavSpend: Mapped[float] = Column(Numeric(8, 2), nullable=True)
    goodsFavSpend: Mapped[float] = Column(Numeric(8, 2), nullable=True)
    inquirySpend: Mapped[float] = Column(Numeric(8, 2), nullable=True)
    inquiryNum: Mapped[int] = Column(Integer, nullable=True, comment="询单量")
    goodsFavNum: Mapped[int] = Column(Integer, nullable=True, comment="收藏量")
    costPerGoodsFav: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="平均收藏成本(元)"
    )
    quickRefundGmv: Mapped[float] = Column(Numeric(8, 2), nullable=True)
    netGmv: Mapped[float] = Column(Numeric(8, 2), nullable=True)
    directGmv: Mapped[float] = Column(Numeric(8, 2), nullable=True)
    indirectGmv: Mapped[float] = Column(Numeric(8, 2), nullable=True)
    globalGmv: Mapped[float] = Column(Numeric(8, 2), nullable=True)
    orderSpendRoi: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="实际投产比"
    )
    orderSpendNetRoi: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="实际净投产比"
    )
    quickRefundOrderNum: Mapped[int] = Column(Integer, nullable=True)
    netOrderNum: Mapped[int] = Column(Integer, nullable=True)
    directOrderNum: Mapped[int] = Column(Integer, nullable=True)
    indirectOrderNum: Mapped[int] = Column(Integer, nullable=True)
    avgDirectPayAmount: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="每笔直接成交金额(元)"
    )
    avgIndirectPayAmount: Mapped[float] = Column(
        Numeric(8, 2), nullable=True, comment="每笔间接成交金额(元)"
    )
    modifyTime: Mapped[datetime] = Column(DateTime, nullable=True, comment="修改时间")

    __table_args__ = (
        Index("idx_pdd_tgsp_details_1", "pid"),
        Index("idx_pdd_tgsp_details_2", "mallId", "date", "goodsId"),
        Index("idx_pdd_tgsp_details_4", "date"),
        Index("idx_pdd_tgsp_details_5", "modifyTime"),
        {
            "mysql_charset": "utf8",
        },
    )
