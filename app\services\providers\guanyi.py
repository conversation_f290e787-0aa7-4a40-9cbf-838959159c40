import time
import hashlib
from typing import Dict, Any, Optional
from app.services.base import BaseService
from app.services.exceptions import ServiceConfigError


class GuanyiService(BaseService):
    def __init__(
        self, appkey: str, sessionkey: str, secret: str, sandbox: bool = False
    ):
        """
        初始化管易服务

        Args:
            appkey: 应用key
            sessionkey: 会话key
            secret: 密钥
        """
        super().__init__()
        if not all([appkey, sessionkey, secret]):
            raise ServiceConfigError("管易服务配置不完整")

        self.appkey = appkey
        self.sessionkey = sessionkey
        self.secret = secret
        self.base_url = "http://api.guanyierp.com/rest/erp_open"

    def _generate_sign(self, params: Dict[str, Any]) -> str:
        """
        生成签名
        """
        # 按参数名称升序排序
        sorted_params = dict(sorted(params.items()))

        # 构造签名字符串
        sign_str = self.secret
        for k, v in sorted_params.items():
            if v is not None and v != "":
                sign_str += f"{k}{v}"
        sign_str += self.secret

        # MD5加密
        return hashlib.md5(sign_str.encode()).hexdigest().upper()

    def _prepare_params(self, method: str, biz_params: Optional[Dict] = None) -> Dict:
        """
        准备请求参数
        """
        timestamp = str(int(time.time()))

        # 基础参数
        params = {
            "appkey": self.appkey,
            "sessionkey": self.sessionkey,
            "method": method,
        }

        # 添加业务参数
        if biz_params:
            for k, v in biz_params.items():
                params[k] = v

        # 生成签名
        params["sign"] = self._generate_sign(params)
        return params

    async def call_api(self, method: str, biz_params: Optional[Dict] = None) -> Dict:
        """
        调用管易API

        Args:
            method: API方法名
            biz_params: 业务参数

        Returns:
            API响应结果
        """
        params = self._prepare_params(method, biz_params)
        return await self.post(self.base_url, params=params)

    async def get_shop_list(self) -> Dict:
        """
        获取店铺列表
        """
        return await self.call_api("gy.erp.shop.get")

    async def get_trade_list(
        self, page_no: int = 1, page_size: int = 100, **kwargs
    ) -> Dict:
        """
        获取订单列表

        Args:
            page_no: 页码
            page_size: 每页数量
            **kwargs: 其他查询参数
        """
        biz_params = {"page_no": page_no, "page_size": page_size, **kwargs}
        return await self.call_api("gy.erp.trade.get", biz_params)

    async def get_inventory_list(
        self, page_no: int = 1, page_size: int = 100, **kwargs
    ) -> Dict:
        """
        获取库存列表

        Args:
            page_no: 页码
            page_size: 每页数量
            **kwargs: 其他查询参数
        """
        biz_params = {"page_no": page_no, "page_size": page_size, **kwargs}
        return await self.call_api("gy.erp.items.inventory.get", biz_params)
