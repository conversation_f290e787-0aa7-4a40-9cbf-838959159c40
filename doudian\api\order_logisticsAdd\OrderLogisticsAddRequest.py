# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_logisticsAdd.param.OrderLogisticsAddParam import OrderLogisticsAddParam


class OrderLogisticsAddRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderLogisticsAddParam()

	def getUrlPath(self, ):
		return "/order/logisticsAdd"

	def getParams(self, ):
		return self.params



