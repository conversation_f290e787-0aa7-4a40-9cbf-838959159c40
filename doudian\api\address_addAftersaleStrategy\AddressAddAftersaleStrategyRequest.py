# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.address_addAftersaleStrategy.param.AddressAddAftersaleStrategyParam import AddressAddAftersaleStrategyParam


class AddressAddAftersaleStrategyRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AddressAddAftersaleStrategyParam()

	def getUrlPath(self, ):
		return "/address/addAftersaleStrategy"

	def getParams(self, ):
		return self.params



