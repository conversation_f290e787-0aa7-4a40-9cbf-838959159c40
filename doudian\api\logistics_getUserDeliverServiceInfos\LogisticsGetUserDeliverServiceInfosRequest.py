# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_getUserDeliverServiceInfos.param.LogisticsGetUserDeliverServiceInfosParam import LogisticsGetUserDeliverServiceInfosParam


class LogisticsGetUserDeliverServiceInfosRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsGetUserDeliverServiceInfosParam()

	def getUrlPath(self, ):
		return "/logistics/getUserDeliverServiceInfos"

	def getParams(self, ):
		return self.params



