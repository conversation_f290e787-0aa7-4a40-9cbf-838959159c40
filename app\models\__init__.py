# 基础模型和数据库连接
from .base import (
    EmptyModel,
    BaseModel,
    Base,
    engine,
    sync_engine,
    session_maker,
    sync_session_maker,
    get_session,
    get_sync_session,
)

# 生产相关模型
from .production import (
    ProductionBatch,
    ProductionBatchAllocateRecord,
)

# 管易ERP相关模型
from .guanyierp import (
    GuanyierpStockOtherInOrderDetail,
    GuanyierpStockOtherInOrder,
    GuanyierpPurchase,
    GuanyierpPurchaseArrive,
    GuanyierpPurchaseArriveDetail,
    GuanyierpGoods3,
    GuanyierpGoods,
)

# 拼多多相关模型
from .pdd import (
    PddMall,
    PddDailyReport,
    PddHourlyReport,
    PddTgsj,
    PddJmsj,
    PddTgspDetails,
)

# 业务规划相关模型
from .business import (
    BusinessAnnualPlanning,
    ShopPerformance,
)

# 小红书品牌相关模型
from .brand_xhs import (
    BrandXhsJgCreativityDataReport,
    BrandXhsJgCreativity,
    BrandXhsJgOverallDataReport,
    BrandXhsNote,
)

# 订单销售相关模型
from .order_sales import (
    ProductOrderSalesItemShopSum,
)

# 导出所有模型类
__all__ = [
    # 基础类
    "EmptyModel",
    "BaseModel",
    "Base",
    "engine",
    "sync_engine",
    "session_maker",
    "sync_session_maker",
    "get_session",
    "get_sync_session",
    # 生产相关
    "ProductionBatch",
    "ProductionBatchAllocateRecord",
    # 管易ERP相关
    "GuanyierpStockOtherInOrderDetail",
    "GuanyierpStockOtherInOrder",
    "GuanyierpPurchase",
    "GuanyierpPurchaseArrive",
    "GuanyierpPurchaseArriveDetail",
    "GuanyierpGoods3",
    "GuanyierpGoods",
    # 拼多多相关
    "PddMall",
    "PddDailyReport",
    "PddHourlyReport",
    "PddTgsj",
    "PddJmsj",
    "PddTgspDetails",
    # 业务规划相关
    "BusinessAnnualPlanning",
    "ShopPerformance",
    # 小红书品牌相关
    "BrandXhsJgCreativityDataReport",
    "BrandXhsJgCreativity",
    "BrandXhsJgOverallDataReport",
    "BrandXhsNote",
    # 订单销售相关
    "ProductOrderSalesItemShopSum",
]
