from fastapi import HTTPException, Security, Request, Depends
from fastapi.security.api_key import APIKeyHeader
from app.core.config import settings
import jwt

# API密钥验证
API_KEY_NAME = "X-API-Key"
api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)

# JWT验证
JWT_NAME = "X-Access-Token"
jwt_header = APIKeyHeader(name=JWT_NAME, auto_error=False)

# 定义需要忽略API密钥验证的路由列表
EXEMPT_ROUTES = ["/health", "/", "/docs", "/redoc", "/openapi.json"]


async def verify_api_key(request: Request, api_key: str = Security(api_key_header)):
    """验证API密钥"""

    # 检查当前路径是否在忽略列表中
    current_path = request.url.path
    if current_path in EXEMPT_ROUTES:
        return api_key

    # 检查是否以某些前缀开头的路径
    for exempt_prefix in getattr(settings, "API_KEY_EXEMPT_PREFIXES", ["/guanyi"]):
        if current_path.startswith(exempt_prefix):
            return api_key

    if not settings.API_KEY:
        return api_key
    if not api_key:
        raise HTTPException(status_code=401, detail="缺少API密钥")
    if api_key != settings.API_KEY:
        raise HTTPException(status_code=401, detail="无效的API密钥")
    return api_key


# 验证API密钥或JWT，API KEY 从 X-API-Key 获取，JWT 从 X-Access-Token 获取
async def verify_api_key_or_jwt(
    request: Request,
    api_key: str = Security(api_key_header),
    jwt_token: str = Security(jwt_header),
):
    """
    验证API密钥或JWT
    """
    # 检查当前路径是否在忽略列表中
    current_path = request.url.path
    if current_path in EXEMPT_ROUTES:
        return api_key or jwt_token

    # 检查是否以某些前缀开头的路径
    for exempt_prefix in getattr(settings, "API_KEY_EXEMPT_PREFIXES", ["/guanyi"]):
        if current_path.startswith(exempt_prefix):
            return api_key or jwt_token

    if not api_key and not jwt_token:
        raise HTTPException(status_code=401, detail="缺少API密钥或JWT")
    if api_key:
        if api_key != settings.API_KEY:
            raise HTTPException(status_code=401, detail="无效的API密钥")
        return api_key
    if jwt_token:
        # 验证JWT
        try:
            public_key = (
                "-----BEGIN PUBLIC KEY-----\n"
                + settings.JWT["PUBLIC_KEY"]
                + "\n-----END PUBLIC KEY-----"
            )
            payload: dict = jwt.decode(jwt_token, public_key, algorithms=["ES256"])
        except jwt.InvalidTokenError as e:
            raise HTTPException(status_code=401, detail=f"无效的JWT: {e}")
        request.state.user = payload
        return payload


if __name__ == "__main__":
    print(settings.JWT["PUBLIC_KEY"])
    public_key = (
        "-----BEGIN PUBLIC KEY-----\n"
        + settings.JWT["PUBLIC_KEY"]
        + "\n-----END PUBLIC KEY-----"
    )
    print(public_key)
    token = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    payload = jwt.decode(
        token, public_key, verify=True, algorithms=["ES256"], leeway=10
    )
    print(payload)
