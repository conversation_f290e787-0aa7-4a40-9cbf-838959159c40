KOPHENIX_DATAPILOT/                   # 项目根目录
├── .venv/                          # Python虚拟环境目录
├── .vscode/                        # VS Code配置目录
├── app/                            # 应用主目录
│   ├── __init__.py                # Python包初始化文件
│   ├── main.py                    # FastAPI应用主入口
│   ├── core/                      # 核心功能模块
│   ├── routers/                   # API路由处理器
│   ├── services/                  # 业务服务层
│   ├── schemas/                   # Pydantic模型/数据验证
│   ├── utils/                     # 工具函数
│   ├── crud/                      # 数据库CRUD操作
│   └── models/                    # 数据库模型
│
├── config/                        # 配置文件目录
│   ├── development.env            # 开发环境配置
│   ├── production.env             # 生产环境配置
│   ├── test.env                   # 测试环境配置
│   └── local.env                  # 本地环境配置
│
├── tests/                         # 测试目录
│   ├── __init__.py               # 测试包初始化文件
│   ├── conftest.py               # pytest配置和通用fixture
│   ├── routers/                  # API路由测试
│   └── services/                 # 服务层测试
│
├── .pytest_cache/                 # pytest缓存目录
├── .git/                         # Git版本控制目录
├── run.py                        # 应用启动脚本
├── Dockerfile                    # Docker构建文件
├── .gitignore                    # Git忽略文件配置
├── requirements.txt              # Python依赖包列表
├── .coverage                     # 测试覆盖率报告
├── pytest.ini                    # pytest配置文件
└── .env                         # 环境变量配置文件 