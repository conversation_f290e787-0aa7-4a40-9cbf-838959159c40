# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_previewChannelProduct.param.ProductPreviewChannelProductParam import ProductPreviewChannelProductParam


class ProductPreviewChannelProductRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductPreviewChannelProductParam()

	def getUrlPath(self, ):
		return "/product/previewChannelProduct"

	def getParams(self, ):
		return self.params



