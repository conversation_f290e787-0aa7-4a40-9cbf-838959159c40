# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_listChannelProduct.param.ProductListChannelProductParam import ProductListChannelProductParam


class ProductListChannelProductRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductListChannelProductParam()

	def getUrlPath(self, ):
		return "/product/listChannelProduct"

	def getParams(self, ):
		return self.params



