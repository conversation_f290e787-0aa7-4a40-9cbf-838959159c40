# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.iop_sellerCancleDistribute.param.IopSellerCancleDistributeParam import IopSellerCancleDistributeParam


class IopSellerCancleDistributeRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = IopSellerCancleDistributeParam()

	def getUrlPath(self, ):
		return "/iop/sellerCancleDistribute"

	def getParams(self, ):
		return self.params



