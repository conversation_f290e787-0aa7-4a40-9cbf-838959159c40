# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.warehouse_removeAddr.param.WarehouseRemoveAddrParam import WarehouseRemoveAddrParam


class WarehouseRemoveAddrRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = WarehouseRemoveAddrParam()

	def getUrlPath(self, ):
		return "/warehouse/removeAddr"

	def getParams(self, ):
		return self.params



