# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.topup_accountTemplateList.param.TopupAccountTemplateListParam import TopupAccountTemplateListParam


class TopupAccountTemplateListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = TopupAccountTemplateListParam()

	def getUrlPath(self, ):
		return "/topup/accountTemplateList"

	def getParams(self, ):
		return self.params



