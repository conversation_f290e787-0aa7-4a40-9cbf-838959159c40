from alibabacloud_dysmsapi20170525.client import Client as Dysmsapi20170525Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525 import models as dysmsapi_20170525_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient


class AliyunSmsProvider:
    def __init__(self, access_key_id: str, access_key_secret: str):
        self.client = Dysmsapi20170525Client.create_client(
            access_key_id, access_key_secret
        )

    async def send_sms(self, phone: str, code: str, template_code: str):
        """发送短信"""
        send_sms_request = dysmsapi_20170525_models.SendSmsRequest(
            phone_numbers=phone,
            sign_name="孔凤春",
            template_code=template_code,
            template_param=f'{{"code":"{code}"}}',
        )
