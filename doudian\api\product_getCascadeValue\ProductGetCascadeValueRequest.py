# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_getCascadeValue.param.ProductGetCascadeValueParam import ProductGetCascadeValueParam


class ProductGetCascadeValueRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductGetCascadeValueParam()

	def getUrlPath(self, ):
		return "/product/getCascadeValue"

	def getParams(self, ):
		return self.params



