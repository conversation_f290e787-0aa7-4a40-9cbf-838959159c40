# Project Structure

## Root Directory
- `app/` - Main application code
- `config/` - Configuration files (INI format)
- `libs/` - Shared libraries and utilities (git submodule)
- `tests/` - Test files and fixtures
- `examples/` - Usage examples and demos
- `docs/` - Project documentation
- `scripts/` - Utility scripts
- `xxl/` - XXL-Job integration

## Application Structure (`app/`)

### Core Modules
- `app/core/` - Core functionality (config, security, database, middleware)
- `app/models/` - Pydantic models and data structures
- `app/schemas/` - Request/response schemas
- `app/services/` - Business logic and external service integrations
- `app/utils/` - Utility functions and helpers
- `app/static/` - Static files

### Routing
- `app/routers/` - API route definitions organized by feature
  - `health.py` - Health check endpoints
  - `guanyi/` - GuanYi ERP integration
  - `rpa/` - RPA automation endpoints
  - `sms/` - SMS management endpoints
  - `bi/` - Business intelligence endpoints

## Key Files
- `app/main.py` - FastAPI application setup and configuration
- `run.py` - Production server startup script
- `pyproject.toml` - Project dependencies and build configuration
- `pytest.ini` - Test configuration
- `Dockerfile` - Container configuration

## Configuration Pattern
- Environment-specific configs in `config/` directory
- Settings loaded through `app.core.config` module
- Support for development, production, and test environments

## Database Collections
- MongoDB collections prefixed by service (e.g., `rpa_data.sms`, `rpa_data.sms_tasks`)
- Async database operations using Motor (MongoDB) and aiomysql (MySQL)

## Naming Conventions
- Snake_case for Python files, functions, and variables
- PascalCase for classes and Pydantic models
- Kebab-case for API endpoints where appropriate
- Descriptive module names reflecting functionality

## Import Organization
- Core modules imported first
- Third-party libraries second
- Local application imports last
- Relative imports for same-package modules