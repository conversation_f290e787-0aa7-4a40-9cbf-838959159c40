import redis.asyncio as redis
import json
from functools import wraps
from app.core.config import settings  # 假设配置在 settings 对象中

# TODO: 将 Redis 配置移至 app/core/config.py 中的 Settings 类
REDIS_HOST = getattr(settings, "REDIS_HOST", "**************")
REDIS_PORT = getattr(settings, "REDIS_PORT", 56379)
REDIS_DB = getattr(settings, "REDIS_DB", 2)
REDIS_PASSWORD = getattr(settings, "REDIS_PASSWORD", "CKL8I0LWqt7F4npd")


class RedisClient:
    _client = None

    @classmethod
    async def get_client(cls):
        if cls._client is None:
            cls._client = redis.Redis(
                host=REDIS_HOST,
                port=REDIS_PORT,
                db=REDIS_DB,
                password=REDIS_PASSWORD,
                decode_responses=True,  # 自动解码响应
            )
        return cls._client

    @classmethod
    async def set(cls, name: str, value, ex: int | None = None):
        """
        设置键值对
        :param name: 键名
        :param value: 值，将自动序列化为 JSON 字符串
        :param ex: 过期时间（秒）
        """
        client = await cls.get_client()
        await client.set(name, json.dumps(value, ensure_ascii=False), ex=ex)

    @classmethod
    async def get(cls, name: str):
        """
        获取键对应的值
        :param name: 键名
        :return: 值，将自动从 JSON 字符串反序列化，如果键不存在则返回 None
        """
        client = await cls.get_client()
        value = await client.get(name)
        return json.loads(value) if value else None

    @classmethod
    async def delete(cls, *names: str):
        """
        删除一个或多个键
        :param names: 键名列表
        """
        client = await cls.get_client()
        await client.delete(*names)

    @classmethod
    async def lock(cls, name: str, timeout: int = 10, blocking_timeout: int = 5):
        """
        获取分布式锁
        :param name: 锁名称
        :param timeout: 锁的超时时间（秒），防止死锁
        :param blocking_timeout: 获取锁的阻塞超时时间（秒）
        :return: Redis 分布式锁对象
        """
        client = await cls.get_client()
        return client.lock(name, timeout=timeout, blocking_timeout=blocking_timeout)


def cache_redis(key_prefix: str, ttl: int = 3600):
    """
    异步函数 Redis 缓存装饰器
    :param key_prefix: 缓存键前缀
    :param ttl: 缓存过期时间（秒），默认 1 小时
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 构建缓存键
            arg_str = ",".join(map(str, args))
            kwarg_str = ",".join(f"{k}={v}" for k, v in sorted(kwargs.items()))
            cache_key = f"{key_prefix}:{func.__name__}:{arg_str}:{kwarg_str}"

            # 尝试从缓存获取
            cached_result = await RedisClient.get(cache_key)
            if cached_result is not None:
                return cached_result

            # 调用原始函数
            result = await func(*args, **kwargs)

            # 将结果存入缓存
            await RedisClient.set(cache_key, result, ex=ttl)

            return result

        return wrapper

    return decorator


async def close_redis_connection():
    """
    关闭 Redis 连接（如果应用需要显式关闭）
    """
    if RedisClient._client:
        await RedisClient._client.close()
        RedisClient._client = None
