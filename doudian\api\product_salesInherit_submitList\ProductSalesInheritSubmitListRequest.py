# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_salesInherit_submitList.param.ProductSalesInheritSubmitListParam import ProductSalesInheritSubmitListParam


class ProductSalesInheritSubmitListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductSalesInheritSubmitListParam()

	def getUrlPath(self, ):
		return "/product/salesInherit/submitList"

	def getParams(self, ):
		return self.params



