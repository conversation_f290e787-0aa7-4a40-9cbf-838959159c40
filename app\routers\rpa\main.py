from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
import logging
import re

from pydantic import BaseModel, Field
from app.core.base_router import BaseRouter

from app.core.database import db
from app.schemas.response import ResponseModel
from pymongo import ReplaceOne
import dateutil.parser

router = BaseRouter()
logger: logging.Logger = logging.getLogger("uvicorn.error")


class PutData(BaseModel):
    """
    通用数据入库请求模型
    """

    key: str = Field(description="数据集合的唯一标识，将用于创建集合名称 rpa_{key}")
    primary_key: str | None = Field(
        description="主键，用于唯一标识数据，可不传", default=None
    )
    data: list[dict] | dict = Field(
        description="要存储的数据，可以是单个字典或字典列表"
    )


datetime_formats = [
    "%Y-%m-%d %H:%M:%S",
    "%Y/%m/%d %H:%M:%S",
    # iso
    "%Y-%m-%dT%H:%M:%S.%fZ",
    "%Y-%m-%dT%H:%M:%S.%f%z",
    "%Y-%m-%dT%H:%M:%S.%f%Z",
    "%Y-%m-%dT%H:%M:%S.%f%z",
]
# 正则表达式匹配 日期时间格式，支持 年月日 时分秒 和 iso 格式
datetime_pattern = re.compile(
    r"\b(?:(?:\d{2}(0[48]|[2468][048]|[13579][26])|(0[48]|[2468][048]|[13579][26])00)[-/.]0?2[-/.]29|\d{4}[-/.](?:(?:0?[1-9]|1[0-2])[-/.](?:0?[1-9]|[12][0-9]|3[01])|(?:0?[13578]|1[02])[-/.](?:29|30|31)|(?:0?[469]|11)[-/.]29|(?:0?[469]|11)[-/.]30)|(?:0?[1-9]|[12][0-9]|3[01])[-/.](?:0?[1-9]|1[0-2])[-/.]\d{4}|(?:0?[1-9]|1[0-2])[-/.](?:0?[1-9]|[12][0-9]|3[01])[-/.]\d{4})\s([01]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?\b|\b\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\.\d+)?(Z|[+-]([01][0-9]|2[0-3]):[0-5][0-9])?\b"
)


# 将字符串转换为 datetime 对象
def parse_datetime(dateStr: str):
    for format in datetime_formats:
        try:
            dt = datetime.strptime(dateStr, format)
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone(timedelta(hours=8)))
            return dt
        except:
            continue
    return dateStr


# 递归将字符串转换为 datetime 对象
def fix_datetime(obj):
    if isinstance(obj, str) and datetime_pattern.match(obj):
        return parse_datetime(obj)
    elif isinstance(obj, dict):
        for key, value in obj.items():
            obj[key] = fix_datetime(value)
    return obj


@router.post("/put", summary="通用数据入库", response_model=ResponseModel)
async def put_data(data: PutData):
    """
    通用数据入库接口

    将数据存储到MongoDB中的指定集合。集合名称会自动添加'rpa_'前缀。

    如果 data 数组内部中未指定 primary_key，则自动将外部 primary_key 设置为每个对象的 primary_key
    """
    if not data.data:
        return router.error(message="数据不能为空")
    if isinstance(data.data, dict):
        data.data = [data.data]
    if len(data.data) > 5000:
        return router.error(message="数据不能超过5000条")
    date = datetime.now().replace(
        hour=0, minute=0, second=0, microsecond=0, tzinfo=timezone(timedelta(hours=8))
    )
    operations = []
    has_primary_key = False

    for item in data.data:
        keys = item.keys()
        if len(keys) == 0:
            return router.error(message="存在空对象")
        if not item.get("primary_key") and data.primary_key:
            item["primary_key"] = data.primary_key

        if item.get("date"):
            parsed_date = dateutil.parser.parse(item["date"])
            item_date = parsed_date.replace(
                hour=0,
                minute=0,
                second=0,
                microsecond=0,
                tzinfo=timezone(timedelta(hours=8)),
            )
            item["date"] = item_date
        else:
            item["date"] = date
        item = fix_datetime(item)
        if item.get("primary_key") is not None:
            has_primary_key = True
            operations.append(
                ReplaceOne({"primary_key": item.get("primary_key")}, item, upsert=True)
            )

    key = f"rpa_{data.key}"
    collection = db.rpa_data[key]

    # 确保 primary_key 字段索引创建
    if has_primary_key or data.primary_key:
        try:
            await collection.create_index("primary_key", background=True)
            logger.info(f"为集合 {key} 创建 primary_key 索引")
        except Exception as e:
            # 索引可能已存在，记录日志但不影响数据插入
            logger.debug(f"集合 {key} primary_key 索引创建失败或已存在: {str(e)}")

    if operations:
        result = await collection.bulk_write(operations)
        return router.success(data={"updated": result.modified_count})
    else:
        result = await collection.insert_many(data.data)
        return router.success(data={"updated": len(result.inserted_ids)})


if __name__ == "__main__":
    dateStr = "2025/01/17 07:32:09"
    obj = {"date": dateStr, "sub": {"date": dateStr}}
    print(fix_datetime(obj))
