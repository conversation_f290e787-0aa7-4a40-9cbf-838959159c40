#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接测试脚本

用于测试数据库连接池配置是否正常工作
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.providers.xiaohongshu import (
    check_database_connection,
    log_database_stats,
)
from app.core.logger import logger


async def test_database_connection():
    """测试数据库连接"""
    logger.info("开始数据库连接测试")

    try:
        # 检查连接状态
        result = await check_database_connection()

        if result["success"]:
            logger.info("✅ 数据库连接测试成功")
            logger.info(f"连接池状态: {result['pool_status']}")
        else:
            logger.error(f"❌ 数据库连接测试失败: {result['error']}")

        # 记录详细的连接池统计信息
        await log_database_stats()

        return result["success"]

    except Exception as e:
        logger.error(f"❌ 数据库连接测试异常: {str(e)}")
        return False


async def test_concurrent_connections():
    """测试并发连接"""
    logger.info("开始并发连接测试")

    try:
        # 创建多个并发连接任务
        tasks = []
        for i in range(5):
            task = check_database_connection()
            tasks.append(task)

        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        success_count = 0
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"并发连接 {i+1} 失败: {str(result)}")
            elif isinstance(result, dict) and result.get("success"):
                success_count += 1
                logger.info(f"✅ 并发连接 {i+1} 成功")
            else:
                logger.error(
                    f"❌ 并发连接 {i+1} 失败: {result.get('error', 'unknown')}"
                )

        logger.info(f"并发连接测试完成: {success_count}/5 成功")
        await log_database_stats()

        return success_count == 5

    except Exception as e:
        logger.error(f"❌ 并发连接测试异常: {str(e)}")
        return False


async def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("数据库连接修复验证测试")
    logger.info("=" * 50)

    # 测试基本连接
    basic_test_passed = await test_database_connection()

    if basic_test_passed:
        logger.info("\n" + "=" * 30)
        logger.info("基本连接测试通过，开始并发测试")
        logger.info("=" * 30)

        # 测试并发连接
        concurrent_test_passed = await test_concurrent_connections()

        if concurrent_test_passed:
            logger.info("\n🎉 所有测试通过！数据库连接配置正常")
        else:
            logger.error("\n⚠️ 并发连接测试失败，请检查连接池配置")
    else:
        logger.error("\n❌ 基本连接测试失败，请检查数据库配置")

    logger.info("=" * 50)
    logger.info("测试完成")
    logger.info("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())
