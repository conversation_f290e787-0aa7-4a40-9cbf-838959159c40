# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.address_delAftersaleAddress.param.AddressDelAftersaleAddressParam import AddressDelAftersaleAddressParam


class AddressDelAftersaleAddressRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AddressDelAftersaleAddressParam()

	def getUrlPath(self, ):
		return "/address/delAftersaleAddress"

	def getParams(self, ):
		return self.params



