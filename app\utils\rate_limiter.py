import time
import threading
from typing import Optional


class RateLimiter:
    """
    QPS 限制器，使用令牌桶算法实现
    """

    def __init__(self, qps: float):
        """
        初始化 QPS 限制器

        Args:
            qps: 每秒允许的最大请求数
        """
        self.qps = qps
        self.tokens = qps  # 初始令牌数等于 QPS
        self.last_update_time = time.time()
        self.lock = threading.Lock()

    def acquire(self, tokens: float = 1.0) -> bool:
        """
        尝试获取指定数量的令牌

        Args:
            tokens: 需要获取的令牌数量，默认为 1

        Returns:
            bool: 是否成功获取令牌
        """
        with self.lock:
            now = time.time()
            # 计算从上次更新到现在应该新增的令牌数
            time_passed = now - self.last_update_time
            new_tokens = time_passed * self.qps

            # 更新令牌数和时间
            self.tokens = min(self.qps, self.tokens + new_tokens)
            self.last_update_time = now

            # 如果令牌数不足，返回 False
            if self.tokens < tokens:
                return False

            # 获取令牌
            self.tokens -= tokens
            return True

    def wait(self, tokens: float = 1.0, timeout: Optional[float] = None) -> bool:
        """
        等待直到获取到指定数量的令牌

        Args:
            tokens: 需要获取的令牌数量，默认为 1
            timeout: 超时时间（秒），None 表示无限等待

        Returns:
            bool: 是否成功获取令牌
        """
        start_time = time.time()
        while True:
            if self.acquire(tokens):
                return True

            # 检查是否超时
            if timeout is not None and time.time() - start_time > timeout:
                return False

            # 短暂休眠，避免过度消耗 CPU
            time.sleep(0.01)


class RateLimiterDecorator:
    """
    QPS 限制器装饰器
    """

    def __init__(self, qps: float):
        """
        初始化 QPS 限制器装饰器

        Args:
            qps: 每秒允许的最大请求数
        """
        self.rate_limiter = RateLimiter(qps)

    def __call__(self, func):
        """
        装饰器调用方法

        Args:
            func: 被装饰的函数

        Returns:
            装饰后的函数
        """

        def wrapper(*args, **kwargs):
            self.rate_limiter.wait()
            return func(*args, **kwargs)

        return wrapper
