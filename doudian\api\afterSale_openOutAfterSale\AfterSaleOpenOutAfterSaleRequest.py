# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.afterSale_openOutAfterSale.param.AfterSaleOpenOutAfterSaleParam import AfterSaleOpenOutAfterSaleParam


class AfterSaleOpenOutAfterSaleRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AfterSaleOpenOutAfterSaleParam()

	def getUrlPath(self, ):
		return "/afterSale/openOutAfterSale"

	def getParams(self, ):
		return self.params



