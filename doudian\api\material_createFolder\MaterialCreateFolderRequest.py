# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_createFolder.param.MaterialCreateFolderParam import MaterialCreateFolderParam


class MaterialCreateFolderRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialCreateFolderParam()

	def getUrlPath(self, ):
		return "/material/createFolder"

	def getParams(self, ):
		return self.params



