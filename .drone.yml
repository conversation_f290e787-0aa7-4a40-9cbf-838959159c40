kind: pipeline
type: docker
name: default

clone:
  recursive: true

steps:
  - name: submodules
    image: alpine/git
    commands:
      - git submodule update --init --recursive

  - name: docker
    image: plugins/docker
    settings:
      registry: crpi-rva6wk7sol3hb2na.cn-hangzhou.personal.cr.aliyuncs.com
      username: aliyun1349305028
      password: kfc123456
      repo: crpi-rva6wk7sol3hb2na.cn-hangzhou.personal.cr.aliyuncs.com/kophenix/datapilot
      tags:
        - latest
    when:
      branch:
        - main
        - master

  - name: SSH
    image: appleboy/drone-ssh
    settings:
      host: **************
      username: root
      password:
        from_secret: SSH_PASSWD
      port: 22
      command_timeout: 1m
      script:
        - cd /data/docker/app/kfc_oa_datapilot
        - docker compose pull
        - docker compose up -d
    when:
      branch:
        - main
        - master

  - name: notification
    image: lddsb/drone-dingtalk-message
    type: markdown
    settings:
      token: fad25a54d80f879a1e67f3b35f8ce1f4b59ca0b93263b909ae71b4d38368231e
      type: markdown
      secret: SECd494a0ac9f486b9003b4858fc06846097e734db968d5e32e45b83ec10db40bc1
      message_color: true
    when:
      status:
        - success
        - failure
