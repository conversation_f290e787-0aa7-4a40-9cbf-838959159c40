# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_waybillApply.param.LogisticsWaybillApplyParam import LogisticsWaybillApplyParam


class LogisticsWaybillApplyRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsWaybillApplyParam()

	def getUrlPath(self, ):
		return "/logistics/waybillApply"

	def getParams(self, ):
		return self.params



