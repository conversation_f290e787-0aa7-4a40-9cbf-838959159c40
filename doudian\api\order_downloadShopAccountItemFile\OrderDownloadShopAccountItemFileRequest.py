# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_downloadShopAccountItemFile.param.OrderDownloadShopAccountItemFileParam import OrderDownloadShopAccountItemFileParam


class OrderDownloadShopAccountItemFileRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderDownloadShopAccountItemFileParam()

	def getUrlPath(self, ):
		return "/order/downloadShopAccountItemFile"

	def getParams(self, ):
		return self.params



