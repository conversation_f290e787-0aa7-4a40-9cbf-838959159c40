# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.address_delAftersaleStrategy.param.AddressDelAftersaleStrategyParam import AddressDelAftersaleStrategyParam


class AddressDelAftersaleStrategyRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AddressDelAftersaleStrategyParam()

	def getUrlPath(self, ):
		return "/address/delAftersaleStrategy"

	def getParams(self, ):
		return self.params



