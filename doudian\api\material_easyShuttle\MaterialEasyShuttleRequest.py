# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_easyShuttle.param.MaterialEasyShuttleParam import MaterialEasyShuttleParam


class MaterialEasyShuttleRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialEasyShuttleParam()

	def getUrlPath(self, ):
		return "/material/easyShuttle"

	def getParams(self, ):
		return self.params



