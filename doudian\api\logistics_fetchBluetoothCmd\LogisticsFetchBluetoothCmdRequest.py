# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_fetchBluetoothCmd.param.LogisticsFetchBluetoothCmdParam import LogisticsFetchBluetoothCmdParam


class LogisticsFetchBluetoothCmdRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsFetchBluetoothCmdParam()

	def getUrlPath(self, ):
		return "/logistics/fetchBluetoothCmd"

	def getParams(self, ):
		return self.params



