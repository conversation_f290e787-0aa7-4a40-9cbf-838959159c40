from sqlalchemy import (
    Column,
    String,
    DateTime,
    Integer,
    Text,
    SmallInteger,
    Numeric,
    BigInteger,
    Boolean,
    UniqueConstraint,
    Index,
)
from sqlalchemy.orm import Mapped
from datetime import datetime
from .base import BaseModel


class BrandXhsJgCreativityDataReport(BaseModel):
    """小红书聚光创意报表"""

    __tablename__ = "brand_xhs_jg_creativity_data_report"

    id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    api: Mapped[bool] = Column(
        Boolean, nullable=False, default=False, comment="api标识"
    )
    creativity_id: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="创意id"
    )
    note_id: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="笔记ID"
    )
    date_time: Mapped[int] = Column(
        BigInteger, nullable=False, default=0, comment="时间"
    )
    date_year: Mapped[str] = Column(String(4), nullable=False, default="", comment="年")
    date_month: Mapped[str] = Column(
        String(2), nullable=False, default="", comment="月"
    )
    date_day: Mapped[str] = Column(String(2), nullable=False, default="", comment="日")
    fee: Mapped[float] = Column(
        Numeric(11, 2), nullable=False, default=0.00, comment="消费"
    )
    impression: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="展现量"
    )
    click: Mapped[int] = Column(Integer, nullable=False, default=0, comment="点击量")
    ctr: Mapped[str] = Column(String(32), nullable=False, default="", comment="点击率")
    acp: Mapped[float] = Column(
        Numeric(11, 2), nullable=False, default=0.00, comment="平均点击成本"
    )
    cpm: Mapped[float] = Column(
        Numeric(11, 2), nullable=False, default=0.00, comment="平均千次展示费用"
    )
    like: Mapped[int] = Column(Integer, nullable=False, default=0, comment="点赞")
    comment: Mapped[int] = Column(Integer, nullable=False, default=0, comment="评论")
    collect: Mapped[int] = Column(Integer, nullable=False, default=0, comment="收藏")
    follow: Mapped[int] = Column(Integer, nullable=False, default=0, comment="关注")
    share: Mapped[int] = Column(Integer, nullable=False, default=0, comment="分享")
    interaction: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="互动量"
    )
    cpi: Mapped[float] = Column(
        Numeric(11, 2), nullable=False, default=0.00, comment="平均互动成本"
    )
    action_button_click: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="行动按钮点击量"
    )
    action_button_ctr: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="行动按钮点击率"
    )
    screenshot: Mapped[int] = Column(Integer, nullable=False, default=0, comment="截图")
    pic_save: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="保存图片"
    )
    reserve_pv: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="预告组件点击"
    )
    search_cmt_click: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="搜索组件点击量"
    )
    search_cmt_click_cvr: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="搜索组件点击转化率"
    )
    search_cmt_after_read_avg: Mapped[float] = Column(
        Numeric(11, 2), nullable=False, default=0.00, comment="平均搜后阅读笔记篇数"
    )
    search_cmt_after_read: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="搜后阅读量"
    )

    __table_args__ = (
        Index("date_time", "date_time"),
        Index("idx_creativity_id", "creativity_id"),
        Index("idx_date_creativity", "date_time", "creativity_id"),
        Index("index_note", "note_id"),
        Index("index_one", "date_time", "creativity_id", "update_time"),
        Index("note_id", "note_id", "date_time"),
        UniqueConstraint(
            "creativity_id", "note_id", "date_time", name="uk_creativity_note_date"
        ),
        {
            "mysql_engine": "MyISAM",
            "mysql_charset": "utf8mb4",
            "mysql_collate": "utf8mb4_unicode_ci",
        },
    )


class BrandXhsJgCreativity(BaseModel):
    """小红书聚光创意表"""

    __tablename__ = "brand_xhs_jg_creativity"

    id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    catch_time: Mapped[int] = Column(
        BigInteger, nullable=False, default=0, comment="抓取时间"
    )
    last_catch_error_time: Mapped[int] = Column(
        BigInteger, nullable=False, default=0, comment="上一次抓取出错的时间"
    )
    catch_data: Mapped[str] = Column(
        String(1024), nullable=False, default="[]", comment="抓取所需数据包"
    )
    campaign_id: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="计划id"
    )
    campaign_name: Mapped[str] = Column(
        String(64), nullable=False, default="", comment="计划名称"
    )
    marketing_target: Mapped[int] = Column(
        SmallInteger, nullable=False, default=0, comment="营销诉求"
    )
    placement: Mapped[int] = Column(
        SmallInteger, nullable=False, default=0, comment="广告类型"
    )
    unit_id: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="单元id"
    )
    unit_name: Mapped[str] = Column(
        String(64), nullable=False, default="", comment="单元名称"
    )
    creativity_id: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="创意id"
    )
    creativity_name: Mapped[str] = Column(
        String(64), nullable=False, default="", comment="创意名称"
    )
    note_id: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="笔记ID"
    )
    note_title: Mapped[str] = Column(
        String(255), nullable=False, default="", comment="笔记标题"
    )
    brand_resource_id: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="关联资源ID"
    )

    __table_args__ = (
        Index("index_one", "creativity_id"),
        {
            "mysql_engine": "MyISAM",
            "mysql_charset": "utf8mb4",
            "mysql_collate": "utf8mb4_unicode_ci",
        },
    )


class BrandXhsJgOverallDataReport(BaseModel):
    """小红书聚光汇总报表"""

    __tablename__ = "brand_xhs_jg_overall_data_report"

    id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    api: Mapped[bool] = Column(
        Boolean, nullable=False, default=False, comment="api标识"
    )
    origin_data: Mapped[str] = Column(Text, nullable=True, comment="源数据")
    date_time: Mapped[int] = Column(
        BigInteger, nullable=False, default=0, comment="时间"
    )
    date_year: Mapped[str] = Column(String(4), nullable=False, default="", comment="年")
    date_month: Mapped[str] = Column(
        String(2), nullable=False, default="", comment="月"
    )
    date_day: Mapped[str] = Column(String(2), nullable=False, default="", comment="日")
    fee: Mapped[str] = Column(String(32), nullable=False, default="", comment="总消费")
    fee_chain_ratio: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="总消费环比"
    )
    impression: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="总展现"
    )
    impression_chain_ratio: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="总展现环比"
    )
    click: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="总点击"
    )
    click_chain_ratio: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="总点击环比"
    )
    ctr: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="总点击率"
    )
    ctr_chain_ratio: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="总点击率环比"
    )
    acp: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="平均点击价格"
    )
    acp_chain_ratio: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="平均点击价格环比"
    )

    __table_args__ = (
        {
            "mysql_engine": "MyISAM",
            "mysql_charset": "utf8mb4",
            "mysql_collate": "utf8mb4_unicode_ci",
        },
    )


class BrandXhsNote(BaseModel):
    """小红书笔记表"""

    __tablename__ = "brand_xhs_note"

    id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    catch_time: Mapped[int] = Column(
        BigInteger, nullable=False, default=0, comment="抓取时间"
    )
    last_catch_error_time: Mapped[int] = Column(
        BigInteger, nullable=False, default=0, comment="上一次抓取出错的时间"
    )
    catch_lock_time: Mapped[int] = Column(
        BigInteger, nullable=False, default=0, comment="抓取锁定时间"
    )
    xhs_user_id: Mapped[str] = Column(
        String(32), nullable=False, default="0", comment="小红书用户ID"
    )
    note_id: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="笔记ID"
    )
    note_coverimg: Mapped[str] = Column(
        String(255), nullable=False, default="", comment="笔记封面图"
    )
    note_link: Mapped[str] = Column(
        String(255), nullable=False, default="", comment="笔记链接"
    )
    note_title: Mapped[str] = Column(
        String(1000), nullable=False, default="", comment="笔记标题"
    )
    note_publish_time: Mapped[int] = Column(
        BigInteger, nullable=False, default=0, comment="笔记发布时间"
    )
    note_count_exposure: Mapped[int] = Column(
        BigInteger, nullable=False, default=0, comment="笔记曝光量"
    )
    note_count_view: Mapped[int] = Column(
        Integer, nullable=True, default=0, comment="笔记阅读量"
    )
    note_count_like: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="笔记点赞数"
    )
    note_count_comment: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="笔记评论数"
    )
    note_count_collect: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="笔记收藏数"
    )
    note_count_share: Mapped[int] = Column(
        Integer, nullable=False, default=0, comment="笔记分享数"
    )
    cost_write: Mapped[float] = Column(
        Numeric(10, 2), nullable=False, default=0.00, comment="稿费"
    )
    cost_flow: Mapped[float] = Column(
        Numeric(10, 2), nullable=False, default=0.00, comment="流量推广费"
    )
    score: Mapped[int] = Column(Integer, nullable=False, default=0, comment="评分")
    localization: Mapped[str] = Column(
        String(255), nullable=False, default="", comment="定位"
    )
    cooperation_product: Mapped[str] = Column(
        String(255), nullable=False, default="", comment="合作产品"
    )
    cooperation_type: Mapped[str] = Column(
        String(255), nullable=False, default="", comment="合作方式"
    )
    follower: Mapped[str] = Column(
        String(32), nullable=False, default="", comment="跟进人"
    )
    is_business: Mapped[bool] = Column(
        Boolean, nullable=True, default=False, comment="是否商业笔记"
    )
    is_note: Mapped[bool] = Column(
        Boolean, nullable=False, default=False, comment="是否图文笔记"
    )
    is_video: Mapped[bool] = Column(
        Boolean, nullable=False, default=False, comment="是否视频笔记"
    )
    business_type: Mapped[str] = Column(
        String(10), nullable=True, default="N", comment="业务类型（N, S）"
    )
    pay_type: Mapped[str] = Column(String(10), nullable=True, comment="付款类型")
    note_xsec_token: Mapped[str] = Column(
        String(255), nullable=True, comment="笔记XsecToken"
    )
    brand_id: Mapped[int] = Column(Integer, nullable=False, default=1, comment="品牌ID")

    __table_args__ = (
        Index("brand_xhs_note_brand_id_index", "brand_id"),
        Index("brand_xhs_note_follower_index", "follower"),
        Index("index_catch_time", "catch_time"),
        Index("index_note_id", "note_id"),
        Index("index_publish_time", "note_publish_time"),
        Index("index_xhs_user_id", "xhs_user_id"),
        {
            "mysql_charset": "utf8mb4",
            "mysql_collate": "utf8mb4_unicode_ci",
        },
    )
