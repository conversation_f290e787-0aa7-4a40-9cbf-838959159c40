# 小红书创意基础信息同步功能文档

## 概述

本功能实现了从小红书聚光平台 API 获取创意基础信息，并自动同步到本地数据库 `brand_xhs_jg_creativity` 表中。该表存储创意的基本信息，包括创意 ID、笔记 ID、计划信息等，为后续的数据分析和报表生成提供基础数据。

## 功能特性

- ✅ **自动获取访问令牌**：自动从数据库获取并刷新小红书 API 访问令牌
- ✅ **分层数据获取**：先获取广告计划，再获取每个计划下的创意列表
- ✅ **数据去重**：基于创意 ID 的唯一性约束，避免重复数据
- ✅ **增量更新**：已存在的记录会被更新，新记录会被插入
- ✅ **错误处理**：完善的错误处理和日志记录
- ✅ **事务支持**：使用数据库事务确保数据一致性
- ✅ **多广告主支持**：支持同时处理多个授权广告主的数据
- ✅ **命令行工具**：提供便捷的命令行同步工具

## 数据库表结构

### 表名：`brand_xhs_jg_creativity`

| 字段名                  | 类型         | 说明                 | 约束        |
| ----------------------- | ------------ | -------------------- | ----------- |
| `id`                    | Integer      | 主键                 | PRIMARY KEY |
| `catch_time`            | BigInteger   | 抓取时间             | NOT NULL    |
| `last_catch_error_time` | BigInteger   | 上一次抓取出错的时间 | NOT NULL    |
| `catch_data`            | String(1024) | 抓取所需数据包       | NOT NULL    |
| `campaign_id`           | String(32)   | 计划 ID              | NOT NULL    |
| `campaign_name`         | String(64)   | 计划名称             | NOT NULL    |
| `marketing_target`      | SmallInteger | 营销诉求             | NOT NULL    |
| `placement`             | SmallInteger | 广告类型             | NOT NULL    |
| `unit_id`               | String(32)   | 单元 ID              | NOT NULL    |
| `unit_name`             | String(64)   | 单元名称             | NOT NULL    |
| `creativity_id`         | String(32)   | 创意 ID              | NOT NULL    |
| `creativity_name`       | String(64)   | 创意名称             | NOT NULL    |
| `note_id`               | String(32)   | 笔记 ID              | NOT NULL    |
| `note_title`            | String(255)  | 笔记标题             | NOT NULL    |
| `brand_resource_id`     | Integer      | 关联资源 ID          | NOT NULL    |
| `create_time`           | BigInteger   | 创建时间             | NOT NULL    |
| `update_time`           | BigInteger   | 更新时间             | NOT NULL    |
| `delete_time`           | BigInteger   | 删除时间             | -           |

### 唯一性约束

表使用 `creativity_id` 字段作为唯一标识，确保每个创意只有一条记录。

### 索引

- `index_one` (`creativity_id`) - 主要查询索引

## 使用方法

### 1. 命令行工具

#### 基本用法

```bash
# 同步所有授权广告主的创意信息
python sync_xiaohongshu_creativity.py

# 同步指定广告主的创意信息
python sync_xiaohongshu_creativity.py --advertiser-ids 123456,789012

# 详细模式同步
python sync_xiaohongshu_creativity.py --verbose

# 测试模式
python sync_xiaohongshu_creativity.py --test --advertiser-ids 123456
```

#### 命令行参数

| 参数                 | 说明                             | 示例                       |
| -------------------- | -------------------------------- | -------------------------- |
| `--advertiser-ids`   | 指定广告主 ID 列表（用逗号分隔） | `--advertiser-ids 123,456` |
| `--with-note-titles` | 尝试获取笔记标题（如果支持的话） | `--with-note-titles`       |
| `--test`             | 运行测试模式                     | `--test`                   |
| `--verbose, -v`      | 详细输出模式                     | `--verbose`                |
| `--help, -h`         | 显示帮助信息                     | `--help`                   |

### 2. 编程接口

#### 基础同步功能

```python
from app.services.providers.xiaohongshu import sync_xiaohongshu_creativity_info

# 同步所有授权广告主的创意信息
result = await sync_xiaohongshu_creativity_info()

# 同步指定广告主的创意信息
result = await sync_xiaohongshu_creativity_info(advertiser_ids=[123456, 789012])

print(f"同步结果: {result}")
```

#### 带笔记标题的同步

```python
from app.services.providers.xiaohongshu import sync_xiaohongshu_creativity_info_with_note_titles

# 同步创意信息并尝试获取笔记标题
result = await sync_xiaohongshu_creativity_info_with_note_titles()

print(f"同步结果: {result}")
```

### 3. 返回结果格式

```json
{
  "success": true,
  "total_processed": 150,
  "total_inserted": 120,
  "total_updated": 30,
  "errors": [],
  "advertisers_count": 3
}
```

#### 字段说明

- `success`: 是否成功
- `total_processed`: 总处理数量
- `total_inserted`: 新增记录数量
- `total_updated`: 更新记录数量
- `errors`: 错误列表
- `advertisers_count`: 处理的广告主数量

## 数据流程

### 1. 数据获取流程

```
1. 获取访问令牌
   ↓
2. 获取授权广告主列表
   ↓
3. 遍历每个广告主
   ↓
4. 获取广告主的所有计划
   ↓
5. 遍历每个计划
   ↓
6. 获取计划下的所有创意
   ↓
7. 处理每个创意数据
   ↓
8. 保存到数据库
```

### 2. 数据处理逻辑

- **新增**：如果创意 ID 不存在，插入新记录
- **更新**：如果创意 ID 已存在，更新除创建时间外的所有字段
- **去重**：基于 `creativity_id` 字段确保唯一性

### 3. 错误处理

- API 调用失败：记录错误并继续处理其他数据
- 数据格式错误：跳过无效数据并记录警告
- 数据库错误：回滚事务并返回错误信息

## 配置要求

### 1. 环境依赖

- Python 3.8+
- SQLAlchemy (异步支持)
- 小红书 API 访问权限

### 2. 数据库配置

确保数据库连接配置正确：

```python
# app/core/config.py
OA = {
    'HOST': 'your_db_host',
    'PORT': 3306,
    'USER': 'your_db_user',
    'PASSWORD': 'your_db_password',
    'DATABASE': 'your_db_name'
}
```

### 3. API 配置

确保小红书 API 配置正确：

```python
# app/core/config.py
XHS_APP_ID = "your_app_id"
XHS_APP_SECRET = "your_app_secret"
```

## 最佳实践

### 1. 运行时机

- **建议时间**：非业务高峰期（如凌晨）
- **频率**：每日一次或根据业务需要
- **监控**：设置日志监控和告警

### 2. 性能优化

- **分页处理**：避免一次性加载大量数据
- **批量操作**：使用数据库事务提高效率
- **错误重试**：对临时性错误进行重试

### 3. 数据质量

- **数据验证**：确保必要字段不为空
- **格式检查**：验证数据格式的正确性
- **一致性检查**：定期检查数据一致性

## 监控和维护

### 1. 日志监控

```python
from app.core.logger import logger

# 查看同步日志
logger.info("开始同步小红书创意基础信息")
logger.error("同步过程中发生错误")
```

### 2. 数据验证

```sql
-- 检查数据完整性
SELECT COUNT(*) FROM brand_xhs_jg_creativity WHERE creativity_id = '';

-- 检查最近同步的数据
SELECT * FROM brand_xhs_jg_creativity
WHERE catch_time > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY))
ORDER BY catch_time DESC;
```

### 3. 性能监控

- 监控同步耗时
- 监控 API 调用频率
- 监控数据库性能

## 故障排除

### 常见问题

1. **访问令牌失效**

   - 检查数据库中的 `access_tokens` 表
   - 确认刷新令牌是否有效
   - 重新获取授权

2. **API 调用失败**

   - 检查网络连接
   - 确认 API 接口是否正常
   - 查看错误日志

3. **数据库连接错误**

   - 检查数据库配置
   - 确认数据库服务是否正常

4. **数据重复或缺失**
   - 检查唯一性约束
   - 验证数据处理逻辑
   - 查看错误日志

### 调试方法

```bash
# 启用详细日志
python sync_xiaohongshu_creativity.py --verbose

# 测试模式运行
python sync_xiaohongshu_creativity.py --test --advertiser-ids 123456

# 检查特定广告主
python sync_xiaohongshu_creativity.py --advertiser-ids 123456 --verbose
```

## 扩展功能

### 1. 添加新字段

如需同步更多字段，可以修改数据处理函数：

```python
# 在 _process_creativity_info_item 函数中添加
data = {
    # 现有字段...
    "new_field": safe_str(creativity.get("new_field")),
}
```

### 2. 自定义过滤条件

```python
# 可以在获取创意列表时添加过滤条件
params = GetCreativitySearchParams(
    advertiser_id=advertiser_id,
    campaign_id=campaign_id,
    status=2,  # 只获取未删除的创意
    # 可以添加更多过滤条件
)
```

### 3. 数据导出

```python
# 导出数据到CSV
import pandas as pd
from app.core.oa_database import get_session, BrandXhsJgCreativity

async with get_session() as session:
    stmt = select(BrandXhsJgCreativity)
    result = await session.execute(stmt)
    records = result.scalars().all()

    data = [record.to_dict() for record in records]
    df = pd.DataFrame(data)
    df.to_csv("xiaohongshu_creativity_info.csv", index=False)
```

## 相关文档

- [小红书创意数据报表同步文档](xiaohongshu_sync_usage.md)
- [小红书 API 文档](xiaohongshu_creative_report_api.md)
- [数据库设计文档](../app/core/oa_database.py)

## 联系支持

如有问题或建议，请联系开发团队或查看相关文档。
