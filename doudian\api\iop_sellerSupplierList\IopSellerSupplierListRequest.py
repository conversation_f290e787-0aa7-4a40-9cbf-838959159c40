# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.iop_sellerSupplierList.param.IopSellerSupplierListParam import IopSellerSupplierListParam


class IopSellerSupplierListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = IopSellerSupplierListParam()

	def getUrlPath(self, ):
		return "/iop/sellerSupplierList"

	def getParams(self, ):
		return self.params



