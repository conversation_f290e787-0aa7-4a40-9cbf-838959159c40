# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.topup_result.param.TopupResultParam import TopupResultParam


class TopupResultRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = TopupResultParam()

	def getUrlPath(self, ):
		return "/topup/result"

	def getParams(self, ):
		return self.params



