# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.address_updateAftersaleStrategy.param.AddressUpdateAftersaleStrategyParam import AddressUpdateAftersaleStrategyParam


class AddressUpdateAftersaleStrategyRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AddressUpdateAftersaleStrategyParam()

	def getUrlPath(self, ):
		return "/address/updateAftersaleStrategy"

	def getParams(self, ):
		return self.params



