# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_logisticsAddMultiPack.param.OrderLogisticsAddMultiPackParam import OrderLogisticsAddMultiPackParam


class OrderLogisticsAddMultiPackRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderLogisticsAddMultiPackParam()

	def getUrlPath(self, ):
		return "/order/logisticsAddMultiPack"

	def getParams(self, ):
		return self.params



