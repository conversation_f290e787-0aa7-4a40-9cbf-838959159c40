# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_getDesignTemplateList.param.LogisticsGetDesignTemplateListParam import LogisticsGetDesignTemplateListParam


class LogisticsGetDesignTemplateListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsGetDesignTemplateListParam()

	def getUrlPath(self, ):
		return "/logistics/getDesignTemplateList"

	def getParams(self, ):
		return self.params



