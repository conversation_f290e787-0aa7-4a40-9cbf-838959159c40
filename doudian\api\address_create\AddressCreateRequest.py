# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.address_create.param.AddressCreateParam import AddressCreateParam


class AddressCreateRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = AddressCreateParam()

	def getUrlPath(self, ):
		return "/address/create"

	def getParams(self, ):
		return self.params



