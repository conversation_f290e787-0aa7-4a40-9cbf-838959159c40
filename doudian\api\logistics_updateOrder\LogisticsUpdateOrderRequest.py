# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_updateOrder.param.LogisticsUpdateOrderParam import LogisticsUpdateOrderParam


class LogisticsUpdateOrderRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsUpdateOrderParam()

	def getUrlPath(self, ):
		return "/logistics/updateOrder"

	def getParams(self, ):
		return self.params



