import os
import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.core.config import Settings, get_settings
from app.services.providers.guanyi import GuanyiService


@pytest.fixture(autouse=True)
def set_test_env():
    """自动设置测试环境"""
    os.environ["APP_ENV"] = "Development"
    yield
    # 测试完成后恢复环境
    if "APP_ENV" in os.environ:
        del os.environ["APP_ENV"]


@pytest.fixture
def test_settings() -> Settings:
    """测试配置"""
    return get_settings()


@pytest.fixture
def test_client(set_test_env):
    """测试客户端"""
    return TestClient(app)


@pytest.fixture
def guanyi_service(test_settings):
    """管易服务实例"""
    service = GuanyiService(
        appkey=test_settings.GUANYI_APPKEY,
        sessionkey=test_settings.GUANYI_SESSIONKEY,
        secret=test_settings.GUANYI_SECRET,
        sandbox=test_settings.GUANYI_SANDBOX,
    )
    return service
