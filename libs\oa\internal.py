import json
import requests
from urllib.parse import urljoin

api_key = "8nZPjpHwZaCYzHQZxwjcWGdH2K2n3V6c"

session = requests.Session()
default_headers = {
    "API-Key": api_key,
    "Content-Type": "application/json",
}
session.headers.update(default_headers)
host = "https://oa.kfch.cn"


def get_url(path: str):
    return urljoin(host, path)


async def get_performance(date: str):
    """
    获取绩效数据

    url: https://oa.kfch.cn/api-internal/performance/index

    params:
        date: 日期, 格式: YYYY-MM-DD 或 YYYY-MM
    """
    url = get_url("/api-internal/performance/index")
    response = session.post(url, json={"select_date": date})
    return response.json()


async def main():
    res = await get_performance("2025-03")
    # write to file
    with open("performance.json", "w", encoding="utf-8") as f:
        json.dump(res, f, ensure_ascii=False, indent=4)


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
