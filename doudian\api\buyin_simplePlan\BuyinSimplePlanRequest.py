# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.buyin_simplePlan.param.BuyinSimplePlanParam import BuyinSimplePlanParam


class BuyinSimplePlanRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = BuyinSimplePlanParam()

	def getUrlPath(self, ):
		return "/buyin/simplePlan"

	def getParams(self, ):
		return self.params



