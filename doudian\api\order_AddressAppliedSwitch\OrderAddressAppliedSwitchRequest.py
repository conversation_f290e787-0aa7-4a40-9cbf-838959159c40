# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_AddressAppliedSwitch.param.OrderAddressAppliedSwitchParam import OrderAddressAppliedSwitchParam


class OrderAddressAppliedSwitchRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderAddressAppliedSwitchParam()

	def getUrlPath(self, ):
		return "/order/AddressAppliedSwitch"

	def getParams(self, ):
		return self.params



