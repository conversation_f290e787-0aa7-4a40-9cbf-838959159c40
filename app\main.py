from fastapi import APIRouter, FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from contextlib import asynccontextmanager
from app.core.security import verify_api_key, verify_api_key_or_jwt
from app.core.middleware import CustomJ<PERSON><PERSON>esponse
from app.core.exceptions import http_exception_handler, global_exception_handler
from app.core.openapi import custom_openapi, get_swagger_ui_html


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 在这里添加启动时的初始化代码
    yield
    # 在这里添加关闭时的清理代码


# 创建主应用
app = FastAPI(
    title="DATAPILOT",
    description="数据管理平台API",
    version="1.0.0",
    lifespan=lifespan,
    default_response_class=CustomJSONResponse,  # 设置默认响应类
    dependencies=[Depends(verify_api_key_or_jwt)],  # 添加全局依赖
    swagger_ui_parameters={
        "persistAuthorization": True,  # 保持认证状态，即使刷新页面
        "tryItOutEnabled": True,  # 默认启用"Try it out"功能
        "displayRequestDuration": True,  # 显示请求持续时间
        "filter": True,  # 启用过滤功能
        "deepLinking": True,  # 启用深度链接
        "syntaxHighlight": {"activate": True, "theme": "agate"},
        "operationsSorter": "alpha",  # 按字母顺序排序操作
        "tagsSorter": "alpha",  # 按字母顺序排序标签
        "docExpansion": "list",  # 默认展开API列表
        "showExtensions": True,  # 显示扩展信息
        "showCommonExtensions": True,  # 显示通用扩展信息
    },
    docs_url=None,  # 禁用默认的docs路由，我们将自定义它
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有请求头
)

# 设置自定义 OpenAPI 配置
app.openapi = lambda: custom_openapi(app)


# 添加自定义的Swagger UI路由，不需要认证
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - API文档",
        swagger_ui_parameters=app.swagger_ui_parameters,
    )


# 确保OpenAPI JSON端点不需要认证
@app.get("/openapi.json", include_in_schema=False)
async def get_openapi_json():
    return app.openapi()


# 注册异常处理器
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(Exception, global_exception_handler)

# 导入路由模块
from app.routers import health, guanyi
from app.routers.rpa import main, pdd
from app.routers.sms import main as sms_main

# 注册路由模块
app.include_router(health.router, tags=["health"])
guanyi_group = APIRouter(prefix="/guanyi", tags=["管易"])
guanyi_group.include_router(guanyi.router, prefix="")
app.include_router(guanyi_group)

# 注册RPA路由模块
rpa_group = APIRouter(prefix="/rpa", tags=["RPA"])
rpa_group.include_router(pdd.router, prefix="/pdd")
rpa_group.include_router(main.router, prefix="")
app.include_router(rpa_group)

# 注册短信路由模块
sms_group = APIRouter(prefix="/sms", tags=["SMS"])
sms_group.include_router(sms_main.router)
app.include_router(sms_group)


from app.routers.bi import tv

# 注册BI路由模块
bi_group = APIRouter(prefix="/bi", tags=["BI"])
bi_group.include_router(tv.router, prefix="/tv")
app.include_router(bi_group)
