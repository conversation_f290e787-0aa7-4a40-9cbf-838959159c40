# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.brand_convert.param.BrandConvertParam import BrandConvertParam


class BrandConvertRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = BrandConvertParam()

	def getUrlPath(self, ):
		return "/brand/convert"

	def getParams(self, ):
		return self.params



