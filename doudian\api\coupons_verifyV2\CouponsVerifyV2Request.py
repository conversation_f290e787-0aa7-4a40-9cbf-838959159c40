# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.coupons_verifyV2.param.CouponsVerifyV2Param import CouponsVerifyV2Param


class CouponsVerifyV2Request(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = CouponsVerifyV2Param()

	def getUrlPath(self, ):
		return "/coupons/verifyV2"

	def getParams(self, ):
		return self.params



