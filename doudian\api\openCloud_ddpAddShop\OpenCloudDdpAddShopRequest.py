# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.openCloud_ddpAddShop.param.OpenCloudDdpAddShopParam import OpenCloudDdpAddShopParam


class OpenCloudDdpAddShopRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OpenCloudDdpAddShopParam()

	def getUrlPath(self, ):
		return "/openCloud/ddpAddShop"

	def getParams(self, ):
		return self.params



