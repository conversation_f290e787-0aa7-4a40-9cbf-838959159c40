# 小红书数据同步服务数据库连接问题修复指南

## 问题描述

在小红书创意信息同步过程中，出现了以下 MySQL 连接错误：

```
(pymysql.err.InternalError) Packet sequence number wrong - got 1 expected 2
```

这通常是由于高并发数据库操作导致的连接池竞争问题。

## 解决方案

### 1. 数据库连接池优化

已对 `app/models/base.py` 中的数据库引擎配置进行了优化：

```python
# 异步引擎连接池配置
engine = create_async_engine(
    connection_url,
    pool_size=10,          # 连接池大小
    max_overflow=20,       # 最大溢出连接数
    pool_pre_ping=True,    # 连接前检查连接有效性
    pool_recycle=3600,     # 连接回收时间（1小时）
    pool_timeout=30,       # 获取连接的超时时间
    connect_args={
        "autocommit": False,
        "charset": "utf8mb4",
        "connect_timeout": 60,
        # 注意：aiomysql 不支持 read_timeout 和 write_timeout 参数
    }
)
```

**重要提示**：aiomysql 和 pymysql 的连接参数不完全兼容：

- aiomysql 支持：`connect_timeout`、`charset`、`autocommit` 等
- aiomysql 不支持：`read_timeout`、`write_timeout`
- pymysql 支持所有上述参数

### 2. 并发控制优化

降低了并发参数以减少数据库压力：

```python
# 同步创意信息时的并发配置
max_concurrent_advertisers: int = 3  # 从10降至3
max_concurrent_campaigns: int = 5    # 从10降至5
```

### 3. 重试机制增强

为数据库操作添加了重试机制：

```python
# 在 _process_creativities_batch 函数中
max_db_retries = 3
for db_attempt in range(max_db_retries):
    try:
        # 数据库操作
        break
    except Exception as e:
        if is_connection_error and db_attempt < max_db_retries - 1:
            await asyncio.sleep((db_attempt + 1) * 2)
            continue
        else:
            raise
```

### 4. API 请求频率限制

增加了 API 请求之间的延迟：

```python
# 每2个请求延迟一次，减少API调用频率
if page_index % 2 == 0:
    await asyncio.sleep(1.5)
```

### 5. 连接监控工具

添加了数据库连接池状态监控：

```python
async def check_database_connection():
    """检查数据库连接状态"""
    # 返回连接池状态信息

async def log_database_stats():
    """记录数据库连接池统计信息"""
    # 在同步开始和结束时记录状态
```

## 配置参数

### 数据库连接池配置

在 `config/development.ini` 中添加了数据库连接池配置：

```ini
[db_pool]
ASYNC_POOL_SIZE = 10
ASYNC_MAX_OVERFLOW = 20
SYNC_POOL_SIZE = 5
SYNC_MAX_OVERFLOW = 10
POOL_RECYCLE = 3600
POOL_TIMEOUT = 30
CONNECT_TIMEOUT = 60
READ_TIMEOUT = 30
WRITE_TIMEOUT = 30
```

## 使用建议

### 1. 监控数据库状态

在运行同步任务时，注意观察日志中的连接池状态信息：

```
数据库连接池状态 - 大小: 10, 已签入: 8, 已签出: 2, 溢出: 0, 无效: 0
```

### 2. 调整并发参数

如果仍然出现连接问题，可以进一步降低并发参数：

```python
# 更保守的并发设置
max_concurrent_advertisers: int = 2
max_concurrent_campaigns: int = 3
```

### 3. 分批处理

对于大量数据，建议分批处理：

```python
# 按广告主ID分批同步
for batch in batches(advertiser_ids, batch_size=5):
    await sync_xiaohongshu_creativity_info(advertiser_ids=batch)
    await asyncio.sleep(60)  # 批次间休息1分钟
```

### 4. 错误处理

已改进的错误处理会自动识别连接错误并重试：

- 连接序列号错误
- 连接超时
- 连接丢失
- MySQL 服务器连接断开

## 故障排除

### 常见问题

1. **连接池耗尽**

   - 检查 `checked_out` 数量是否接近 `pool_size + max_overflow`
   - 降低并发数量或增加连接池大小

2. **连接超时**

   - 检查网络连接
   - 增加 `connect_timeout` 和 `pool_timeout` 值

3. **重试失败**

   - 检查数据库服务器状态
   - 验证数据库配置参数

4. **SQLAlchemy 2.x 兼容性问题**

   - 错误：`Textual SQL expression 'SELECT 1 as test' should be explicitly declared as text()`
   - 解决：使用 `text()` 函数包装 SQL 字符串

   ```python
   from sqlalchemy import text
   result = await session.execute(text("SELECT 1 as test"))
   ```

5. **aiomysql 参数兼容性问题**
   - 错误：`connect() got an unexpected keyword argument 'read_timeout'`
   - 解决：移除 aiomysql 不支持的参数（`read_timeout`、`write_timeout`）
   - 仅在 pymysql 连接中使用这些参数

### 日志分析

关注以下日志信息：

```
# 正常状态
数据库连接池状态 - 大小: 10, 已签入: 8, 已签出: 2, 溢出: 0, 无效: 0

# 连接问题
数据库连接错误，第 1 次重试 (延迟 2s): Packet sequence number wrong

# 重试成功
重试API调用，第 1 次重试
```

## 性能优化建议

1. **定期清理连接池**：设置合理的 `pool_recycle` 时间
2. **监控连接使用**：定期检查连接池状态
3. **分时段同步**：避免在业务高峰期运行大量同步任务
4. **数据库调优**：优化数据库服务器配置和索引

## 后续改进

1. **连接池监控仪表板**：实时监控连接池状态
2. **自适应并发控制**：根据连接池状态动态调整并发数
3. **连接池预热**：应用启动时预热连接池
4. **异常熔断机制**：连续失败时暂停同步任务
