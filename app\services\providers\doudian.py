import asyncio
import calendar
import copy
import os
import sys
import time
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional
from enum import Enum
import logging

import dateutil.parser

from app.core.config import settings
from app.core.database import db as db_client
from app.utils.rate_limiter import RateLimiterDecorator
from app.utils.tools import retry

# 添加 path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../../.."))
sys.path.insert(0, project_root)

from doudian.api.order_searchList.OrderSearchListRequest import OrderSearchListRequest
from doudian.api.order_getSettleBillDetailV3.OrderGetSettleBillDetailV3Request import (
    OrderGetSettleBillDetailV3Request,
)
from doudian.core.AccessTokenBuilder import AccessTokenBuilder
from doudian.core.DoudianOpConfig import GlobalConfig

# 配置全局设置
GlobalConfig.appKey = settings.DOUDIAN["APPKEY"]
GlobalConfig.appSecret = settings.DOUDIAN["APPSECRET"]

# 配置日志
logger = logging.getLogger(__name__)


# 常量定义
class Constants:
    """系统常量定义"""

    # 时间相关常量
    SECONDS_PER_DAY = 86400
    DEFAULT_LOOKBACK_DAYS = 15
    TIME_PARTS_PER_DAY = 192

    # 数据库相关常量
    DATABASE_NAME = "rpa_data"
    COLLECTION_QIANCHUAN = "doudian_qianchuan"
    COLLECTION_LUOPAN = "doudian_luopan"
    COLLECTION_ADV = "doudian_adv"
    COLLECTION_ADV_CONFIG = "doudian_adv_config"
    COLLECTION_DAILY_REPORT = "doudian_daily_report"

    # 线程池配置
    MAX_WORKERS_MULTIPLIER = 1  # CPU核心数的倍数

    # API配置
    DEFAULT_PAGE_SIZE = 100
    MAX_RETRIES = 3
    RETRY_DELAY = 1
    QPS_LIMIT = 20  # 降低QPS限制以避免触发服务端限流

    # 已授权的店铺ID
    AUTHORIZED_STORE_IDS = ["8432227", "18483687", "33293715", "176970461"]


class ReportType(Enum):
    """报告类型枚举"""

    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"


@dataclass
class OrderSumResult:
    """订单汇总结果基类"""

    def __add__(self, other):
        """支持加法操作"""
        if not isinstance(other, self.__class__):
            return NotImplemented

        result = self.__class__()
        for field_name in self.__dataclass_fields__:
            current_value = getattr(self, field_name)
            other_value = getattr(other, field_name)
            setattr(result, field_name, current_value + other_value)
        return result


@dataclass
class DoudianOrderSumResult(OrderSumResult):
    """抖店订单汇总结果"""

    only_platform_cost_amount: float = 0.0  # 平台承担金额
    promotion_talent_amount: float = 0.0  # 达人承担金额


@dataclass
class AllianceOrderSumResult(OrderSumResult):
    """联盟订单汇总结果"""

    commission: float = 0.0  # 佣金


@dataclass
class LiveRoomData:
    """直播间数据"""

    name: str
    stat_cost: float = 0.0
    gmv: float = 0.0
    pay_amt: float = 0.0
    coupon_amt: float = 0.0
    roi: float = 0.0


@dataclass
class DailyReportItem:
    """每日报告数据项"""

    shop_id: str
    shop_name: str
    date: str
    datetime: int
    type: str = "daily"

    # 全域直播数据
    uni_live_room_stat_cost: float = 0.0
    uni_live_room_gmv: float = 0.0
    uni_live_room_pay_amt: float = 0.0
    uni_live_room_coupon_amt: float = 0.0
    uni_live_room_roi: float = 0.0

    # 全域商品数据
    uni_product_stat_cost: float = 0.0
    uni_product_gmv: float = 0.0
    uni_product_pay_amt: float = 0.0
    uni_product_coupon_amt: float = 0.0
    uni_product_roi: float = 0.0

    # 标准直播间数据
    standard_live_room_stat_cost: float = 0.0

    # 标准商品数据
    standard_product_stat_cost: float = 0.0
    standard_product_gmv: float = 0.0
    standard_product_pay_amt: float = 0.0
    standard_product_coupon_amt: float = 0.0
    standard_product_roi: float = 0.0

    # 品专数据
    brand_stat_cost: float = 0.0
    brand_gmv: float = 0.0
    brand_roi: float = 0.0

    # KOC数据
    koc_stat_cost: float = 0.0
    koc_gmv: float = 0.0
    koc_pay_amt: float = 0.0
    koc_coupon_amt: float = 0.0
    koc_roi: float = 0.0

    # 其他数据
    platform_subsidy: float = 0.0
    influencer_actual_commission_expense: float = 0.0
    influencer_subsidy: float = 0.0
    cooperation_gmv: float = 0.0
    product_card_pay_amt: float = 0.0
    total_pay_amt: float = 0.0
    total_refund_amt: float = 0.0
    total_refund_amt_pay_time: float = 0.0
    actual_total_pay_amt: float = 0.0
    total_cost: float = 0.0
    self_total_cost: float = 0.0
    self_total_amt: float = 0.0
    self_total_roi: float = 0.0
    product_card_natural_amt: float = 0.0

    # 直播间数据列表
    live_room: List[LiveRoomData] = field(default_factory=list)


class DoudianStatisticsService:
    """抖店数据统计服务类

    提供抖店平台数据统计的核心功能，包括：
    - 数据查询和聚合
    - ROI计算
    - 报告生成
    - 缓存管理
    """

    def __init__(self):
        """初始化服务"""
        self.db = db_client[Constants.DATABASE_NAME]
        self.timezone = timezone(timedelta(hours=8))

    @staticmethod
    def get_monday_timestamp(timestamp: int) -> int:
        """获取指定时间戳所在周的周一时间戳

        Args:
            timestamp: 输入时间戳

        Returns:
            周一的时间戳
        """
        dt = datetime.fromtimestamp(timestamp)
        monday = dt - timedelta(days=dt.weekday())
        return int(monday.timestamp())

    def _get_default_time_range(self) -> tuple[int, int]:
        """获取默认时间范围（15天前到现在）

        Returns:
            (start_time, end_time) 时间戳元组
        """
        current_time = int(time.time())
        start_time = (
            current_time - Constants.SECONDS_PER_DAY * Constants.DEFAULT_LOOKBACK_DAYS
        )
        return start_time, current_time

    async def _get_adv_list_with_config(self) -> List[Dict]:
        """获取广告主列表及其配置信息

        Returns:
            包含广告主和配置信息的列表
        """
        try:
            pipeline = [
                {
                    "$lookup": {
                        "from": Constants.COLLECTION_ADV_CONFIG,
                        "localField": "adv_id",
                        "foreignField": "adv_id",
                        "as": "configs",
                    }
                },
                {
                    "$unwind": {
                        "path": "$configs",
                        "preserveNullAndEmptyArrays": True,
                    },
                },
                {"$project": {"_id": 0}},
            ]

            adv_list = (
                await self.db[Constants.COLLECTION_ADV].aggregate(pipeline).to_list()
            )
            logger.info(f"获取到 {len(adv_list)} 个广告主配置")
            return adv_list

        except Exception as e:
            logger.error(f"获取广告主列表失败: {e}")
            raise

    def _build_main_aggregation_pipeline(
        self, start_time: int, end_time: int
    ) -> List[Dict]:
        """构建主要数据聚合管道

        Args:
            start_time: 开始时间戳
            end_time: 结束时间戳

        Returns:
            MongoDB聚合管道
        """
        where_clause = {"datetime": {"$gte": start_time, "$lte": end_time}}

        return [
            {"$match": where_clause},
            {
                "$group": {
                    "_id": {
                        "shop_id": "$shop_id",
                        "date": "$date",
                        "datetime": "$datetime",
                    },
                    "shop_id": {"$first": "$shop_id"},
                    "shop_name": {"$first": "$shop_name"},
                    "date": {"$first": "$date"},
                    "datetime": {"$first": "$datetime"},
                    # 全域直播数据聚合
                    "uni_live_room_stat_cost": {
                        "$sum": "$promotion.uni.live_room.summary.stat_cost"
                    },
                    "uni_live_room_gmv": {
                        "$sum": {
                            "$add": [
                                "$promotion.uni.live_room.summary.total_pay_order_gmv_for_roi2",
                                "$promotion.uni.live_room.summary.total_pay_order_coupon_amount_for_roi2",
                            ]
                        }
                    },
                    "uni_live_room_pay_amt": {
                        "$sum": "$promotion.uni.live_room.summary.total_pay_order_gmv_for_roi2"
                    },
                    "uni_live_room_coupon_amt": {
                        "$sum": "$promotion.uni.live_room.summary.total_pay_order_coupon_amount_for_roi2"
                    },
                    # 标准直播间数据聚合
                    "standard_live_room_stat_cost": {
                        "$sum": "$promotion.standard.live_room.summary.stat_cost"
                    },
                }
            },
            # 关联罗盘数据
            {
                "$lookup": {
                    "from": Constants.COLLECTION_LUOPAN,
                    "let": {"shop_id": "$shop_id", "datetime": "$datetime"},
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {"$eq": ["$shop_id", "$$shop_id"]},
                                        {"$eq": ["$datetime", "$$datetime"]},
                                    ]
                                }
                            }
                        },
                        {
                            "$project": {
                                "cooperation_gmv": "$cooperation.values.pay_amt",
                                "product_card_pay_amt": "$product_card.pay_amt",
                                "pay_amt": "$all.pay_amt",
                                "refund_amt": "$all.refund_amt",
                                "refund_amt_pay_time": "$all.refund_amt_pay_time",
                                "_id": 0,
                            }
                        },
                    ],
                    "as": "luopan_data",
                }
            },
            # 添加计算字段
            {
                "$addFields": {
                    "cooperation_gmv": {
                        "$round": [
                            {
                                "$divide": [
                                    {
                                        "$ifNull": [
                                            {"$first": "$luopan_data.cooperation_gmv"},
                                            0,
                                        ]
                                    },
                                    100,
                                ]
                            },
                            2,
                        ]
                    },
                    "product_card_pay_amt": {
                        "$round": [
                            {
                                "$divide": [
                                    {
                                        "$ifNull": [
                                            {
                                                "$first": "$luopan_data.product_card_pay_amt"
                                            },
                                            0,
                                        ]
                                    },
                                    100,
                                ]
                            },
                            2,
                        ]
                    },
                    "uni_live_room_gmv": {"$round": ["$uni_live_room_gmv", 2]},
                    "total_pay_amt": {
                        "$divide": [{"$sum": ["$luopan_data.pay_amt"]}, 100]
                    },
                    "total_refund_amt": {
                        "$divide": [{"$sum": ["$luopan_data.refund_amt"]}, 100]
                    },
                    "total_refund_amt_pay_time": {
                        "$divide": [{"$sum": ["$luopan_data.refund_amt_pay_time"]}, 100]
                    },
                }
            },
            # 字段投影
            {
                "$project": {
                    "_id": 0,
                    "luopan_data": 0,
                    "yesterday_data": 0,
                }
            },
            {"$sort": {"datetime": -1}},
        ]

    async def execute_daily_statistics(
        self, start_time: Optional[int] = None, end_time: Optional[int] = None
    ) -> List[Dict]:
        """执行每日数据统计

        Args:
            start_time: 开始时间戳，默认为15天前
            end_time: 结束时间戳，默认为当前时间

        Returns:
            统计结果列表
        """
        try:
            # 设置默认时间范围
            if start_time is None or end_time is None:
                default_start, default_end = self._get_default_time_range()
                start_time = start_time or default_start
                end_time = end_time or default_end

            logger.info(f"开始执行每日数据统计，时间范围: {start_time} - {end_time}")

            # 获取广告主配置
            adv_list = await self._get_adv_list_with_config()

            # 构建并执行主要数据查询
            pipeline = self._build_main_aggregation_pipeline(start_time, end_time)
            data = (
                await self.db[Constants.COLLECTION_QIANCHUAN]
                .aggregate(pipeline)
                .to_list()
            )

            logger.info(f"获取到 {len(data)} 条主要数据记录")

            # 处理每条数据记录
            for item in data:
                await self._process_daily_item(item, adv_list)

            logger.info("每日数据统计完成")
            return data

        except Exception as e:
            logger.error(f"执行每日数据统计失败: {e}")
            raise

    async def _process_daily_item(
        self,
        item: Dict,
        adv_list: List[Dict],
    ) -> None:
        """处理单个每日数据项

        Args:
            item: 数据项
            adv_list: 广告主列表
        """
        shop_id = item.get("shop_id")
        item_datetime = item.get("datetime")

        logger.debug(f"处理店铺 {shop_id} 的数据，时间: {item_datetime}")

        # 并行获取订单汇总数据
        doudian_result, alliance_result = await self._get_order_summary_parallel(
            shop_id, item_datetime
        )

        # 设置基础数据
        item["platform_subsidy"] = doudian_result.only_platform_cost_amount / 100
        item["influencer_actual_commission_expense"] = alliance_result.commission / 100
        item["influencer_subsidy"] = doudian_result.promotion_talent_amount / 100

        # 处理各类广告数据
        await self._process_uni_product_data(item, adv_list, shop_id, item_datetime)
        await self._process_standard_product_data(
            item, adv_list, shop_id, item_datetime
        )
        await self._process_koc_data(item, adv_list, shop_id, item_datetime)
        await self._process_live_room_data(item, adv_list, shop_id, item_datetime)
        await self._process_brand_data(item, adv_list, shop_id, item_datetime)

        # 计算汇总数据
        self._calculate_summary_metrics(item)

        # 计算ROI
        self._calculate_roi(item)

        # 保存到数据库
        await self._save_daily_report(item)

    async def _get_order_summary_parallel(
        self, shop_id: str, item_datetime: int
    ) -> tuple[DoudianOrderSumResult, AllianceOrderSumResult]:
        """并行获取订单汇总数据

        Args:
            shop_id: 店铺ID
            item_datetime: 数据时间戳

        Returns:
            (抖店订单汇总, 联盟订单汇总)
        """
        doudian_result = DoudianOrderSumResult()
        alliance_result = AllianceOrderSumResult()

        def add_doudian_result(future):
            nonlocal doudian_result
            doudian_result = doudian_result + future.result()

        def add_alliance_result(future):
            nonlocal alliance_result
            alliance_result = alliance_result + future.result()

        # 大幅减少并发数量以避免触发限流
        max_workers = 2  # 限制最大并发数为2

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            # 减少时间段分割，从192个减少到24个（每小时一个）
            time_parts_per_day = 24
            step = Constants.SECONDS_PER_DAY // time_parts_per_day

            for i in range(time_parts_per_day):
                start_time = item_datetime + i * step
                end_time = (
                    start_time + step
                    if i < time_parts_per_day - 1
                    else item_datetime + Constants.SECONDS_PER_DAY
                )

                # 提交抖店订单任务
                doudian_future = executor.submit(
                    sum_doudian_order, shop_id, start_time, end_time
                )
                doudian_future.add_done_callback(add_doudian_result)
                futures.append(doudian_future)

                # 提交联盟订单任务
                alliance_future = executor.submit(
                    sum_alliance_order, shop_id, start_time, end_time
                )
                alliance_future.add_done_callback(add_alliance_result)
                futures.append(alliance_future)

            # 等待所有任务完成
            executor.shutdown(wait=True)

        return doudian_result, alliance_result

    async def _process_uni_product_data(
        self, item: Dict, adv_list: List[Dict], shop_id: str, item_datetime: int
    ) -> None:
        """处理全域商品数据"""
        uni_product_card_adv_list = [
            adv.get("adv_id")
            for adv in adv_list
            if adv.get("configs", {}).get("is_uni_product_card") == True
            and (
                adv.get("configs", {}).get("uni_product_card_shop_id") == shop_id
                or (
                    adv.get("shop_id") == shop_id
                    and adv.get("configs", {}).get("uni_product_card_shop_id") is None
                )
            )
        ]

        # 如果没有配置，则取广告主本身店铺为当前店铺的广告主列表
        if len(uni_product_card_adv_list) == 0:
            uni_product_card_adv_list = [
                adv.get("adv_id") for adv in adv_list if adv.get("shop_id") == shop_id
            ]

        if len(uni_product_card_adv_list) > 0:
            try:
                uni_product_card_data = (
                    await self.db[Constants.COLLECTION_QIANCHUAN]
                    .aggregate(
                        [
                            {
                                "$match": {
                                    "adv_id": {"$in": uni_product_card_adv_list},
                                    "datetime": item_datetime,
                                }
                            },
                            {
                                "$group": {
                                    "_id": None,
                                    "stat_cost": {
                                        "$sum": "$promotion.uni.product.summary.stat_cost_for_roi2"
                                    },
                                    "pay_amt": {
                                        "$sum": "$promotion.uni.product.summary.total_pay_order_gmv_for_roi2"
                                    },
                                    "coupon_amt": {
                                        "$sum": "$promotion.uni.product.summary.total_pay_order_coupon_amount_for_roi2"
                                    },
                                }
                            },
                            {"$project": {"_id": 0}},
                        ]
                    )
                    .next()
                )

                item["uni_product_stat_cost"] = round(
                    uni_product_card_data.get("stat_cost", 0), 2
                )
                item["uni_product_pay_amt"] = round(
                    uni_product_card_data.get("pay_amt", 0), 2
                )
                item["uni_product_coupon_amt"] = round(
                    uni_product_card_data.get("coupon_amt", 0), 2
                )
                item["uni_product_gmv"] = round(
                    item["uni_product_pay_amt"] + item["uni_product_coupon_amt"], 2
                )
            except StopAsyncIteration:
                # 没有数据时设置默认值
                item["uni_product_stat_cost"] = 0
                item["uni_product_pay_amt"] = 0
                item["uni_product_coupon_amt"] = 0
                item["uni_product_gmv"] = 0
        else:
            item["uni_product_stat_cost"] = 0
            item["uni_product_pay_amt"] = 0
            item["uni_product_coupon_amt"] = 0
            item["uni_product_gmv"] = 0

    async def _process_standard_product_data(
        self, item: Dict, adv_list: List[Dict], shop_id: str, item_datetime: int
    ) -> None:
        """处理标准商品（图文）数据"""
        standard_product_adv_list = [
            adv.get("adv_id")
            for adv in adv_list
            if adv.get("configs", {}).get("is_standard_product") == True
            and (
                adv.get("configs", {}).get("standard_product_shop_id") == shop_id
                or (
                    adv.get("shop_id") == shop_id
                    and adv.get("configs", {}).get("standard_product_shop_id") is None
                )
            )
        ]

        if len(standard_product_adv_list) > 0:
            try:
                standard_product_data = (
                    await self.db[Constants.COLLECTION_QIANCHUAN]
                    .aggregate(
                        [
                            {
                                "$match": {
                                    "adv_id": {"$in": standard_product_adv_list},
                                    "datetime": item_datetime,
                                }
                            },
                            {
                                "$group": {
                                    "_id": None,
                                    "stat_cost": {
                                        "$sum": "$promotion.standard.product.summary.stat_cost"
                                    },
                                    "gmv": {
                                        "$sum": {
                                            "$add": [
                                                "$promotion.standard.product.summary.all_order_pay_gmv_7days",
                                                "$promotion.standard.product.summary.pay_order_coupon_amount",
                                            ]
                                        },
                                    },
                                    "coupon_amt": {
                                        "$sum": "$promotion.standard.product.summary.pay_order_coupon_amount"
                                    },
                                    "pay_amt": {
                                        "$sum": "$promotion.standard.product.summary.all_order_pay_gmv_7days"
                                    },
                                }
                            },
                            {"$project": {"_id": 0}},
                        ]
                    )
                    .next()
                )

                item["standard_product_stat_cost"] = round(
                    standard_product_data.get("stat_cost", 0), 2
                )
                item["standard_product_gmv"] = round(
                    standard_product_data.get("gmv", 0), 2
                )
                item["standard_product_coupon_amt"] = round(
                    standard_product_data.get("coupon_amt", 0), 2
                )
                item["standard_product_pay_amt"] = round(
                    standard_product_data.get("pay_amt", 0), 2
                )
            except StopAsyncIteration:
                item["standard_product_stat_cost"] = 0
                item["standard_product_gmv"] = 0
                item["standard_product_coupon_amt"] = 0
                item["standard_product_pay_amt"] = 0
        else:
            item["standard_product_stat_cost"] = 0
            item["standard_product_gmv"] = 0
            item["standard_product_coupon_amt"] = 0
            item["standard_product_pay_amt"] = 0

    async def _process_koc_data(
        self, item: Dict, adv_list: List[Dict], shop_id: str, item_datetime: int
    ) -> None:
        """处理KOC挂车数据"""
        koc_adv_list = [
            adv.get("adv_id")
            for adv in adv_list
            if adv.get("configs", {}).get("is_koc") == True
            and (
                adv.get("configs", {}).get("koc_shop_id") == shop_id
                or (
                    adv.get("shop_id") == shop_id
                    and adv.get("configs", {}).get("koc_shop_id") is None
                )
            )
        ]

        if len(koc_adv_list) > 0:
            try:
                koc_data = (
                    await self.db[Constants.COLLECTION_QIANCHUAN]
                    .aggregate(
                        [
                            {
                                "$match": {
                                    "adv_id": {"$in": koc_adv_list},
                                    "datetime": item_datetime,
                                }
                            },
                            {
                                "$group": {
                                    "_id": {
                                        "shop_id": "$shop_id",
                                        "datetime": "$datetime",
                                    },
                                    "stat_cost": {
                                        "$sum": "$promotion.standard.product.summary.stat_cost"
                                    },
                                    "gmv": {
                                        "$sum": {
                                            "$add": [
                                                "$promotion.standard.product.summary.all_order_pay_gmv_7days",
                                                "$promotion.standard.product.summary.pay_order_coupon_amount",
                                            ]
                                        },
                                    },
                                    "pay_amt": {
                                        "$sum": "$promotion.standard.product.summary.all_order_pay_gmv_7days"
                                    },
                                    "coupon_amt": {
                                        "$sum": "$promotion.standard.product.summary.pay_order_coupon_amount"
                                    },
                                }
                            },
                        ]
                    )
                    .next()
                )

                item["koc_stat_cost"] = round(koc_data.get("stat_cost", 0), 2)
                item["koc_gmv"] = round(koc_data.get("gmv", 0), 2)
                item["koc_pay_amt"] = round(koc_data.get("pay_amt", 0), 2)
                item["koc_coupon_amt"] = round(koc_data.get("coupon_amt", 0), 2)
            except StopAsyncIteration:
                item["koc_stat_cost"] = 0
                item["koc_gmv"] = 0
                item["koc_pay_amt"] = 0
                item["koc_coupon_amt"] = 0
        else:
            item["koc_stat_cost"] = 0
            item["koc_gmv"] = 0
            item["koc_pay_amt"] = 0
            item["koc_coupon_amt"] = 0

    async def _process_live_room_data(
        self,
        item: Dict,
        adv_list: List[Dict],
        shop_id: str,
        item_datetime: int,
    ) -> None:
        """处理直播间数据"""
        live_room_adv_list = [
            adv
            for adv in adv_list
            if adv.get("configs", {}).get("live_room_shop_id") == shop_id
            or (
                adv.get("shop_id") == shop_id
                and adv.get("configs", {}).get("live_room_shop_id") is None
            )
        ]

        live_room_map = {}
        for adv in live_room_adv_list:
            live_room = adv.get("configs", {}).get("live_room")
            if live_room and live_room.strip():
                if live_room not in live_room_map:
                    live_room_map[live_room] = []
                live_room_map[live_room].append(adv.get("adv_id"))

        live_room_cost = 0.0
        live_room_gmv = 0.0
        item["live_room"] = []

        for live_room, adv_id_list in live_room_map.items():
            try:
                live_room_data = (
                    await self.db[Constants.COLLECTION_QIANCHUAN]
                    .aggregate(
                        [
                            {
                                "$match": {
                                    "adv_id": {"$in": adv_id_list},
                                    "datetime": item_datetime,
                                }
                            },
                            {
                                "$group": {
                                    "_id": None,
                                    "stat_cost": {
                                        "$sum": "$promotion.standard.live_room.summary.stat_cost"
                                    },
                                    "gmv": {
                                        "$sum": {
                                            "$add": [
                                                "$promotion.standard.live_room.summary.pay_order_amount",
                                                "$promotion.standard.live_room.summary.pay_order_coupon_amount",
                                            ]
                                        },
                                    },
                                    "pay_amt": {
                                        "$sum": "$promotion.standard.live_room.summary.pay_order_amount"
                                    },
                                    "coupon_amt": {
                                        "$sum": "$promotion.standard.live_room.summary.pay_order_coupon_amount"
                                    },
                                },
                            },
                            {
                                "$addFields": {
                                    "stat_cost": {"$round": ["$stat_cost", 2]},
                                    "gmv": {"$round": ["$gmv", 2]},
                                    "pay_amt": {"$round": ["$pay_amt", 2]},
                                    "coupon_amt": {"$round": ["$coupon_amt", 2]},
                                }
                            },
                            {"$project": {"_id": 0}},
                        ]
                    )
                    .next()
                )

                live_room_data["name"] = live_room
                item["live_room"].append(live_room_data)
                live_room_cost += live_room_data.get("stat_cost", 0)
                live_room_gmv += live_room_data.get("gmv", 0)

            except StopAsyncIteration:
                # 没有数据时添加空记录
                empty_data = {
                    "name": live_room,
                    "stat_cost": 0.0,
                    "gmv": 0.0,
                    "pay_amt": 0.0,
                    "coupon_amt": 0.0,
                }
                item["live_room"].append(empty_data)

    async def _process_brand_data(
        self, item: Dict, adv_list: List[Dict], shop_id: str, item_datetime: int
    ) -> None:
        """处理品专数据"""
        brand_adv_list = [
            adv.get("adv_id")
            for adv in adv_list
            if adv.get("configs", {}).get("is_brand_ad") == True
            and (
                adv.get("configs", {}).get("brand_shop_id") == shop_id
                or (
                    adv.get("shop_id") == shop_id
                    and adv.get("configs", {}).get("brand_shop_id") is None
                )
            )
        ]

        if len(brand_adv_list) > 0:
            try:
                brand_data = (
                    await self.db[Constants.COLLECTION_QIANCHUAN]
                    .aggregate(
                        [
                            {
                                "$match": {
                                    "adv_id": {"$in": brand_adv_list},
                                    "datetime": item_datetime,
                                }
                            },
                            {
                                "$group": {
                                    "_id": None,
                                    "stat_cost": {"$sum": "$brand.stat_cost"},
                                    "gmv": {"$sum": "$brand.all_order_pay_gmv_1days"},
                                }
                            },
                        ]
                    )
                    .next()
                )

                # 品专成本需要乘以0.76的系数
                item["brand_stat_cost"] = round(
                    brand_data.get("stat_cost", 0) * 0.76, 2
                )
                item["brand_gmv"] = round(brand_data.get("gmv", 0), 2)
            except StopAsyncIteration:
                item["brand_stat_cost"] = 0
                item["brand_gmv"] = 0
        else:
            item["brand_stat_cost"] = 0
            item["brand_gmv"] = 0

    def _calculate_summary_metrics(self, item: Dict) -> None:
        """计算汇总指标"""
        # 获取直播间数据
        live_room_cost = sum(
            room.get("stat_cost", 0) for room in item.get("live_room", [])
        )
        live_room_gmv = sum(room.get("gmv", 0) for room in item.get("live_room", []))

        # 计算总成本
        total_cost = (
            item.get("uni_live_room_stat_cost", 0)  # 全域直播
            + item.get("uni_product_stat_cost", 0)  # 全域商品
            + item.get("brand_stat_cost", 0)  # 品专
            + item.get("influencer_actual_commission_expense", 0)  # 达人佣金
            + item.get("standard_product_stat_cost", 0)  # 图文消耗/标准商品
            + live_room_cost  # 直播间
            + item.get("koc_stat_cost", 0)  # KOC挂车消耗
        )

        # 如果没有直播间配置，则加上标准直播间成本
        if not item.get("live_room"):
            total_cost += item.get("standard_live_room_stat_cost", 0)

        item["total_cost"] = round(total_cost, 2)

        # 计算实际总支付金额
        actual_total_pay_amt = (
            item.get("total_pay_amt", 0)
            - item.get("total_refund_amt_pay_time", 0)
            + item.get("platform_subsidy", 0)
            + item.get("influencer_subsidy", 0)
        )
        item["actual_total_pay_amt"] = round(actual_total_pay_amt, 2)

        # 自营总消耗
        self_total_cost = (
            item.get("uni_live_room_stat_cost", 0)
            + item.get("uni_product_stat_cost", 0)
            + item.get("brand_stat_cost", 0)
            + live_room_cost
            + item.get("standard_product_stat_cost", 0)
            + item.get("koc_stat_cost", 0)
        )
        item["self_total_cost"] = round(self_total_cost, 2)

        # 达播 GMV = 合作GMV - KOC GMV
        dabo_gmv = item.get("cooperation_gmv", 0) - item.get("koc_gmv", 0)

        # 自营总销售额
        self_total_amt = (
            item.get("actual_total_pay_amt", 0)
            + item.get("platform_subsidy", 0)
            - item.get("total_refund_amt_pay_time", 0)
            - dabo_gmv
        )
        item["self_total_amt"] = round(self_total_amt, 2)

        # 商品卡自然成交
        product_card_natural_amt = (
            item.get("actual_total_pay_amt", 0)
            + item.get("total_refund_amt_pay_time", 0)
            - item.get("standard_product_gmv", 0)
            - item.get("cooperation_gmv", 0)
            - item.get("influencer_subsidy", 0)
            - live_room_gmv
            - item.get("brand_gmv", 0)
            - item.get("uni_product_gmv", 0)
            - item.get("uni_live_room_gmv", 0)
            - item.get("koc_gmv", 0)
        )
        item["product_card_natural_amt"] = round(product_card_natural_amt, 2)
        item["type"] = "daily"

    def _calculate_roi(self, item: Dict) -> None:
        """计算各项ROI"""
        # 全域直播ROI
        if item.get("uni_live_room_stat_cost", 0) > 0:
            item["uni_live_room_roi"] = round(
                item.get("uni_live_room_gmv", 0)
                / item.get("uni_live_room_stat_cost", 0),
                2,
            )
        else:
            item["uni_live_room_roi"] = 0

        # 全域商品ROI
        if item.get("uni_product_stat_cost", 0) > 0:
            item["uni_product_roi"] = round(
                item.get("uni_product_gmv", 0) / item.get("uni_product_stat_cost", 0), 2
            )
        else:
            item["uni_product_roi"] = 0

        # 品专ROI
        if item.get("brand_stat_cost", 0) > 0:
            item["brand_roi"] = round(
                item.get("brand_gmv", 0) / item.get("brand_stat_cost", 0), 2
            )
        else:
            item["brand_roi"] = 0

        # 图文ROI
        if item.get("standard_product_stat_cost", 0) > 0:
            item["standard_product_roi"] = round(
                item.get("standard_product_gmv", 0)
                / item.get("standard_product_stat_cost", 0),
                2,
            )
        else:
            item["standard_product_roi"] = 0

        # KOC ROI
        if item.get("koc_stat_cost", 0) > 0:
            item["koc_roi"] = round(
                item.get("koc_gmv", 0) / item.get("koc_stat_cost", 0), 2
            )
        else:
            item["koc_roi"] = 0

        # 自营总ROI
        if item.get("self_total_cost", 0) > 0:
            item["self_total_roi"] = round(
                item.get("self_total_amt", 0) / item.get("self_total_cost", 0), 2
            )
        else:
            item["self_total_roi"] = 0

        # 直播间ROI
        for live_room in item.get("live_room", []):
            if live_room.get("stat_cost", 0) > 0:
                live_room["roi"] = round(
                    live_room.get("gmv", 0) / live_room.get("stat_cost", 0), 2
                )
            else:
                live_room["roi"] = 0

    async def _save_daily_report(self, item: Dict) -> None:
        """保存每日报告到数据库"""
        try:
            await self.db[Constants.COLLECTION_DAILY_REPORT].replace_one(
                {"shop_id": item.get("shop_id"), "date": item.get("date")},
                item,
                upsert=True,
            )
            logger.debug(
                f"保存每日报告成功: 店铺 {item.get('shop_id')}, 日期 {item.get('date')}"
            )
        except Exception as e:
            logger.error(f"保存每日报告失败: {e}")
            raise


# 创建全局服务实例
_doudian_service = DoudianStatisticsService()


# 向后兼容的包装函数
async def executeStatisticsDailyData(
    start_time: Optional[int] = None,
    end_time: Optional[int] = None,
) -> List[Dict]:
    """执行统计每日数据（向后兼容包装函数）

    Args:
        start_time: 开始时间戳，默认为15天前
        end_time: 结束时间戳，默认为当前时间

    Returns:
        统计结果列表
    """
    return await _doudian_service.execute_daily_statistics(start_time, end_time)


def get_monday_timestamp(timestamp: int) -> int:
    """获取指定时间戳所在周的周一时间戳（向后兼容包装函数）

    Args:
        timestamp: 输入时间戳

    Returns:
        周一的时间戳
    """
    return DoudianStatisticsService.get_monday_timestamp(timestamp)


def calculate_roi(item: Dict) -> None:
    """计算ROI（向后兼容包装函数）

    Args:
        item: 数据项字典
    """
    _doudian_service._calculate_roi(item)


async def executeStatisticsData(
    type: str,
    start_date: datetime | None = None,
    end_date: datetime | None = None,
):
    """
    执行统计每周数据

    Args:
        type: 类型, 可选值: "weekly", "monthly"
        start_date: 开始时间，默认为当前周的周一或当前月的1号
        end_date: 结束时间，默认为当前周的周日或当前月的最后一天
    """
    tz = timezone(timedelta(hours=8))
    if start_date is None:
        if type == "weekly":
            start_date = datetime.now().replace(
                hour=0, minute=0, second=0, microsecond=0, tzinfo=tz
            ) - timedelta(days=datetime.now().weekday())
        elif type == "monthly":
            start_date = datetime.now().replace(
                day=1, hour=0, minute=0, second=0, microsecond=0, tzinfo=tz
            )
    else:
        if type == "weekly":
            start_date = start_date.replace(
                hour=0, minute=0, second=0, microsecond=0, tzinfo=tz
            ) - timedelta(days=start_date.weekday())
        elif type == "monthly":
            start_date = start_date.replace(
                day=1, hour=0, minute=0, second=0, microsecond=0, tzinfo=tz
            )
    if end_date is None:
        if type == "weekly":
            end_date = start_date.replace(
                hour=23, minute=59, second=59, microsecond=999999, tzinfo=tz
            ) + timedelta(days=6)
        elif type == "monthly":
            end_date = start_date.replace(
                day=calendar.monthrange(start_date.year, start_date.month)[1],
                hour=23,
                minute=59,
                second=59,
                microsecond=999999,
                tzinfo=tz,
            )
    else:
        if type == "weekly":
            end_date = end_date.replace(
                hour=23, minute=59, second=59, microsecond=999999, tzinfo=tz
            ) + timedelta(days=6)
        elif type == "monthly":
            end_date = end_date.replace(
                day=calendar.monthrange(end_date.year, end_date.month)[1],
                hour=23,
                minute=59,
                second=59,
                microsecond=999999,
                tzinfo=tz,
            )
    start_time = int(start_date.timestamp())
    end_time = int(end_date.timestamp())
    collection = db_client["rpa_data"]["doudian_daily_report"]
    # _id 不返回,
    data = (
        await collection.find(
            {"type": "daily", "datetime": {"$gte": start_time, "$lte": end_time}},
            {"_id": 0},
        )
        .sort("datetime", 1)
        .to_list()
    )
    ignore_fields = [
        "_id",
        "shop_id",
        "shop_name",
        "live_room",
        "date",
        "datetime",
        "name",
        "type",
        "start_time",
        "end_time",
    ]

    def get_group_start_time(date: datetime):
        if type == "weekly":
            return date.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(
                days=date.weekday()
            )
        elif type == "monthly":
            return date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    def get_group_end_time(date: datetime):
        if type == "weekly":
            return date.replace(
                hour=23, minute=59, second=59, microsecond=999999
            ) + timedelta(days=6)
        elif type == "monthly":
            return date.replace(
                day=calendar.monthrange(date.year, date.month)[1],
                hour=23,
                minute=59,
                second=59,
                microsecond=999999,
            )

    # 按周分组
    group_data = {}
    for item in data:
        shop_id = item.get("shop_id")
        item_date = datetime.fromtimestamp(item.get("datetime"))
        # 取这一周的周一 或者 这一月的1号
        group_start_time = get_group_start_time(item_date)
        group_end_time = get_group_end_time(group_start_time)
        group_name = f"{shop_id} - {group_start_time.strftime('%Y-%m-%d')} - {group_end_time.strftime('%Y-%m-%d')}"

        if group_name not in group_data:
            group_data[group_name] = {
                "start_time": int(group_start_time.timestamp()),
                "end_time": int(group_end_time.timestamp()),
            }

        # 获取当前数据项的所有字段名
        keys = list(item.keys())
        for key in keys:
            # 处理直播间数据
            if key == "live_room":
                # 如果周数据中还没有直播间数组,初始化一个空数组
                if "live_room" not in group_data[group_name]:
                    group_data[group_name]["live_room"] = []
                # 遍历当前数据项中的每个直播间
                for live_room in item.get("live_room", []):
                    # 在周数据的直播间列表中查找是否已存在同名直播间
                    target = next(
                        (
                            room
                            for room in group_data[group_name]["live_room"]
                            if room.get("name") == live_room.get("name")
                        ),
                        None,
                    )
                    # 如果不存在,则复制一份添加到周数据中
                    if target is None:
                        target = copy.deepcopy(live_room)
                        group_data[group_name]["live_room"].append(target)
                        continue
                    # 存在则累加各项数据
                    live_room_keys = list(live_room.keys())
                    for live_room_key in live_room_keys:
                        # 跳过不需要累加的字段
                        if live_room_key in ignore_fields:
                            continue
                        # 初始化为0
                        if live_room_key not in target:
                            target[live_room_key] = 0
                        # 累加数值
                        target[live_room_key] += live_room.get(live_room_key)
            elif key not in group_data[group_name]:
                group_data[group_name][key] = item.get(key)
            elif key not in ignore_fields:
                group_data[group_name][key] += item.get(key)

    def round_value(obj: any) -> any:
        if isinstance(obj, float):
            return round(obj, 2)
        if isinstance(obj, dict):
            return {key: round_value(value) for key, value in obj.items()}
        if isinstance(obj, list):
            return [round_value(value) for value in obj]
        return obj

    group_data = round_value(group_data)
    collection = db_client["rpa_data"]["doudian_daily_report"]
    for group_name, values in group_data.items():
        # 删除 date, datetime
        values.pop("date")
        values.pop("datetime")
        values["datetime"] = values.get("end_time")
        shop_id = values.get("shop_id")
        values["type"] = type
        values["date"] = "周数据" if type == "weekly" else "月数据"
        calculate_roi(values)
        # 保存到数据库
        await collection.replace_one(
            {
                "start_time": values.get("start_time"),
                "end_time": values.get("end_time"),
                "shop_id": shop_id,
            },
            values,
            upsert=True,
        )


def calculate_roi(item: dict) -> float:
    # 计算各项 ROI
    # 全域直播ROI
    if item.get("uni_live_room_stat_cost", 0) > 0:
        item["uni_live_room_roi"] = round(
            item.get("uni_live_room_gmv", 0) / item.get("uni_live_room_stat_cost", 0),
            2,
        )
    else:
        item["uni_live_room_roi"] = 0

    # 全域商品ROI
    if item.get("uni_product_stat_cost", 0) > 0:
        item["uni_product_roi"] = round(
            item.get("uni_product_gmv", 0) / item.get("uni_product_stat_cost", 0),
            2,
        )
    else:
        item["uni_product_roi"] = 0

    # 品专ROI
    if item.get("brand_stat_cost", 0) > 0:
        item["brand_roi"] = round(
            item.get("brand_gmv", 0) / item.get("brand_stat_cost", 0), 2
        )
    else:
        item["brand_roi"] = 0

    # 图文ROI
    if item.get("standard_product_stat_cost", 0) > 0:
        item["standard_product_roi"] = round(
            item.get("standard_product_gmv", 0)
            / item.get("standard_product_stat_cost", 0),
            2,
        )
    else:
        item["standard_product_roi"] = 0

    # KOC ROI
    if item.get("koc_stat_cost", 0) > 0:
        item["koc_roi"] = round(
            item.get("koc_gmv", 0) / item.get("koc_stat_cost", 0), 2
        )
    else:
        item["koc_roi"] = 0

    # 自营总ROI
    if item.get("self_total_cost", 0) > 0:
        item["self_total_roi"] = round(
            item.get("self_total_amt", 0) / item.get("self_total_cost", 0), 2
        )
    else:
        item["self_total_roi"] = 0

    # 直播间ROI
    for live_room in item.get("live_room", []):
        if live_room.get("stat_cost", 0) > 0:
            live_room["roi"] = round(
                live_room.get("gmv", 0) / live_room.get("stat_cost", 0), 2
            )
        else:
            live_room["roi"] = 0


# 已授权的店铺ID（向后兼容）
authorized_store_id = Constants.AUTHORIZED_STORE_IDS


@retry(max_retries=Constants.MAX_RETRIES, delay=Constants.RETRY_DELAY)
@RateLimiterDecorator(qps=Constants.QPS_LIMIT)
def sum_alliance_order(
    shop_id: str,
    start_time: int,
    end_time: int,
) -> AllianceOrderSumResult:
    """统计联盟订单

    Args:
        shop_id: 店铺ID
        start_time: 开始时间戳
        end_time: 结束时间戳

    Returns:
        联盟订单汇总结果
    """
    if shop_id not in Constants.AUTHORIZED_STORE_IDS:
        return AllianceOrderSumResult()

    try:
        accessToken = AccessTokenBuilder().buildTokenByShopId(shop_id)
        if not accessToken.isSuccess():
            raise Exception(
                f"获取 accessToken 失败: {accessToken.accessTokenResp.msg} - {accessToken.accessTokenResp.sub_msg}"
            )

        next_start_time = datetime.fromtimestamp(start_time).strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        req_end_time = datetime.fromtimestamp(end_time).strftime("%Y-%m-%d %H:%M:%S")
        next_start_index = 0
        result = AllianceOrderSumResult()

        while True:
            req = OrderGetSettleBillDetailV3Request()
            req.params.start_index = next_start_index
            req.params.size = Constants.DEFAULT_PAGE_SIZE
            req.params.start_time = next_start_time
            req.params.end_time = req_end_time

            resp = req.execute(accessToken)
            if not resp.isSuccess():
                raise Exception(f"获取联盟订单失败: {resp.msg} - {resp.sub_msg}")

            for order in resp.data.get("data", []):
                result.commission += abs(order.get("commission", 0))

            if resp.data.get("is_end", 0) == 1:
                break

            next_start_index = resp.data.get("next_start_index", 0)
            next_start_time = resp.data.get("next_start_time", 0)

        return result

    except Exception as e:
        logger.error(f"统计联盟订单失败: shop_id={shop_id}, error={e}")
        raise


@retry(max_retries=Constants.MAX_RETRIES, delay=Constants.RETRY_DELAY)
@RateLimiterDecorator(qps=Constants.QPS_LIMIT)
def sum_doudian_order(
    shop_id: str,
    start_time: int,
    end_time: int,
    page: Optional[int] = None,
) -> DoudianOrderSumResult:
    """获取平台承担金额

    Args:
        shop_id: 店铺ID
        start_time: 开始时间戳
        end_time: 结束时间戳
        page: 页码，None表示获取所有页

    Returns:
        抖店订单汇总结果
    """
    if shop_id not in Constants.AUTHORIZED_STORE_IDS:
        return DoudianOrderSumResult()

    try:
        accessToken = AccessTokenBuilder().buildTokenByShopId(shop_id)
        if not accessToken.isSuccess():
            raise Exception(
                f"获取 accessToken 失败: {accessToken.accessTokenResp.msg} - {accessToken.accessTokenResp.sub_msg}"
            )

        req = OrderSearchListRequest()
        req.params.create_time_start = int(start_time)
        req.params.create_time_end = int(end_time)
        req.params.page = page if page is not None else 1
        req.params.size = Constants.DEFAULT_PAGE_SIZE

        resp = req.execute(accessToken)
        if not resp.isSuccess():
            raise Exception(f"获取平台承担金额失败: {resp.msg} - {resp.sub_msg}")

        result = DoudianOrderSumResult()
        for order in resp.data.get("shop_order_list", []):
            result.only_platform_cost_amount += order.get(
                "only_platform_cost_amount", 0
            )
            result.promotion_talent_amount += order.get("promotion_talent_amount", 0)

        # 如果指定了页码，只返回当前页结果
        if page is not None:
            return result

        # 多线程获取剩余页面，限制并发数以避免触发限流
        total_count = resp.data.get("total", 0)
        total_page = total_count // Constants.DEFAULT_PAGE_SIZE

        if total_page > 1:
            # 限制并发数为2，避免触发API限流
            with ThreadPoolExecutor(max_workers=2) as executor:
                futures = [
                    executor.submit(
                        sum_doudian_order, shop_id, start_time, end_time, page
                    )
                    for page in range(2, total_page + 1)
                ]

                for future in futures:
                    future_result = future.result()
                    result = result + future_result

        return result

    except Exception as e:
        logger.error(f"获取平台承担金额失败: shop_id={shop_id}, error={e}")
        raise


async def _main():
    from app.core.logger import logger

    logger.info("开始执行统计数据")

    logger.info("开始执行每日数据统计")
    await executeStatisticsDailyData(
        start_time=dateutil.parser.parse("2025-04-22").timestamp(),
        end_time=dateutil.parser.parse("2025-06-09").timestamp(),
    )
    logger.info("开始执行周数据统计")
    await executeStatisticsData(
        type="weekly",
        start_date=dateutil.parser.parse("2025-04-22").replace(
            hour=0, minute=0, second=0, microsecond=0
        ),
        end_date=dateutil.parser.parse("2025-06-09").replace(
            hour=23, minute=59, second=59, microsecond=999999
        ),
    )
    logger.info("开始执行月数据统计")
    await executeStatisticsData(
        type="monthly",
        start_date=dateutil.parser.parse("2025-04-22").replace(
            hour=0, minute=0, second=0, microsecond=0
        ),
        end_date=dateutil.parser.parse("2025-06-09").replace(
            hour=23, minute=59, second=59, microsecond=999999
        ),
    )
    logger.info("统计数据执行完毕")


if __name__ == "__main__":
    asyncio.run(_main())

    # start_time = dateutil.parser.parse("2025-01-01").timestamp()
    # end_time = int(
    #     datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).timestamp()
    # )
    # while start_time < end_time:
    #     start_time_str = datetime.fromtimestamp(start_time).strftime("%Y-%m-%d")
    #     end_time_str = datetime.fromtimestamp(start_time + 86400 - 1).strftime(
    #         "%Y-%m-%d"
    #     )
    #     platform_subsidy = get_platform_subsidy(start_time, start_time + 86400 - 1)
    #     print(f"{start_time_str} - {end_time_str}: {platform_subsidy}")
    #     start_time += 86400
