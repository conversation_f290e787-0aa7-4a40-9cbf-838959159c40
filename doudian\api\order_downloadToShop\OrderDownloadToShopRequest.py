# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_downloadToShop.param.OrderDownloadToShopParam import OrderDownloadToShopParam


class OrderDownloadToShopRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderDownloadToShopParam()

	def getUrlPath(self, ):
		return "/order/downloadToShop"

	def getParams(self, ):
		return self.params



