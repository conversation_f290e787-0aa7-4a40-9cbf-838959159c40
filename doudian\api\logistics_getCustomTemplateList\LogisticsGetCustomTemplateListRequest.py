# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_getCustomTemplateList.param.LogisticsGetCustomTemplateListParam import LogisticsGetCustomTemplateListParam


class LogisticsGetCustomTemplateListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsGetCustomTemplateListParam()

	def getUrlPath(self, ):
		return "/logistics/getCustomTemplateList"

	def getParams(self, ):
		return self.params



