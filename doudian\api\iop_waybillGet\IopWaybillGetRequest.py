# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.iop_waybillGet.param.IopWaybillGetParam import IopWaybillGetParam


class IopWaybillGetRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = IopWaybillGetParam()

	def getUrlPath(self, ):
		return "/iop/waybillGet"

	def getParams(self, ):
		return self.params



