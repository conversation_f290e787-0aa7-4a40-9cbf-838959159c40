# 小红书 Redis 缓存系统文档

## 概述

本文档介绍了小红书 API 访问令牌管理的 Redis 缓存系统，包括自动令牌刷新、分布式锁定和降级机制。该系统现已支持缓存和读取 `approval_advertisers` 字段。

## 主要功能

### 1. 完整令牌信息缓存
- 缓存 `access_token` 和 `approval_advertisers` 字段
- 支持自动令牌刷新
- 分布式锁防止并发刷新
- Redis 连接失败时的优雅降级

### 2. 向后兼容性
- 提供便捷函数保持向后兼容
- 支持获取单独的 `access_token` 或 `approval_advertisers`

## API 函数

### 类型定义

系统提供了完整的类型提示支持：

```python
from typing import TypedDict, List

class AdvertiserInfo(TypedDict):
    """授权广告主信息结构"""
    advertiser_id: int
    advertiser_name: str

class TokenInfo(TypedDict):
    """访问令牌信息结构"""
    access_token: str
    approval_advertisers: List[AdvertiserInfo]

# 类型别名
ApprovalAdvertisersList = List[AdvertiserInfo]
```

### `get_access_token()` - 主要函数

获取完整的令牌信息，包括 access_token 和 approval_advertisers。

```python
async def get_access_token() -> TokenInfo | None:
    """
    获取访问令牌（带 Redis 缓存和分布式锁，自动刷新和降级处理）
    
    Returns:
        TokenInfo: 包含 access_token 和 approval_advertisers 的字典，格式为:
                   {
                       "access_token": str,
                       "approval_advertisers": List[AdvertiserInfo]
                   }
                   失败时返回 None
    """
```

**返回示例:**
```python
{
    "access_token": "your_access_token_here",
    "approval_advertisers": [
        {
            "advertiser_id": 1234,
            "advertiser_name": "品牌测试账号222"
        },
        {
            "advertiser_id": 5678,
            "advertiser_name": "另一个测试账号"
        }
    ]
}
```

### `get_access_token_only()` - 向后兼容函数

仅获取 access_token 字符串，保持向后兼容性。

```python
async def get_access_token_only() -> str | None:
    """
    获取访问令牌（仅返回 access_token 字符串，向后兼容）
    
    Returns:
        str: access_token 字符串，失败时返回 None
    """
```

### `get_approval_advertisers()` - 获取授权广告主

获取授权广告主列表。

```python
async def get_approval_advertisers() -> ApprovalAdvertisersList:
    """
    获取授权广告主列表
    
    Returns:
        ApprovalAdvertisersList: approval_advertisers 列表，失败时返回空列表
    """
```

## 使用示例

### 基本用法

```python
import asyncio
from app.services.providers.xiaohongshu import get_access_token, TokenInfo, AdvertiserInfo

async def main():
    # 获取完整令牌信息 - 带类型提示
    token_info: TokenInfo | None = await get_access_token()
    if token_info:
        access_token: str = token_info["access_token"]
        approval_advertisers: List[AdvertiserInfo] = token_info["approval_advertisers"]
        
        print(f"Access Token: {access_token}")
        
        # 遍历授权广告主
        for advertiser in approval_advertisers:
            advertiser_id: int = advertiser["advertiser_id"]
            advertiser_name: str = advertiser["advertiser_name"]
            print(f"广告主: {advertiser_name} (ID: {advertiser_id})")
    else:
        print("获取令牌失败")

asyncio.run(main())
```

### 向后兼容用法

```python
from app.services.providers.xiaohongshu import get_access_token_only

async def legacy_usage():
    # 仅获取 access_token（向后兼容）
    access_token = await get_access_token_only()
    if access_token:
        # 使用 access_token 调用 API
        campaign_result = get_campaign_list(access_token, params)
```

### 获取授权广告主

```python
from app.services.providers.xiaohongshu import get_approval_advertisers, ApprovalAdvertisersList

async def get_advertisers():
    advertisers: ApprovalAdvertisersList = await get_approval_advertisers()
    for advertiser in advertisers:
        advertiser_id: int = advertiser["advertiser_id"]
        advertiser_name: str = advertiser["advertiser_name"]
        print(f"Advertiser: {advertiser_name} (ID: {advertiser_id})")
```

## 缓存机制

### Redis 缓存键
- **缓存键**: `DATAPILOT:XIAOHONGSHU:TOKEN_INFO`
- **锁键**: `DATAPILOT:XIAOHONGSHU:TOKEN_INFO:LOCK`

### 缓存过期策略
- 缓存过期时间 = 令牌剩余有效期 - 120秒
- 最小缓存时间: 60秒
- 在令牌过期前 5分钟自动刷新

### 降级机制
1. **Redis 连接失败**: 直接查询数据库
2. **分布式锁失败**: 降级为直接数据库查询
3. **令牌刷新失败**: 返回 None 并记录错误日志

## 数据库结构

### access_tokens 集合

```javascript
{
    "_id": ObjectId("..."),
    "type": "xiaohongshu",
    "access_token": "your_access_token",
    "expire_time": 1234567890,
    "refresh_token": "your_refresh_token", 
    "refresh_token_expires_time": 1234567890,
    "approval_advertisers": [123456, 789012, 345678],
    "created_at": 1234567890
}
```

### approval_advertisers 字段

```javascript
{
    "_id": ObjectId("..."),
    "type": "xiaohongshu",
    "access_token": "your_access_token",
    "expire_time": 1234567890,
    "refresh_token": "your_refresh_token", 
    "refresh_token_expires_time": 1234567890,
    "approval_advertisers": [
        {
            "advertiser_id": 1234,
            "advertiser_name": "品牌测试账号222"
        },
        {
            "advertiser_id": 5678,
            "advertiser_name": "另一个测试账号"
        }
    ],
    "created_at": 1234567890
}
```

### 字段说明
- `approval_advertisers`: 授权广告主列表，每个元素包含 `advertiser_id` 和 `advertiser_name`
- 刷新令牌时保留现有的 `approval_advertisers`
- 如果 API 返回新的 `approval_advertisers`，则更新该字段

## 错误处理

### Redis 相关错误
- Redis 连接失败时自动降级到数据库查询
- 缓存写入失败不影响正常功能

### 令牌相关错误
- 令牌过期自动刷新
- 刷新令牌过期时记录错误并要求重新登录
- 刷新失败时返回详细错误信息

## 测试

运行测试脚本来验证功能：

```bash
python test_xhs_caching.py
```

运行类型提示演示：

```bash
python examples/type_hints_demo.py
```

测试包括：
1. 完整令牌信息的获取
2. 向后兼容函数的正确性
3. 数据一致性验证
4. 错误处理机制
5. 类型安全验证

## 类型检查

推荐使用 mypy 进行静态类型检查：

```bash
pip install mypy
mypy app/services/providers/xiaohongshu.py
```

## 配置要求

确保在 `app/core/config.py` 中配置了以下环境变量：

```python
XHS_APP_ID = "your_app_id"
XHS_APP_SECRET = "your_app_secret"
```

## 注意事项

1. **并发安全**: 使用分布式锁确保并发环境下的令牌刷新安全
2. **向后兼容**: 保持现有代码的兼容性，无需修改已有调用
3. **优雅降级**: Redis 不可用时仍能正常工作
4. **性能优化**: 减少数据库查询，提高 API 响应速度
5. **错误监控**: 详细的错误日志便于问题排查
