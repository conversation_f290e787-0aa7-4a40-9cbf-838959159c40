# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_batchUploadVideoAsync.param.MaterialBatchUploadVideoAsyncParam import MaterialBatchUploadVideoAsyncParam


class MaterialBatchUploadVideoAsyncRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialBatchUploadVideoAsyncParam()

	def getUrlPath(self, ):
		return "/material/batchUploadVideoAsync"

	def getParams(self, ):
		return self.params



