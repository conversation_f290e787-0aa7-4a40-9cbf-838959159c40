import asyncio
import base64
from datetime import datetime
import hashlib
import hmac
import re
import time
import urllib
from fastapi import Query, Request
import httpx
from pydantic import BaseModel, Field
from app.core.base_router import BaseRouter
from app.schemas.response import ResponseModel
from app.models.sms import (
    SMSTasksResponse,
    SMSTaskResponse,
    SendResultRequest,
    TaskProcessedRequest,
)
from app.core.database import db
from app.core.config import settings
from app.core.logger import logger

router = BaseRouter()


def get_tag(content: str):
    """
    获取短信标签
    格式:【标签】
    """
    result = re.search(r"【(\w+)】", content)
    if result is None:
        return None
    return result.group(1)


def get_captcha(content: str):
    """
    获取验证码
    格式: 验证码123456 、 验证码：123456
    """
    result = re.search(r"验证码(为)?([：|:])?\s*(\d{4,6})", content)
    if result is None:
        return None
    return result.group(3)


def get_mobile(content: str):
    """
    提取手机号码，忽略国家代码前缀
    支持格式: +8613800138000, +1234567890, 13800138000
    """
    # 匹配带有国家代码的手机号码（+XX或XX开头）
    result = re.search(r"(?:\+\d{1,4}|86)?(\d{11})", content)
    if result is None:
        # 如果没有匹配到11位数字，尝试匹配其他长度的手机号
        result = re.search(r"(?:\+\d{1,4})?(\d{10,15})", content)
        if result is None:
            return content
    return result.group(1)


class ReceiveRequest(BaseModel):
    mobile: str = Field(description="手机号")
    from_number: str | None = Field(default=None, description="发送号码")
    content: str = Field(description="短信内容")


class CreateTaskRequest(BaseModel):
    mobile: str = Field(description="设备号码")
    to_number: str = Field(description="目标手机号")
    content: str = Field(description="短信内容")


@router.post("/receive", summary="接收短信", response_model=ResponseModel)
async def receive(request: ReceiveRequest):

    collection = db.rpa_data.sms
    data = {
        "mobile": get_mobile(request.mobile),
        "from_number": request.from_number,
        "tag": get_tag(request.content),
        "content": request.content,
        "captcha": get_captcha(request.content),
        "receive_time": datetime.now(),
    }
    await collection.insert_one(data)
    text = f"**手机号**: {data['mobile']}"
    if request.from_number:
        text += f"\n\n**发送号码**: {request.from_number}"
    if data["tag"]:
        text += f"\n\n**标签**: {data['tag']}"
    text += f"\n\n**内容**: {request.content}"
    if data["captcha"]:
        text += f"\n\n**验证码**: {data['captcha']}"
    text += f"\n\n**时间**: {data['receive_time'].strftime('%Y-%m-%d %H:%M:%S')}"
    body = {
        "msgtype": "markdown",
        "markdown": {
            "title": "短信通知",
            "text": text,
        },
    }
    await send_dingtalk(body)
    await send_sms_to_callback_center(
        {
            "mobile": data["mobile"],
            "tag": data["tag"],
            "content": data["content"],
            "captcha": data["captcha"],
            "receive_time": data["receive_time"],
        }
    )
    return router.success()


async def send_sms_to_callback_center(data: dict):
    try:
        url = "https://callback-center.kfch.cn/sms/callback"
        # 将datetime对象转换为ISO格式字符串
        data["receive_time"] = data["receive_time"].isoformat()
        async with httpx.AsyncClient() as client:
            await client.post(
                url,
                json=data,
                headers={
                    "Content-Type": "application/json",
                    "x-api-key": settings.API_KEY,
                },
            )
    except Exception as e:
        logger.error(f"发送短信回调失败: {e}")


@router.get("/captcha", summary="获取验证码", response_model=ResponseModel)
async def captcha(
    mobile: str = Query(description="手机号"), tag: str = Query(description="标签")
):
    collection = db.rpa_data.sms
    # get the latest captcha
    result = await collection.find_one(
        {"mobile": mobile, "tag": tag}, sort=[("receive_time", -1)]
    )
    if result is None:
        return router.error(message="未收到短信")
    return router.success(
        {"captcha": result["captcha"], "time": result["receive_time"]}
    )


@router.post("/tasks", summary="创建SMS任务", response_model=ResponseModel)
async def create_task(request: CreateTaskRequest):
    """
    创建SMS发送任务

    Args:
        request: 包含设备号码、目标手机号和短信内容的请求

    Returns:
        创建结果
    """
    try:
        import uuid

        collection = db.rpa_data.sms_tasks

        # 处理手机号码，忽略国家代码前缀
        mobile = get_mobile(request.mobile)
        to_number = get_mobile(request.to_number)

        task_data = {
            "id": f"task_{uuid.uuid4().hex[:8]}",
            "mobile": mobile,
            "to_number": to_number,
            "content": request.content,
            "status": "pending",
            "created_time": datetime.now(),
            "sent_time": None,
            "retry_count": 0,
            "max_retry": 3,
        }

        await collection.insert_one(task_data)
        logger.info(f"创建SMS任务成功: {task_data['id']}")

        return router.success({"task_id": task_data["id"], "message": "任务创建成功"})

    except Exception as e:
        logger.error(f"创建SMS任务失败: {e}")
        return router.error(message="创建任务失败")


@router.get(
    "/tasks", summary="轮询任务接口", response_model=ResponseModel[SMSTasksResponse]
)
async def get_tasks(mobile: str = Query(description="设备号码")):
    """
    轮询任务接口
    根据设备号码获取待处理的SMS任务

    Args:
        mobile: 设备号码

    Returns:
        包含任务列表的响应，格式为:
        {
            "tasks": [
                {
                    "id": "task_123",
                    "to_number": "13800138000",
                    "content": "短信内容"
                }
            ]
        }
    """
    try:
        collection = db.rpa_data.sms_tasks

        # 处理手机号码，忽略国家代码前缀
        mobile = get_mobile(mobile)

        # 查询该设备的待处理任务，按创建时间排序
        cursor = collection.find(
            {"mobile": mobile, "status": "pending"}, {"_id": 0}  # 排除MongoDB的_id字段
        ).sort(
            "created_time", 1
        )  # 按创建时间升序排列

        tasks = []
        async for task in cursor:
            task_response = SMSTaskResponse(
                id=task.get("id", str(task.get("_id", ""))),
                to_number=task.get("to_number", ""),
                content=task.get("content", ""),
            )
            tasks.append(task_response)

        response_data = SMSTasksResponse(tasks=tasks)
        return router.success(response_data.dict())

    except Exception as e:
        logger.error(f"获取SMS任务失败: {e}")
        return router.error(message="获取任务失败")


@router.post("/send_result", summary="发送结果上报接口", response_model=ResponseModel)
async def send_result(request: SendResultRequest):
    """
    发送结果上报接口
    设备发送短信后上报发送结果

    Args:
        request: 包含设备号码、目标号码、短信内容、发送状态和时间戳的请求

    Returns:
        上报结果
    """
    try:
        if request.status not in ["success", "failed"]:
            return router.error(message="无效的状态值，必须是 success 或 failed")

        collection = db.rpa_data.sms_send_results

        # 处理手机号码，忽略国家代码前缀
        mobile = get_mobile(request.mobile)
        to_number = get_mobile(request.to_number)

        # 记录发送结果
        result_data = {
            "mobile": mobile,
            "to_number": to_number,
            "content": request.content,
            "status": request.status,
            "timestamp": request.timestamp,
            "report_time": datetime.now(),
        }

        await collection.insert_one(result_data)

        logger.info(
            f"SMS发送结果上报成功: {mobile} -> {to_number}, 状态: {request.status}"
        )

        # 可以在这里添加钉钉通知或其他回调逻辑
        if request.status == "failed":
            # 发送失败通知
            text = f"**短信发送失败**\n\n**设备号码**: {mobile}\n\n**目标号码**: {to_number}\n\n**内容**: {request.content}\n\n**时间**: {datetime.fromtimestamp(request.timestamp).strftime('%Y-%m-%d %H:%M:%S')}"
            body = {
                "msgtype": "markdown",
                "markdown": {
                    "title": "短信发送失败通知",
                    "text": text,
                },
            }
            await send_dingtalk(body)

        return router.success({"message": "发送结果上报成功"})

    except Exception as e:
        logger.error(f"发送结果上报失败: {e}")
        return router.error(message="发送结果上报失败")


@router.post(
    "/task_processed", summary="任务完成标记接口", response_model=ResponseModel
)
async def task_processed(request: TaskProcessedRequest):
    """
    任务完成标记接口
    设备处理完任务后标记任务为已处理

    Args:
        request: 包含设备号码、任务ID和时间戳的请求

    Returns:
        标记结果
    """
    try:
        collection = db.rpa_data.sms_tasks

        # 处理手机号码，忽略国家代码前缀
        mobile = get_mobile(request.mobile)

        # 更新任务状态为已处理
        update_data = {
            "status": "processed",
            "processed_time": datetime.now(),
            "processed_timestamp": request.timestamp,
        }

        result = await collection.update_one(
            {"id": request.task_id, "mobile": mobile}, {"$set": update_data}
        )

        if result.matched_count == 0:
            return router.error(message="任务不存在或设备号码不匹配")

        logger.info(f"任务标记为已处理: {request.task_id} by {mobile}")

        return router.success({"message": "任务标记成功"})

    except Exception as e:
        logger.error(f"任务标记失败: {e}")
        return router.error(message="任务标记失败")


robot_token = "6f6d0d085890341aa01a30615e7c42bb5f70446c11e3bd705c8d9f422d13b77a"
robot_secret = "SEC64971a9249d1636430d3e3ccf1af7c9518e989a6a58ab3842e4fed763daf6b8e"


async def send_dingtalk(data: dict):
    timestamp = str(int(time.time() * 1000))
    sign = hmac.new(
        robot_secret.encode("utf-8"),
        f"{timestamp}\n{robot_secret}".encode("utf-8"),
        hashlib.sha256,
    ).digest()
    sign = base64.b64encode(sign).decode("utf-8")
    sign = urllib.parse.quote_plus(sign)
    base_url = "https://oapi.dingtalk.com/robot/send"
    params = {"access_token": robot_token, "timestamp": timestamp, "sign": sign}
    query_string = urllib.parse.urlencode(params)
    url = f"{base_url}?{query_string}"
    async with httpx.AsyncClient() as client:
        await client.post(url, json=data)


if __name__ == "__main__":
    content = "【小红书】您的验证码是: 626801，5分钟内有效。请勿向他人泄漏。如非本人操作，可忽略本消息。"
    print(get_captcha(content))
