#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新小红书笔记流量推广费服务

通过调用小红书笔记离线报表API获取消费金额数据，
并更新BrandXhsNote表中对应笔记的流量推广费字段。
"""

import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Tuple
from decimal import Decimal
import functools
from collections import defaultdict

from sqlalchemy import select, update
from sqlalchemy.orm import Session

from app.core.logger import logger
from app.models import BrandXhsNote, get_session

# 从libs中导入小红书API功能
from libs.xiaohongshu import (
    get_access_token,
    get_note_offline_report,
    GetNoteOfflineReportParams,
)


# 错误类型枚举
class ErrorType:
    DATABASE_ERROR = "database_error"
    VALIDATION_ERROR = "validation_error"
    CONNECTION_ERROR = "connection_error"
    TIMEOUT_ERROR = "timeout_error"
    RECORD_NOT_FOUND = "record_not_found"  # 记录不存在
    UNKNOWN_ERROR = "unknown_error"


class ErrorStats:
    """错误统计类"""

    def __init__(self):
        self.error_details = defaultdict(list)  # {error_type: [error_messages]}
        self.note_errors = {}  # {note_id: error_info}

    def add_error(
        self,
        note_id: str,
        error_type: str,
        error_message: str,
        exception: Exception = None,
    ):
        """添加错误记录"""
        error_info = {
            "note_id": note_id,
            "error_type": error_type,
            "error_message": error_message,
            "exception_type": type(exception).__name__ if exception else "Unknown",
            "exception_str": str(exception) if exception else error_message,
            "timestamp": datetime.now().isoformat(),
        }

        self.error_details[error_type].append(error_info)
        self.note_errors[note_id] = error_info

    def get_summary(self) -> Dict:
        """获取错误摘要"""
        summary = {
            "total_errors": len(self.note_errors),
            "error_types": {},
            "top_errors": [],
        }

        # 按错误类型统计
        for error_type, errors in self.error_details.items():
            summary["error_types"][error_type] = len(errors)

        # 获取最常见的错误消息
        error_msg_count = defaultdict(int)
        for errors in self.error_details.values():
            for error in errors:
                error_msg_count[error["error_message"]] += 1

        # 排序获取前10个最常见的错误
        top_errors = sorted(error_msg_count.items(), key=lambda x: x[1], reverse=True)[
            :10
        ]
        summary["top_errors"] = [
            {"message": msg, "count": count} for msg, count in top_errors
        ]

        return summary

    def print_detailed_report(self):
        """打印详细的错误报告"""
        if not self.note_errors:
            logger.info("✅ 没有发生错误")
            return

        logger.error(f"❌ 总计 {len(self.note_errors)} 条错误记录")

        # 按错误类型分组显示
        for error_type, errors in self.error_details.items():
            logger.error(f"\n📊 {error_type} ({len(errors)} 条):")

            # 统计相同错误消息的数量
            msg_count = defaultdict(int)
            for error in errors:
                msg_count[error["error_message"]] += 1

            # 显示错误消息统计
            for msg, count in sorted(
                msg_count.items(), key=lambda x: x[1], reverse=True
            ):
                logger.error(f"  - {msg}: {count} 次")

        # 显示前几个具体的错误示例
        logger.error(f"\n🔍 错误详情示例（前5条）:")
        for i, (note_id, error_info) in enumerate(list(self.note_errors.items())[:5]):
            logger.error(f"  {i+1}. 笔记ID: {note_id}")
            logger.error(f"     错误类型: {error_info['error_type']}")
            logger.error(f"     错误消息: {error_info['error_message']}")
            logger.error(f"     异常类型: {error_info['exception_type']}")
            logger.error(f"     完整异常: {error_info['exception_str']}")
            logger.error(f"     发生时间: {error_info['timestamp']}")
            logger.error("")


def classify_error(exception: Exception) -> str:
    """根据异常类型分类错误"""
    exception_name = type(exception).__name__.lower()
    error_str = str(exception).lower()

    if "connection" in error_str or "network" in error_str:
        return ErrorType.CONNECTION_ERROR
    elif "timeout" in error_str:
        return ErrorType.TIMEOUT_ERROR
    elif (
        "integrity" in error_str or "foreign key" in error_str or "unique" in error_str
    ):
        return ErrorType.DATABASE_ERROR
    elif (
        "validation" in error_str or "invalid" in error_str or "constraint" in error_str
    ):
        return ErrorType.VALIDATION_ERROR
    elif "sqlalchemy" in exception_name or "database" in error_str:
        return ErrorType.DATABASE_ERROR
    else:
        return ErrorType.UNKNOWN_ERROR


async def async_get_note_offline_report(
    access_token: str, params: GetNoteOfflineReportParams
) -> dict:
    """
    异步包装的小红书笔记离线报表API调用

    Args:
        access_token: 访问令牌
        params: 查询参数

    Returns:
        dict: API响应结果
    """
    loop = asyncio.get_event_loop()
    # 在线程池中运行同步的API调用
    return await loop.run_in_executor(
        None, functools.partial(get_note_offline_report, access_token, params)
    )


async def fetch_advertiser_data(
    access_token: str,
    advertiser: dict,
    start_date: str,
    end_date: str,
    time_unit: str,
    semaphore: asyncio.Semaphore,
) -> Tuple[str, Dict[str, float], int]:
    """
    获取单个广告主的所有笔记数据

    Args:
        access_token: 访问令牌
        advertiser: 广告主信息
        start_date: 开始日期
        end_date: 结束日期
        time_unit: 时间维度
        semaphore: 并发控制信号量

    Returns:
        Tuple[str, Dict[str, float], int]: (广告主名称, 笔记费用数据, 总记录数)
    """
    async with semaphore:
        advertiser_id = advertiser["advertiser_id"]
        advertiser_name = advertiser.get("advertiser_name", "未知")

        logger.info(f"开始处理广告主：{advertiser_name} (ID: {advertiser_id})")

        note_cost_data = {}  # {note_id: total_fee}
        total_notes = 0
        page_num = 1
        page_size = 500  # 最大分页大小

        while True:
            try:
                # 设置查询参数
                params = GetNoteOfflineReportParams(
                    advertiser_id=advertiser_id,
                    start_date=start_date,
                    end_date=end_date,
                    time_unit=time_unit,
                    page_num=page_num,
                    page_size=page_size,
                )

                # 异步调用API获取笔记离线报表数据
                result = await async_get_note_offline_report(access_token, params)

                if not result.get("success"):
                    logger.error(
                        f"广告主 {advertiser_name} 获取第 {page_num} 页数据失败：{result.get('msg', '未知错误')}"
                    )
                    break

                data = result.get("data", {})
                data_list = data.get("data_list", [])
                total_count = data.get("total_count", 0)

                logger.info(
                    f"广告主 {advertiser_name} 第 {page_num} 页：获取到 {len(data_list)} 条记录，总记录数：{total_count}"
                )

                if not data_list:
                    break

                # 处理当前页的数据
                for item in data_list:
                    note_id = item.get("note_id")
                    fee_str = item.get("fee", "0")

                    if not note_id:
                        continue

                    try:
                        # 转换费用为浮点数
                        fee = float(fee_str) if fee_str else 0.0

                        # 累加该笔记的消费金额
                        if note_id in note_cost_data:
                            note_cost_data[note_id] += fee
                        else:
                            note_cost_data[note_id] = fee

                    except (ValueError, TypeError) as e:
                        logger.warning(
                            f"笔记 {note_id} 的费用数据转换失败：{fee_str}，错误：{e}"
                        )
                        continue

                total_notes += len(data_list)

                # 如果当前页数据量小于页面大小，说明已经是最后一页
                if len(data_list) < page_size:
                    break

                page_num += 1

            except Exception as e:
                logger.error(
                    f"广告主 {advertiser_name} 获取第 {page_num} 页数据时发生错误：{e}"
                )
                break

        logger.info(
            f"广告主 {advertiser_name} 处理完成，获取 {total_notes} 条记录，涉及 {len(note_cost_data)} 个唯一笔记"
        )

        return advertiser_name, note_cost_data, total_notes


async def update_single_note_with_retry(
    note_id: str,
    cost_flow: Decimal,
    max_retries: int = 3,
    error_stats: ErrorStats = None,
) -> Tuple[bool, str]:
    """
    带重试机制的无锁方式更新单条笔记记录

    Args:
        note_id: 笔记ID
        cost_flow: 流量推广费
        max_retries: 最大重试次数
        error_stats: 错误统计对象

    Returns:
        Tuple[bool, str]: (更新是否成功, 结果描述)
            - (True, "success"): 更新成功
            - (False, "not_found"): 记录不存在
            - (False, "error"): 发生异常错误
    """
    for attempt in range(max_retries + 1):
        try:
            async with get_session() as session:
                stmt = (
                    update(BrandXhsNote)
                    .where(BrandXhsNote.note_id == note_id)
                    .values(cost_flow=cost_flow)
                )

                result = await session.execute(stmt)
                await session.commit()

                if result.rowcount > 0:
                    return True, "success"
                else:
                    # 记录不存在，不需要重试
                    if error_stats:
                        error_stats.add_error(
                            note_id,
                            ErrorType.RECORD_NOT_FOUND,
                            f"数据库中找不到笔记ID为 {note_id} 的记录",
                        )
                    return False, "not_found"

        except Exception as e:
            error_type = classify_error(e)

            if attempt < max_retries:
                # 指数退避重试
                wait_time = 0.1 * (2**attempt)
                logger.debug(
                    f"笔记 {note_id} 更新失败，{wait_time:.1f}秒后重试 ({attempt + 1}/{max_retries}): {e}"
                )
                await asyncio.sleep(wait_time)
                continue
            else:
                # 记录最终失败的错误
                if error_stats:
                    error_stats.add_error(
                        note_id, error_type, f"更新失败，已达最大重试次数: {str(e)}", e
                    )

                logger.warning(f"笔记 {note_id} 更新失败，已达最大重试次数：{e}")
                return False, "error"

    return False, "error"


async def batch_update_database(
    note_cost_data: Dict[str, float],
    max_concurrent_updates: int = 50,
    enable_progress: bool = True,
) -> Tuple[int, int, int, ErrorStats]:
    """
    无锁方式批量更新数据库中的笔记流量推广费

    Args:
        note_cost_data: 笔记费用数据字典 {note_id: total_fee}
        max_concurrent_updates: 最大并发更新数量
        enable_progress: 是否启用进度监控

    Returns:
        Tuple[int, int, int, ErrorStats]: (成功更新数量, 记录不存在数量, 异常错误数量, 错误统计)
    """
    error_stats = ErrorStats()

    if not note_cost_data:
        return 0, 0, 0, error_stats

    total_count = len(note_cost_data)
    logger.info(
        f"开始无锁方式批量更新 {total_count} 条笔记数据，最大并发数：{max_concurrent_updates}"
    )

    # 准备所有更新任务
    update_tasks = []

    for note_id, total_fee in note_cost_data.items():
        try:
            cost_flow = round(Decimal(str(total_fee)), 2)
            task = update_single_note_with_retry(
                note_id, cost_flow, error_stats=error_stats
            )
            update_tasks.append((note_id, task))
        except Exception as e:
            error_type = classify_error(e)
            error_stats.add_error(
                note_id, error_type, f"准备更新任务时出错: {str(e)}", e
            )
            logger.warning(f"准备笔记 {note_id} 更新任务时出错：{e}")
            continue

    if not update_tasks:
        logger.warning("没有有效的更新任务")
        return 0, 0, 0, error_stats

    # 控制并发数量
    semaphore = asyncio.Semaphore(max_concurrent_updates)

    # 进度监控
    completed_count = 0
    success_count = 0
    not_found_count = 0
    error_count = 0

    async def semaphore_update_with_progress(
        note_id: str, task
    ) -> Tuple[str, bool, str]:
        """带信号量控制和进度监控的更新任务"""
        nonlocal completed_count, success_count, not_found_count, error_count

        async with semaphore:
            try:
                update_success, result_type = await task

                # 更新进度
                completed_count += 1
                if update_success:
                    success_count += 1
                elif result_type == "not_found":
                    not_found_count += 1
                else:  # result_type == "error"
                    error_count += 1

                # 进度日志（每完成10%或每1000条记录报告一次）
                if enable_progress and (
                    completed_count % max(1, total_count // 10) == 0
                    or completed_count % 1000 == 0
                    or completed_count == len(update_tasks)
                ):
                    progress_pct = (completed_count / len(update_tasks)) * 100
                    logger.info(
                        f"更新进度：{completed_count}/{len(update_tasks)} ({progress_pct:.1f}%) - "
                        f"成功：{success_count}，记录不存在：{not_found_count}，异常错误：{error_count}"
                    )

                return note_id, update_success, result_type

            except Exception as e:
                completed_count += 1
                error_count += 1
                error_type = classify_error(e)
                error_stats.add_error(
                    note_id, error_type, f"更新任务执行异常: {str(e)}", e
                )
                logger.error(f"笔记 {note_id} 更新任务执行异常：{e}")
                return note_id, False, "error"

    # 执行所有更新任务
    logger.info(f"开始并发执行 {len(update_tasks)} 个更新任务")

    try:
        # 使用 asyncio.gather 并发执行所有更新
        controlled_tasks = [
            semaphore_update_with_progress(note_id, task)
            for note_id, task in update_tasks
        ]

        results = await asyncio.gather(*controlled_tasks, return_exceptions=True)

        # 最终统计（防止并发计数有误差）
        final_success_count = 0
        final_not_found_count = 0
        final_error_count = 0

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                note_id = update_tasks[i][0]
                error_type = classify_error(result)
                error_stats.add_error(
                    note_id, error_type, f"更新任务执行异常: {str(result)}", result
                )
                logger.error(f"笔记 {note_id} 更新任务执行异常：{result}")
                final_error_count += 1
                continue

            note_id, update_success, result_type = result
            if update_success:
                final_success_count += 1
            elif result_type == "not_found":
                final_not_found_count += 1
            else:
                final_error_count += 1

        logger.info(
            f"无锁批量更新完成 - 成功：{final_success_count}，记录不存在：{final_not_found_count}，异常错误：{final_error_count}"
        )
        return (
            final_success_count,
            final_not_found_count,
            final_error_count,
            error_stats,
        )

    except Exception as e:
        logger.error(f"无锁批量更新过程中发生异常：{e}")
        return success_count, not_found_count, error_count, error_stats


async def update_note_cost_flow_from_report(
    start_date: str = "2020-01-01",
    end_date: str = None,
    time_unit: str = "SUMMARY",
    max_concurrent_advertisers: int = 3,
    max_concurrent_db_updates: int = 50,
    enable_db_progress: bool = True,
) -> Dict[str, int]:
    """
    通过小红书笔记离线报表数据更新笔记流量推广费（异步并发优化版 + 无锁数据库更新）

    Args:
        start_date: 开始日期，格式 "YYYY-MM-DD"，默认从2020-01-01开始
        end_date: 结束日期，格式 "YYYY-MM-DD"，默认到昨天
        time_unit: 时间维度，默认为"SUMMARY"（汇总）
        max_concurrent_advertisers: 最大并发广告主数量，默认3个
        max_concurrent_db_updates: 最大并发数据库更新数量，默认50个
        enable_db_progress: 是否启用数据库更新进度监控，默认True

    Returns:
        Dict[str, int]: 包含处理结果的字典
            - success_count: 成功更新的笔记数量
            - error_count: 处理失败的笔记数量
            - total_notes: 从API获取到的笔记总数
            - unique_notes: 唯一笔记数量
            - error_details: 详细错误信息
    """

    # 如果没有指定结束日期，使用昨天
    if end_date is None:
        end_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

    logger.info(
        f"开始异步并发更新笔记流量推广费，查询时间范围：{start_date} 到 {end_date}"
    )
    logger.info(f"API并发配置 - 最大并发广告主数量：{max_concurrent_advertisers}")
    logger.info(
        f"数据库并发配置 - 最大并发更新数量：{max_concurrent_db_updates}，进度监控：{enable_db_progress}"
    )

    try:
        # 1. 获取访问令牌
        logger.info("正在获取小红书API访问令牌...")
        token_info = await get_access_token()
        if not token_info:
            logger.error("获取访问令牌失败")
            return {
                "success_count": 0,
                "error_count": 0,
                "total_notes": 0,
                "unique_notes": 0,
                "error_details": {},
            }

        access_token = token_info["access_token"]
        approval_advertisers = token_info["approval_advertisers"]

        if not approval_advertisers:
            logger.error("没有授权的广告主")
            return {
                "success_count": 0,
                "error_count": 0,
                "total_notes": 0,
                "unique_notes": 0,
                "error_details": {},
            }

        logger.info(f"成功获取访问令牌，找到 {len(approval_advertisers)} 个授权广告主")

        # 2. 创建并发控制信号量
        semaphore = asyncio.Semaphore(max_concurrent_advertisers)

        # 3. 并发获取所有广告主的数据
        logger.info("开始并发获取广告主数据...")
        tasks = [
            fetch_advertiser_data(
                access_token, advertiser, start_date, end_date, time_unit, semaphore
            )
            for advertiser in approval_advertisers
        ]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 4. 汇总所有广告主的数据
        all_note_cost_data = {}  # {note_id: total_fee}
        total_notes_from_api = 0

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                advertiser_name = approval_advertisers[i].get("advertiser_name", "未知")
                logger.error(f"广告主 {advertiser_name} 处理失败：{result}")
                continue

            advertiser_name, note_cost_data, total_notes = result
            total_notes_from_api += total_notes

            # 合并笔记数据，如果同一笔记在多个广告主中出现，累加费用
            for note_id, fee in note_cost_data.items():
                if note_id in all_note_cost_data:
                    all_note_cost_data[note_id] += fee
                else:
                    all_note_cost_data[note_id] = fee

        logger.info(
            f"并发获取完成，共获得 {total_notes_from_api} 条记录，涉及 {len(all_note_cost_data)} 个唯一笔记"
        )

        # 5. 无锁批量更新数据库
        success_count = 0
        not_found_count = 0
        error_count = 0
        error_stats = ErrorStats()

        if all_note_cost_data:
            logger.info("开始无锁批量更新数据库...")
            success_count, not_found_count, error_count, error_stats = (
                await batch_update_database(
                    all_note_cost_data,
                    max_concurrent_updates=max_concurrent_db_updates,
                    enable_progress=enable_db_progress,
                )
            )
        else:
            logger.warning("没有获取到任何笔记数据")

        # 打印详细的错误报告
        total_failed = not_found_count + error_count
        if total_failed > 0:
            logger.error(f"\n{'='*60}")
            logger.error("🔍 详细错误分析报告")
            logger.error(f"{'='*60}")
            if not_found_count > 0:
                logger.error(
                    f"📝 记录不存在：{not_found_count} 条（数据库中找不到对应的笔记ID）"
                )
            if error_count > 0:
                logger.error(f"❌ 异常错误：{error_count} 条（真正的异常错误）")
                error_stats.print_detailed_report()
            logger.error(f"{'='*60}")

        result_summary = {
            "success_count": success_count,
            "error_count": total_failed,  # 保持兼容性，总失败数 = 记录不存在 + 异常错误
            "not_found_count": not_found_count,
            "exception_error_count": error_count,
            "total_notes": total_notes_from_api,
            "unique_notes": len(all_note_cost_data),
            "error_details": error_stats.get_summary(),
        }

        # 格式化输出结果摘要
        logger.info(f"\n{'='*60}")
        logger.info("📊 异步并发更新完成 - 执行结果摘要")
        logger.info(f"{'='*60}")
        logger.info(f"✅ 成功更新笔记数量：{success_count:,} 条")
        logger.info(f"📝 记录不存在数量：{not_found_count:,} 条")
        logger.info(f"❌ 异常错误数量：{error_count:,} 条")
        logger.info(f"📈 总失败数量：{total_failed:,} 条")
        logger.info(f"📊 API获取总记录：{total_notes_from_api:,} 条")
        logger.info(f"🔢 涉及唯一笔记：{len(all_note_cost_data):,} 个")

        if len(all_note_cost_data) > 0:
            success_rate = (success_count / len(all_note_cost_data)) * 100
            logger.info(f"📈 更新成功率：{success_rate:.1f}%")

        logger.info(f"{'='*60}")

        return result_summary

    except Exception as e:
        logger.error(f"异步并发更新笔记流量推广费时发生异常：{e}")
        return {
            "success_count": 0,
            "error_count": 0,
            "total_notes": 0,
            "unique_notes": 0,
            "error_details": {},
        }


async def main():
    """
    主函数，用于测试功能（展示无锁数据库更新配置）
    """
    # 获取昨天的日期
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

    print(f"开始更新笔记流量推广费（查询时间：2020-01-01 到 {yesterday}）")
    print("使用无锁数据库更新模式...")

    result = await update_note_cost_flow_from_report(
        start_date="2020-01-01",
        end_date=yesterday,
        time_unit="SUMMARY",  # 使用汇总模式获取总计数据
        max_concurrent_advertisers=3,  # API并发：最多3个广告主同时处理
        max_concurrent_db_updates=100,  # 数据库并发：最多100个更新同时执行
        enable_db_progress=True,  # 启用数据库更新进度监控
    )

    # 格式化显示更新结果
    print(f"\n{'='*60}")
    print("🎯 主函数执行完成 - 最终结果摘要")
    print(f"{'='*60}")

    if result["success_count"] > 0:
        print(f"✅ 成功更新了 {result['success_count']:,} 条笔记的流量推广费")

    if result.get("not_found_count", 0) > 0:
        print(f"📝 有 {result['not_found_count']:,} 条记录在数据库中找不到对应的笔记ID")
        print(f"   这通常意味着这些笔记尚未同步到 BrandXhsNote 表中")

    if result.get("exception_error_count", 0) > 0:
        print(f"❌ 有 {result['exception_error_count']:,} 条记录更新时发生异常错误")

    print(
        f"📊 从API获取了 {result['total_notes']:,} 条数据，涉及 {result.get('unique_notes', 0):,} 个唯一笔记"
    )

    # 显示错误详情摘要
    error_details = result.get("error_details", {})
    if error_details.get("total_errors", 0) > 0:
        print(f"\n🔍 错误类型分布：")
        for error_type, count in error_details.get("error_types", {}).items():
            error_type_cn = {
                "record_not_found": "记录不存在",
                "database_error": "数据库错误",
                "connection_error": "连接错误",
                "timeout_error": "超时错误",
                "validation_error": "验证错误",
                "unknown_error": "未知错误",
            }.get(error_type, error_type)
            print(f"  - {error_type_cn}: {count} 条")

    print(f"{'='*60}")


if __name__ == "__main__":
    asyncio.run(main())
