from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class SMSTask(BaseModel):
    """SMS任务模型"""
    id: str = Field(description="任务ID")
    mobile: str = Field(description="设备号码")
    to_number: str = Field(description="目标手机号")
    content: str = Field(description="短信内容")
    status: str = Field(default="pending", description="任务状态: pending, sent, failed")
    created_time: datetime = Field(default_factory=datetime.now, description="创建时间")
    sent_time: Optional[datetime] = Field(default=None, description="发送时间")
    retry_count: int = Field(default=0, description="重试次数")
    max_retry: int = Field(default=3, description="最大重试次数")


class SMSTaskResponse(BaseModel):
    """SMS任务响应模型"""
    id: str = Field(description="任务ID")
    to_number: str = Field(description="目标手机号")
    content: str = Field(description="短信内容")


class SMSTasksResponse(BaseModel):
    """SMS任务列表响应模型"""
    tasks: list[SMSTaskResponse] = Field(description="任务列表")


class SendResultRequest(BaseModel):
    """发送结果上报请求模型"""
    mobile: str = Field(description="设备号码")
    to_number: str = Field(description="目标号码")
    content: str = Field(description="短信内容")
    status: str = Field(description="发送状态: success|failed")
    timestamp: int = Field(description="时间戳")


class TaskProcessedRequest(BaseModel):
    """任务完成标记请求模型"""
    mobile: str = Field(description="设备号码")
    task_id: str = Field(description="任务ID")
    timestamp: int = Field(description="时间戳")