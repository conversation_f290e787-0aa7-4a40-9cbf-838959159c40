# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.iop_orderList.param.IopOrderListParam import IopOrderListParam


class IopOrderListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = IopOrderListParam()

	def getUrlPath(self, ):
		return "/iop/orderList"

	def getParams(self, ):
		return self.params



