# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_batchDecrypt.param.OrderBatchDecryptParam import OrderBatchDecryptParam


class OrderBatchDecryptRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderBatchDecryptParam()

	def getUrlPath(self, ):
		return "/order/batchDecrypt"

	def getParams(self, ):
		return self.params



