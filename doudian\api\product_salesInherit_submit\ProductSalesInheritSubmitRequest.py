# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_salesInherit_submit.param.ProductSalesInheritSubmitParam import ProductSalesInheritSubmitParam


class ProductSalesInheritSubmitRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductSalesInheritSubmitParam()

	def getUrlPath(self, ):
		return "/product/salesInherit/submit"

	def getParams(self, ):
		return self.params



