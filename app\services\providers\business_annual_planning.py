import asyncio
from datetime import datetime, timedelta

from sqlalchemy import func, select, update

from app.core.oa_database import (
    BusinessAnnualPlanning,
    PddDailyReport,
    PddMall,
    ProductOrderSalesItemShopSum,
    ShopPerformance,
    get_sync_session,
)
from dateutil.relativedelta import relativedelta


def update_business_annual_planning(
    target_date: datetime | None = None,
):
    if target_date is None:
        target_date = datetime.now() - relativedelta(months=1)
    if target_date.year < 2020:
        raise ValueError("target_year must be greater than 2020")
    if target_date.month < 1 or target_date.month > 12:
        raise ValueError("target_month must be between 1 and 12")

    # 上个月
    target_date = target_date
    start_date = datetime(target_date.year, target_date.month, 1)
    end_date = start_date + relativedelta(months=1)
    target_year = target_date.year
    target_month = target_date.month
    with get_sync_session() as session:
        plan_list = (
            session.execute(
                select(BusinessAnnualPlanning).where(
                    BusinessAnnualPlanning.year == target_year,
                    BusinessAnnualPlanning.month == target_month,
                )
            )
            .scalars()
            .all()
        )
        shop_code_list = [plan.shop_code for plan in plan_list]
        shop_query = select(PddMall).where(PddMall.gyCode.in_(shop_code_list))
        shops = session.execute(shop_query).scalars().all()
        shop_id_list = [shop.mallId for shop in shops]
        shop_code_map = {shop.mallId: shop.gyCode for shop in shops}
        # sum_list = (
        #     session.execute(
        #         select(
        #             ProductOrderSalesItemShopSum.shop_code,
        #             func.sum(ProductOrderSalesItemShopSum.after_amount).label(
        #                 "after_amount"
        #             ),
        #         )
        #         .where(
        #             ProductOrderSalesItemShopSum.date_year == target_year,
        #             ProductOrderSalesItemShopSum.date_month == target_month,
        #             ProductOrderSalesItemShopSum.shop_code.in_(shop_code_list),
        #         )
        #         .group_by(
        #             ProductOrderSalesItemShopSum.shop_code,
        #         )
        #     )
        # ).all()

        sum_list = session.execute(
            select(
                ShopPerformance.code.label("shop_code"),
                func.sum(ShopPerformance.total).label("after_amount"),
            )
            .where(
                ShopPerformance.code.in_(shop_code_list),
                ShopPerformance.year == str(target_year),
                ShopPerformance.month == f"{target_month:02d}",
            )
            .group_by(ShopPerformance.code)
        ).all()

        sum_dict = {item.shop_code: item.after_amount for item in sum_list}
        updates = []

        # 推广费用
        promotion_fee_list = session.execute(
            select(
                PddDailyReport.mallId,
                func.sum(PddDailyReport.spend).label("spend"),
            )
            .where(
                PddDailyReport.mallId.in_(shop_id_list),
                PddDailyReport.date >= start_date,
                PddDailyReport.date < end_date,
            )
            .group_by(PddDailyReport.mallId)
        ).all()
        promotion_fee_dict = {
            shop_code_map[item.mallId]: item.spend for item in promotion_fee_list
        }

        for plan in plan_list:
            shop_code = plan.shop_code
            sales_amount = sum_dict.get(shop_code, 0)
            promotion_cost_rate_actual = promotion_fee_dict.get(shop_code, 0)
            updates.append(
                {
                    "id": plan.id,
                    "shop_sales_volume_actual": sales_amount,
                    "promotion_cost_rate_actual": (
                        promotion_cost_rate_actual / sales_amount
                        if sales_amount != 0
                        else 0
                    ),
                }
            )

        session.bulk_update_mappings(BusinessAnnualPlanning, updates)
        session.commit()


if __name__ == "__main__":
    update_business_annual_planning(datetime(2025, 1, 1))
