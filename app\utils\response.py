from typing import Any, Optional
from app.schemas.response import ResponseModel


def success_response(data: Any = None, message: str = None) -> ResponseModel:
    """
    生成成功响应
    """
    return ResponseModel(code=200, msg=message, data=data)


def error_response(
    message: str = "error", code: int = 500, data: Any = None
) -> ResponseModel:
    """
    生成错误响应
    """
    return ResponseModel(code=code, msg=message, data=data)
