# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.coupons_abandon.param.CouponsAbandonParam import CouponsAbandonParam


class CouponsAbandonRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = CouponsAbandonParam()

	def getUrlPath(self, ):
		return "/coupons/abandon"

	def getParams(self, ):
		return self.params



