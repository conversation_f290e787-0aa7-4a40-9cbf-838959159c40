from typing import Any
from fastapi import APIRouter
from app.utils.response import success_response, error_response


class BaseRouter(APIRouter):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def success(self, data: Any = None, message: str = None):
        return success_response(data=data, message=message)

    def error(self, message: str = "error", code: int = 500, data: Any = None):
        return error_response(message=message, code=code, data=data)
