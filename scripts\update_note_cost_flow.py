#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新小红书笔记流量推广费 - 运行脚本

快速运行脚本，用于更新笔记的流量推广费数据。
查询时间范围：2020-01-01 到昨天的汇总金额
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

from app.services.update_note_cost_flow import update_note_cost_flow_from_report


async def main():
    """
    主函数：更新笔记流量推广费
    """
    print("🚀 开始更新小红书笔记流量推广费")
    print("=" * 60)

    # 获取昨天的日期
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    start_date = "2020-01-01"

    print(f"📅 查询时间范围：{start_date} 到 {yesterday}")
    print(f"⏰ 开始时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    try:
        # 执行更新
        result = await update_note_cost_flow_from_report(
            start_date=start_date,
            end_date=yesterday,
            time_unit="SUMMARY",  # 使用汇总模式
        )

        print()
        print("📊 更新结果统计：")
        print("-" * 40)
        print(f"✅ 成功更新笔记数量：{result['success_count']}")
        print(f"❌ 更新失败笔记数量：{result['error_count']}")
        print(f"📄 API获取记录总数：{result['total_notes']}")
        print(f"🔍 涉及唯一笔记数量：{result.get('unique_notes', 0)}")

        # 根据结果显示相应信息
        if result["success_count"] > 0:
            print(
                f"\n🎉 更新完成！成功更新了 {result['success_count']} 条笔记的流量推广费"
            )

        if result["error_count"] > 0:
            print(f"\n⚠️  注意：有 {result['error_count']} 条记录更新失败，请检查日志")

        if result["success_count"] == 0 and result["error_count"] == 0:
            print("\nℹ️  没有找到需要更新的数据")

    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        return 1

    except Exception as e:
        print(f"\n💥 执行过程中发生错误：{e}")
        return 1

    print(f"\n⏰ 结束时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
