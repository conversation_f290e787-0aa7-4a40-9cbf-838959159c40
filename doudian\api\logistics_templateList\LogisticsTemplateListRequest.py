# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_templateList.param.LogisticsTemplateListParam import LogisticsTemplateListParam


class LogisticsTemplateListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsTemplateListParam()

	def getUrlPath(self, ):
		return "/logistics/templateList"

	def getParams(self, ):
		return self.params



