# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.logistics_pushOnlineServiceConfig.param.LogisticsPushOnlineServiceConfigParam import LogisticsPushOnlineServiceConfigParam


class LogisticsPushOnlineServiceConfigRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = LogisticsPushOnlineServiceConfigParam()

	def getUrlPath(self, ):
		return "/logistics/pushOnlineServiceConfig"

	def getParams(self, ):
		return self.params



