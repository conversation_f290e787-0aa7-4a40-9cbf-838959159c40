# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_queryMaterialDetail.param.MaterialQueryMaterialDetailParam import MaterialQueryMaterialDetailParam


class MaterialQueryMaterialDetailRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialQueryMaterialDetailParam()

	def getUrlPath(self, ):
		return "/material/queryMaterialDetail"

	def getParams(self, ):
		return self.params



