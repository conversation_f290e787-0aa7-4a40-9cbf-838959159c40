# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_invoiceList.param.OrderInvoiceListParam import OrderInvoiceListParam


class OrderInvoiceListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderInvoiceListParam()

	def getUrlPath(self, ):
		return "/order/invoiceList"

	def getParams(self, ):
		return self.params



