# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.openCloud_ddpGetShopList.param.OpenCloudDdpGetShopListParam import OpenCloudDdpGetShopListParam


class OpenCloudDdpGetShopListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OpenCloudDdpGetShopListParam()

	def getUrlPath(self, ):
		return "/openCloud/ddpGetShopList"

	def getParams(self, ):
		return self.params



