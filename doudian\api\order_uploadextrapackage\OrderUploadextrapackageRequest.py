# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_uploadextrapackage.param.OrderUploadextrapackageParam import OrderUploadextrapackageParam


class OrderUploadextrapackageRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderUploadextrapackageParam()

	def getUrlPath(self, ):
		return "/order/uploadextrapackage"

	def getParams(self, ):
		return self.params



