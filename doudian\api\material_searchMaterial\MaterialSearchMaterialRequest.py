# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_searchMaterial.param.MaterialSearchMaterialParam import MaterialSearchMaterialParam


class MaterialSearchMaterialRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialSearchMaterialParam()

	def getUrlPath(self, ):
		return "/material/searchMaterial"

	def getParams(self, ):
		return self.params



