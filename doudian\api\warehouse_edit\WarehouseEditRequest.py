# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.warehouse_edit.param.WarehouseEditParam import WarehouseEditParam


class WarehouseEditRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = WarehouseEditParam()

	def getUrlPath(self, ):
		return "/warehouse/edit"

	def getParams(self, ):
		return self.params



