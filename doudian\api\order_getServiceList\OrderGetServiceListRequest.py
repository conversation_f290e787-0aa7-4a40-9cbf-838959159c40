# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_getServiceList.param.OrderGetServiceListParam import OrderGetServiceListParam


class OrderGetServiceListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderGetServiceListParam()

	def getUrlPath(self, ):
		return "/order/getServiceList"

	def getParams(self, ):
		return self.params



