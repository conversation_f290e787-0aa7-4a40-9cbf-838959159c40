# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.warehouse_create.param.WarehouseCreateParam import WarehouseCreateParam


class WarehouseCreateRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = WarehouseCreateParam()

	def getUrlPath(self, ):
		return "/warehouse/create"

	def getParams(self, ):
		return self.params



