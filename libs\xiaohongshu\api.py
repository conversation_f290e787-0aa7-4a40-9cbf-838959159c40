"""
小红书聚光平台 API 调用模块

提供所有API端点的调用封装，包括广告计划、创意、报表等功能。
"""

import dataclasses
import requests
from typing import Dict, Any

from .types import (
    GetCampaignListParams,
    CampaignListResponse,
    GetCreativitySearchParams,
    CreativitySearchResponse,
    GetCreativeOfflineReportParams,
    CreativeOfflineReportResponse,
    GetNoteOfflineReportParams,
    NoteOfflineReportResponse,
    SubAccountListResponse,
)
from .constants import BASE_URL, ENDPOINTS, DEFAULT_PAGE_CONFIG, TIME_UNITS


def _prepare_params_dict(params_obj: Any) -> Dict[str, Any]:
    """
    将dataclass参数对象转换为API请求字典

    Args:
        params_obj: dataclass参数对象

    Returns:
        Dict[str, Any]: 过滤None值后的参数字典
    """
    params_dict = {}
    for k, v in dataclasses.asdict(params_obj).items():
        if v is not None:
            # 特殊处理filters字段，因为它包含FilterClause对象列表
            if k == "filters" and v:
                # asdict后v已经是字典列表，直接使用
                params_dict[k] = v
            else:
                params_dict[k] = v
    return params_dict


def get_campaign_list(
    access_token: str, params: GetCampaignListParams
) -> CampaignListResponse:
    """
    获取广告计划列表

    Args:
        access_token: 访问令牌
        params: 查询参数

    Returns:
        CampaignListResponse: 广告计划列表响应数据

    Raises:
        requests.HTTPError: HTTP请求错误
    """
    url = f"{BASE_URL}{ENDPOINTS['campaign_list']}"
    headers = {"Access-Token": access_token}

    # 转换参数
    params_dict = _prepare_params_dict(params)

    # 设置默认分页参数
    if "page" not in params_dict:
        params_dict["page"] = {
            "page_index": DEFAULT_PAGE_CONFIG["page_num"],
            "page_size": DEFAULT_PAGE_CONFIG["page_size"],
        }

    response = requests.post(url, headers=headers, json=params_dict)

    if response.status_code == 200:
        return response.json()
    else:
        response.raise_for_status()


def get_creativity_search(
    access_token: str, params: GetCreativitySearchParams
) -> CreativitySearchResponse:
    """
    获取创意列表

    Args:
        access_token: 访问令牌
        params: 查询参数

    Returns:
        CreativitySearchResponse: 创意列表响应数据

    Raises:
        requests.HTTPError: HTTP请求错误
    """
    url = f"{BASE_URL}{ENDPOINTS['creativity_search']}"
    headers = {"Access-Token": access_token}

    # 转换参数
    params_dict = _prepare_params_dict(params)

    # 设置默认分页参数
    if "page" not in params_dict:
        params_dict["page"] = {
            "page_index": DEFAULT_PAGE_CONFIG["page_num"],
            "page_size": DEFAULT_PAGE_CONFIG["page_size"],
        }

    response = requests.post(url, headers=headers, json=params_dict)

    if response.status_code == 200:
        return response.json()
    else:
        response.raise_for_status()


def get_creative_offline_report(
    access_token: str, params: GetCreativeOfflineReportParams
) -> CreativeOfflineReportResponse:
    """
    获取创意离线报表数据

    Args:
        access_token: 访问令牌
        params: 查询参数

    Returns:
        CreativeOfflineReportResponse: 创意离线报表响应数据

    Raises:
        requests.HTTPError: HTTP请求错误
    """
    url = f"{BASE_URL}{ENDPOINTS['creative_offline_report']}"
    headers = {"Access-Token": access_token}

    # 转换参数
    params_dict = _prepare_params_dict(params)

    # 设置默认值
    if "time_unit" not in params_dict:
        params_dict["time_unit"] = TIME_UNITS["DAY"]
    if "page_num" not in params_dict:
        params_dict["page_num"] = DEFAULT_PAGE_CONFIG["page_num"]
    if "page_size" not in params_dict:
        params_dict["page_size"] = DEFAULT_PAGE_CONFIG["page_size"]

    response = requests.post(url, headers=headers, json=params_dict)

    if response.status_code == 200:
        return response.json()
    else:
        response.raise_for_status()


def get_note_offline_report(
    access_token: str, params: GetNoteOfflineReportParams
) -> NoteOfflineReportResponse:
    """
    获取笔记离线报表数据

    Args:
        access_token: 访问令牌
        params: 查询参数

    Returns:
        NoteOfflineReportResponse: 笔记离线报表响应数据

    Raises:
        requests.HTTPError: HTTP请求错误
    """
    url = f"{BASE_URL}{ENDPOINTS['note_offline_report']}"
    headers = {"Access-Token": access_token}

    # 转换参数
    params_dict = _prepare_params_dict(params)

    # 设置默认值
    if "time_unit" not in params_dict:
        params_dict["time_unit"] = TIME_UNITS["DAY"]
    if "page_num" not in params_dict:
        params_dict["page_num"] = DEFAULT_PAGE_CONFIG["page_num"]
    if "page_size" not in params_dict:
        params_dict["page_size"] = DEFAULT_PAGE_CONFIG["page_size"]

    response = requests.post(url, headers=headers, json=params_dict)

    if response.status_code == 200:
        return response.json()
    else:
        response.raise_for_status()


def get_sub_account_list(access_token: str) -> SubAccountListResponse:
    """
    获取子账号列表

    Args:
        access_token: 访问令牌

    Returns:
        SubAccountListResponse: 子账号列表响应数据

    Raises:
        requests.HTTPError: HTTP请求错误
    """
    url = f"{BASE_URL}{ENDPOINTS['sub_account_list']}"
    headers = {"Access-Token": access_token}
    params = {
        "user_id": "71701",
        "page": 1,
        "page_size": 500,
    }

    response = requests.post(url, headers=headers, json=params)

    if response.status_code == 200:
        return response.json()
    else:
        response.raise_for_status()


# 辅助函数
def validate_date_range(start_date: str, end_date: str) -> bool:
    """
    验证日期范围是否有效

    Args:
        start_date: 开始日期 YYYY-MM-DD
        end_date: 结束日期 YYYY-MM-DD

    Returns:
        bool: 日期范围是否有效
    """
    try:
        from datetime import datetime

        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
        today = datetime.now().date()

        # 检查日期格式和范围
        if start > end:
            return False

        # 结束日期不能是今天
        if end.date() >= today:
            return False

        return True
    except ValueError:
        return False


def build_pagination_params(page_num: int = 1, page_size: int = 20) -> Dict[str, int]:
    """
    构建分页参数

    Args:
        page_num: 页码，默认为1
        page_size: 页面大小，默认为20

    Returns:
        Dict[str, int]: 分页参数字典
    """
    # 限制页面大小
    page_size = min(page_size, DEFAULT_PAGE_CONFIG["max_page_size"])
    return {"page_num": page_num, "page_size": page_size}
