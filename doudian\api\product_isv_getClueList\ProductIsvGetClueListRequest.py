# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_isv_getClueList.param.ProductIsvGetClueListParam import ProductIsvGetClueListParam


class ProductIsvGetClueListRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductIsvGetClueListParam()

	def getUrlPath(self, ):
		return "/product/isv/getClueList"

	def getParams(self, ):
		return self.params



