# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.iop_sellerOrderInfo.param.IopSellerOrderInfoParam import IopSellerOrderInfoParam


class IopSellerOrderInfoRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = IopSellerOrderInfoParam()

	def getUrlPath(self, ):
		return "/iop/sellerOrderInfo"

	def getParams(self, ):
		return self.params



