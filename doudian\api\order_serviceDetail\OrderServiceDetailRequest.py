# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.order_serviceDetail.param.OrderServiceDetailParam import OrderServiceDetailParam


class OrderServiceDetailRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = OrderServiceDetailParam()

	def getUrlPath(self, ):
		return "/order/serviceDetail"

	def getParams(self, ):
		return self.params



