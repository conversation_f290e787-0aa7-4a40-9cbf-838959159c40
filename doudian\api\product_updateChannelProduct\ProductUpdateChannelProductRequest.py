# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_updateChannelProduct.param.ProductUpdateChannelProductParam import ProductUpdateChannelProductParam


class ProductUpdateChannelProductRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductUpdateChannelProductParam()

	def getUrlPath(self, ):
		return "/product/updateChannelProduct"

	def getParams(self, ):
		return self.params



