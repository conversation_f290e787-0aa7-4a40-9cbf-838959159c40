from sqlalchemy import (
    Column,
    String,
    DateTime,
    Integer,
    Numeric,
    UniqueConstraint,
    Index,
    func,
)
from sqlalchemy.orm import Mapped
from datetime import datetime
from .base import EmptyModel, Base


class BusinessAnnualPlanning(EmptyModel):
    """业务年度规划表"""

    __tablename__ = "business_annual_planning"

    id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    shop_code: Mapped[str] = Column(String(10), comment="店铺代码")
    status: Mapped[int] = Column(Integer, nullable=False, default=0, comment="状态")
    year: Mapped[int] = Column(Integer, comment="年份")
    month: Mapped[int] = Column(Integer, comment="月份")
    planner_dingtalk_id: Mapped[str] = Column(String(255), comment="计划制定人钉钉id")

    # 店铺销售额
    shop_sales_volume_goal: Mapped[float] = Column(
        Numeric(10, 2), comment="店铺销售额 目标"
    )
    shop_sales_volume_actual: Mapped[str] = Column(
        String(255), comment="店铺销售额 实际"
    )

    # 产品销售额
    product_sales_volume_goal: Mapped[float] = Column(
        Numeric(10, 2), comment="产品销售额 目标"
    )
    product_sales_volume_actual: Mapped[float] = Column(
        Numeric(10, 2), comment="产品销售额 实际"
    )

    # 商品成本占比
    product_cost_rate_goal: Mapped[float] = Column(
        Numeric(5, 2), comment="商品成本占比 目标"
    )
    product_cost_rate_actual: Mapped[float] = Column(
        Numeric(5, 2), comment="商品成本占比 实际"
    )

    # 物流成本占比
    logistics_cost_rate_goal: Mapped[float] = Column(
        Numeric(5, 2), comment="物流成本占比 目标"
    )
    logistics_cost_rate_actual: Mapped[float] = Column(
        Numeric(5, 2), comment="物流成本占比 实际"
    )

    # 推广费用占比
    promotion_cost_rate_goal: Mapped[float] = Column(
        Numeric(5, 2), comment="推广费用占比 目标"
    )
    promotion_cost_rate_actual: Mapped[float] = Column(
        Numeric(5, 2), comment="推广费用占比 实际"
    )

    # 营销费用占比
    marketing_cost_rate_goal: Mapped[float] = Column(
        Numeric(5, 2), comment="营销费用占比 目标"
    )
    marketing_cost_rate_actual: Mapped[float] = Column(
        Numeric(5, 2), comment="营销费用占比 实际"
    )

    # 平台费用占比
    platform_cost_rate_goal: Mapped[float] = Column(
        Numeric(5, 2), comment="平台扣点、服务费、办公占比 目标"
    )
    platform_cost_rate_actual: Mapped[float] = Column(
        Numeric(5, 2), comment="平台扣点、服务费、办公占比 实际"
    )

    # 人员成本占比
    staff_cost_rate_goal: Mapped[float] = Column(
        Numeric(5, 2), comment="人员成本占比 目标"
    )
    staff_cost_rate_actual: Mapped[float] = Column(
        Numeric(5, 2), comment="人员成本占比 实际"
    )

    # 税后利润率
    profit_rate_goal: Mapped[float] = Column(Numeric(5, 2), comment="税后利润率 目标")
    profit_rate_actual: Mapped[float] = Column(Numeric(5, 2), comment="税后利润率 实际")

    # 时间字段
    created_time: Mapped[datetime] = Column(
        DateTime, nullable=False, comment="创建时间"
    )
    updated_time: Mapped[datetime] = Column(
        DateTime, nullable=False, comment="更新时间"
    )
    deleted_time: Mapped[datetime] = Column(DateTime, comment="删除时间")

    __table_args__ = (
        UniqueConstraint("shop_code", "year", "month", name="uk_shop_year_month"),
        Index("idx_year_month", "year", "month"),
        Index("idx_status", "status"),
        Index("idx_planner_dingtalk_id", "planner_dingtalk_id"),
        Index("idx_created_time", "created_time"),
        Index("idx_updated_time", "updated_time"),
    )


class ShopPerformance(Base):
    """店铺业绩表"""

    __tablename__ = "shop_performance"

    id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    code: Mapped[str] = Column(String(32), nullable=False)
    name: Mapped[str] = Column(String(255), nullable=True)
    total: Mapped[float] = Column(Numeric(13, 2), nullable=False, default=0.00)
    year: Mapped[str] = Column(String(4), nullable=False)
    month: Mapped[str] = Column(String(2), nullable=False)
    day: Mapped[str] = Column(String(2), nullable=False)
    datetime: Mapped[int] = Column(Integer, nullable=False)
    create_time: Mapped[datetime] = Column(
        DateTime, nullable=True, onupdate=func.current_timestamp()
    )

    __table_args__ = {
        "mysql_engine": "InnoDB",
        "mysql_charset": "utf8mb4",
        "mysql_collate": "utf8mb4_unicode_ci",
        "mysql_row_format": "DYNAMIC",
    }
