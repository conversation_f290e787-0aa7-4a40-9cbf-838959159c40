import pytest
from unittest.mock import patch, MagicMock
from app.services.exceptions import ServiceConfigError
from app.services.providers.guanyi import GuanyiService
from app.core.config import get_settings


def test_init_service_with_invalid_config():
    """测试初始化服务时的配置验证"""
    with pytest.raises(ServiceConfigError):
        GuanyiService(appkey="", sessionkey="", secret="")


def test_generate_sign(guanyi_service, test_settings):
    """测试签名生成"""
    params = {
        "appkey": test_settings.GUANYI_APPKEY,
        "method": "test.method",
    }
    sign = guanyi_service._generate_sign(params)
    assert isinstance(sign, str)
    assert len(sign) == 32  # MD5哈希长度为32位


def test_prepare_params(guanyi_service, test_settings):
    """测试参数准备"""
    method = "test.method"
    biz_params = {"param1": "value1"}

    params = guanyi_service._prepare_params(method, biz_params)

    assert params["appkey"] == test_settings.GUANYI_APPKEY
    assert params["sessionkey"] == test_settings.GUANYI_SESSIONKEY
    assert params["method"] == method
    assert params["param1"] == "value1"
    assert "sign" in params


@pytest.mark.asyncio
async def test_get_shop_list(guanyi_service):
    """测试获取店铺列表"""
    mock_response = {
        "shops": [
            {"shop_id": "1", "shop_name": "测试店铺1"},
            {"shop_id": "2", "shop_name": "测试店铺2"},
        ]
    }

    with patch.object(guanyi_service, "post", return_value=mock_response):
        result = await guanyi_service.get_shop_list()
        assert result == mock_response


@pytest.mark.asyncio
async def test_get_trade_list(guanyi_service):
    """测试获取订单列表"""
    mock_response = {
        "trades": [
            {"trade_id": "1", "status": "WAIT_SELLER_SEND_GOODS"},
            {"trade_id": "2", "status": "WAIT_BUYER_CONFIRM_GOODS"},
        ],
        "total_count": 2,
    }

    with patch.object(guanyi_service, "post", return_value=mock_response):
        result = await guanyi_service.get_trade_list(page_no=1, page_size=10)
        assert result == mock_response


@pytest.mark.asyncio
async def test_get_inventory_list(guanyi_service):
    """测试获取库存列表"""
    mock_response = {
        "items": [
            {"item_code": "SKU001", "quantity": 100},
            {"item_code": "SKU002", "quantity": 200},
        ],
        "total_count": 2,
    }

    with patch.object(guanyi_service, "post", return_value=mock_response):
        result = await guanyi_service.get_inventory_list(page_no=1, page_size=10)
        assert result == mock_response


@pytest.fixture(autouse=True)
async def cleanup():
    """清理资源"""
    yield
    # 在每个测试后清理资源
