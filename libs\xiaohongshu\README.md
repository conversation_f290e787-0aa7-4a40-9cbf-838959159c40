# 小红书聚光平台 API 库

小红书聚光平台完整 API 接口库，提供类型安全的接口封装和认证管理。

## 功能特性

- 🔐 **认证管理** - 自动访问令牌获取、刷新、缓存
- 📊 **完整 API 封装** - 广告计划、创意、报表等所有接口
- 🎯 **类型安全** - 完整的 TypeScript 风格类型提示
- ⚡ **高性能缓存** - Redis 分布式锁和缓存机制
- 🛡️ **错误处理** - 完善的异常处理和降级策略
- 📄 **详细文档** - 完整的 API 文档和使用示例

## 模块结构

```
libs/xiaohongshu/
├── __init__.py          # 模块入口，导出所有公共接口
├── constants.py         # 常量定义（URL、端点、配置等）
├── types.py            # 类型定义（请求/响应结构）
├── auth.py             # 认证模块（令牌管理）
├── api.py              # API调用模块
└── README.md           # 本文档
```

## 快速开始

### 基础使用

```python
import asyncio
from libs.xiaohongshu import (
    get_access_token,
    get_note_offline_report,
    GetNoteOfflineReportParams
)

async def main():
    # 获取访问令牌
    token_info = await get_access_token()
    access_token = token_info["access_token"]

    # 构建查询参数
    params = GetNoteOfflineReportParams(
        advertiser_id=123456,
        start_date="2024-01-01",
        end_date="2024-01-07"
    )

    # 调用API
    result = get_note_offline_report(access_token, params)
    print(f"获取到 {len(result['data']['data_list'])} 条笔记数据")

asyncio.run(main())
```

### 认证模块使用

```python
from libs.xiaohongshu import (
    get_access_token,
    get_access_token_only,
    get_approval_advertisers
)

# 获取完整令牌信息（推荐）
token_info = await get_access_token()
if token_info:
    access_token = token_info["access_token"]
    advertisers = token_info["approval_advertisers"]

# 仅获取access_token（向后兼容）
access_token = await get_access_token_only()

# 获取授权广告主列表
advertisers = await get_approval_advertisers()
```

### API 调用示例

```python
from libs.xiaohongshu import (
    get_campaign_list,
    get_creativity_search,
    get_creative_offline_report,
    get_note_offline_report,
    GetCampaignListParams,
    GetCreativeOfflineReportParams,
    FilterClause
)

# 获取广告计划列表
campaign_params = GetCampaignListParams(
    advertiser_id=123456,
    status=6,  # 所有未删除状态
    page={"page_index": 1, "page_size": 20}
)
campaigns = get_campaign_list(access_token, campaign_params)

# 获取创意离线报表（带过滤条件）
filter_clause = FilterClause(
    column="fee",
    operator=">",
    values=["100"]
)

report_params = GetCreativeOfflineReportParams(
    advertiser_id=123456,
    start_date="2024-01-01",
    end_date="2024-01-07",
    filters=[filter_clause]
)
report = get_creative_offline_report(access_token, report_params)
```

## 类型系统

### 请求参数类型

- `GetCampaignListParams` - 广告计划查询参数
- `GetCreativitySearchParams` - 创意查询参数
- `GetCreativeOfflineReportParams` - 创意报表查询参数
- `GetNoteOfflineReportParams` - 笔记报表查询参数
- `FilterClause` - 过滤条件

### 响应数据类型

- `CampaignListResponse` - 广告计划列表响应
- `CreativitySearchResponse` - 创意列表响应
- `CreativeOfflineReportResponse` - 创意报表响应
- `NoteOfflineReportResponse` - 笔记报表响应

### 业务数据类型

- `TokenInfo` - 访问令牌信息
- `AdvertiserInfo` - 广告主信息
- `BaseCampaignDto` - 广告计划数据
- `CreativityDto` - 创意数据
- `CreativeReportData` - 创意报表数据
- `NoteReportData` - 笔记报表数据

## 常量配置

### API 端点

```python
from libs.xiaohongshu import BASE_URL, ENDPOINTS

print(BASE_URL)  # https://adapi.xiaohongshu.com
print(ENDPOINTS["note_offline_report"])  # /api/open/jg/data/report/offline/note
```

### 预定义常量

```python
from libs.xiaohongshu.constants import (
    TIME_UNITS,
    MARKETING_TARGETS,
    PLACEMENT_TYPES,
    FILTER_OPERATORS
)

# 时间维度
TIME_UNITS["DAY"]      # "DAY"
TIME_UNITS["SUMMARY"]  # "SUMMARY"

# 营销目标
MARKETING_TARGETS[3]   # "商品销量"
MARKETING_TARGETS[4]   # "产品种草"

# 过滤操作符
FILTER_OPERATORS["GREATER_THAN"]  # ">"
```

## 错误处理

### HTTP 错误

```python
import requests
from libs.xiaohongshu import get_note_offline_report

try:
    result = get_note_offline_report(access_token, params)
except requests.HTTPError as e:
    print(f"HTTP错误: {e}")
except Exception as e:
    print(f"其他错误: {e}")
```

### 认证错误

```python
from libs.xiaohongshu import get_access_token

token_info = await get_access_token()
if not token_info:
    print("获取访问令牌失败，请检查配置或网络连接")
```

## 依赖要求

### 主项目模块

此库依赖以下主项目模块（自动导入）：

- `app.core.database` - 数据库连接
- `app.utils.redis_utils` - Redis 工具
- `app.core.config` - 配置管理
- `app.core.logger` - 日志记录

### 第三方依赖

- `requests` - HTTP 请求
- `typing` - 类型提示

## 最佳实践

### 1. 错误处理

```python
async def safe_get_data():
    try:
        token_info = await get_access_token()
        if not token_info:
            return None

        result = get_note_offline_report(
            token_info["access_token"],
            params
        )

        if result.get("success"):
            return result["data"]
        else:
            print(f"API错误: {result.get('msg')}")
            return None

    except Exception as e:
        print(f"请求失败: {e}")
        return None
```

### 2. 分页处理

```python
async def get_all_data(advertiser_id):
    all_data = []
    page_num = 1

    while True:
        params = GetNoteOfflineReportParams(
            advertiser_id=advertiser_id,
            start_date="2024-01-01",
            end_date="2024-01-07",
            page_num=page_num,
            page_size=100
        )

        result = get_note_offline_report(access_token, params)

        if not result.get("success"):
            break

        data_list = result["data"]["data_list"]
        if not data_list:
            break

        all_data.extend(data_list)

        if len(data_list) < 100:  # 最后一页
            break

        page_num += 1

    return all_data
```

### 3. 类型提示

```python
from typing import List, Optional
from libs.xiaohongshu import NoteReportData, TokenInfo

async def process_note_data(
    advertiser_id: int,
    start_date: str,
    end_date: str
) -> Optional[List[NoteReportData]]:
    token_info: Optional[TokenInfo] = await get_access_token()

    if not token_info:
        return None

    # ... 处理逻辑
    return data_list
```

## 版本历史

- v1.0.0 - 初始版本，包含所有基础 API 接口
- 支持广告计划、创意、报表查询
- 完整的认证和缓存机制
- 类型安全的接口设计

## 许可证

Copyright © 2024 KOPHENIX DATAPILOT Team
