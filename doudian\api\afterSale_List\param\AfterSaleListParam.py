# auto generated file
class AfterSaleListParam:

	def __init__(self):
		self.order_id = None
		self.aftersale_type = None
		self.aftersale_status = None
		self.reason = None
		self.logistics_status = None
		self.pay_type = None
		self.refund_type = None
		self.arbitrate_status = None
		self.order_flag = None
		self.start_time = None
		self.end_time = None
		self.amount_start = None
		self.amount_end = None
		self.risk_flag = None
		self.order_by = None
		self.page = None
		self.size = None
		self.aftersale_id = None
		self.standard_aftersale_status = None
		self.need_special_type = None
		self.update_start_time = None
		self.update_end_time = None
		self.order_logistics_tracking_no = None
		self.order_logistics_state = None
		self.agree_refuse_sign = None
		self.aftersale_sub_type = None
		self.aftersale_status_to_final_start_time = None
		self.aftersale_status_to_final_end_time = None




