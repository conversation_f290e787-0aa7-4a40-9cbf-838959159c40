# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.product_isv_batchGetClueSubmitStatus.param.ProductIsvBatchGetClueSubmitStatusParam import ProductIsvBatchGetClueSubmitStatusParam


class ProductIsvBatchGetClueSubmitStatusRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = ProductIsvBatchGetClueSubmitStatusParam()

	def getUrlPath(self, ):
		return "/product/isv/batchGetClueSubmitStatus"

	def getParams(self, ):
		return self.params



