# 模型重构说明

## 重构目的

原来的 `app/core/oa_database.py` 文件包含了大量的数据库模型类（1200 多行代码），导致文件过于庞大，难以维护。本次重构将模型按业务领域拆分到多个文件中，提高代码的可维护性和可读性。

## 文件结构

```
app/models/
├── __init__.py          # 导入所有模型，提供统一接口
├── base.py              # 基础模型类和数据库连接
├── production.py        # 生产相关模型
├── guanyierp.py         # 管易ERP相关模型
├── pdd.py               # 拼多多相关模型
├── business.py          # 业务规划相关模型
├── brand_xhs.py         # 小红书品牌相关模型
├── order_sales.py       # 订单销售相关模型
└── README.md            # 本说明文件
```

## 模型分类

### base.py - 基础模型

- `EmptyModel` - 空模型基类
- `BaseModel` - 带时间戳的基础模型
- `Base` - 简单基础模型
- 数据库连接和会话管理功能

### production.py - 生产相关

- `ProductionBatch` - 生产批次
- `ProductionBatchAllocateRecord` - 生产批次分配记录

### guanyierp.py - 管易 ERP 相关

- `GuanyierpStockOtherInOrder` - 其他入库单
- `GuanyierpStockOtherInOrderDetail` - 其他入库单明细
- `GuanyierpPurchase` - 采购订单
- `GuanyierpPurchaseArrive` - 采购入库
- `GuanyierpPurchaseArriveDetail` - 采购入库明细
- `GuanyierpGoods` - 商品信息
- `GuanyierpGoods3` - 商品信息 3

### pdd.py - 拼多多相关

- `PddMall` - 店铺信息
- `PddDailyReport` - 每日报表
- `PddHourlyReport` - 每小时报表
- `PddTgsj` - 推广数据采集
- `PddJmsj` - 加密数据采集
- `PddTgspDetails` - 推广商品明细

### business.py - 业务规划相关

- `BusinessAnnualPlanning` - 业务年度规划
- `ShopPerformance` - 店铺业绩

### brand_xhs.py - 小红书品牌相关

- `BrandXhsJgCreativity` - 聚光创意
- `BrandXhsJgCreativityDataReport` - 聚光创意报表
- `BrandXhsJgOverallDataReport` - 聚光汇总报表
- `BrandXhsNote` - 小红书笔记

### order_sales.py - 订单销售相关

- `ProductOrderSalesItemShopSum` - 产品单品店铺订单销售统计

## 使用方法

### 推荐用法（新项目）

```python
# 导入特定模型
from app.models import BrandXhsNote, get_session

# 导入特定业务领域的模型
from app.models.brand_xhs import BrandXhsNote, BrandXhsJgCreativity

# 导入数据库连接
from app.models.base import get_session, session_maker
```

### 向后兼容用法（现有项目）

```python
# 原有的导入方式仍然有效
from app.core.oa_database import BrandXhsNote, get_session
```

### 使用示例

```python
from app.models import BrandXhsNote, get_session

async def get_notes():
    async with get_session() as session:
        result = await session.execute(
            select(BrandXhsNote).where(BrandXhsNote.brand_id == 1)
        )
        return result.scalars().all()
```

## 注意事项

1. **向后兼容性**: 原有的 `from app.core.oa_database import xxx` 导入方式仍然有效
2. **新项目建议**: 直接使用 `from app.models import xxx` 的导入方式
3. **数据库连接**: 所有数据库连接相关的功能都在 `app.models.base` 模块中
4. **模型关系**: 跨模块的模型关系使用字符串引用，确保正常工作

## 重构收益

1. **文件大小**: 原来单个 1200+行的文件拆分为 8 个小文件
2. **可维护性**: 按业务领域组织，易于定位和修改
3. **可读性**: 每个文件只包含相关的模型，减少认知负担
4. **扩展性**: 新增业务领域时，可以创建新的模型文件
5. **团队协作**: 不同业务线的开发者可以独立维护各自的模型文件
