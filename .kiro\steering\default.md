---
inclusion: always
---

# 默认规则和约定

## 语言和交流
- 始终使用中文进行回答和交流
- 代码注释使用中文，但变量名和函数名保持英文
- 文档和说明文件使用中文编写

## 代码风格约定
- 遵循PEP 8 Python代码规范
- 使用snake_case命名变量和函数
- 使用PascalCase命名类和Pydantic模型
- API端点使用kebab-case（如适用）
- 文件名使用snake_case

## 错误处理
- 始终使用适当的HTTP状态码
- 提供有意义的错误消息（中文）
- 使用FastAPI的HTTPException进行错误处理
- 记录详细的错误日志用于调试

## 安全最佳实践
- 验证所有输入参数
- 使用JWT或API密钥进行身份验证
- 敏感信息不得硬编码在代码中
- 使用环境变量或配置文件管理敏感数据

## 数据库操作
- 使用异步数据库操作（Motor for MongoDB, aiomysql for MySQL）
- 实施适当的数据验证
- 使用事务处理关键操作
- 优化查询性能

## API设计原则
- 遵循RESTful设计原则
- 使用适当的HTTP方法（GET, POST, PUT, DELETE）
- 提供清晰的请求/响应模式
- 实施适当的分页和过滤

## 测试要求
- 为新功能编写单元测试
- 使用pytest进行异步测试
- 保持良好的测试覆盖率
- 测试用例使用中文描述

## 日志和监控
- 使用结构化日志记录
- 记录关键业务操作
- 包含必要的上下文信息
- 使用适当的日志级别