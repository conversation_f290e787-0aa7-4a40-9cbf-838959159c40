# auto generated file
from doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from doudian.api.material_deleteMaterial.param.MaterialDeleteMaterialParam import MaterialDeleteMaterialParam


class MaterialDeleteMaterialRequest(DoudianOpApiRequest):

	def __init__(self):
		DoudianOpApiRequest.__init__(self)
		self.params = MaterialDeleteMaterialParam()

	def getUrlPath(self, ):
		return "/material/deleteMaterial"

	def getParams(self, ):
		return self.params



